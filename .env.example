#  dev- https://fastpassapp-server.azurewebsites.net  Prod- https://fastpass-server.azurewebsites.net
VITE_BACKEND_URL = "Your_Twilio_Backend_URL" 
## Azure Key Vault Details ##
VITE_KEY_VAULT_NAME=##################
AZURE_TENANT_ID=###########################
AZURE_CLIENT_ID=############################
AZURE_CLIENT_SECRET=########################
## END ##
VITE_BACKEND_URL = ##################
VITE_INTERCOM_NOACCOUNT_APP_ID = ##################
VITE_INTERCOM_NOLOANACCOUNT_APP_ID = ##################
VITE_GOOGLEMAP_API_KEY = ##################
VITE_OTP_TIMER_VALUE = ##################
VITE_ASSETS_BASE_URL = ##################
# provide this variable only
# working locally
VITE_LOCAL_BACKEND_AI_URL = ##################