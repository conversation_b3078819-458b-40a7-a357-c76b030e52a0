# Release v5.1.2 on 2025-04-02 

    ~ Enhancement : Change monthly payment and downpayment calculation in command center
# Release v5.1.1 on 2025-03-28 

    ~ Fix : Monthly payment and downpayment calculation issue fix
    ~ Fix: Added Global Popup Context for Chat
    ~ Fix: Redirection to FastPass from Carbots VDP screen
    ~ Enhancement : Merge KYBP page with command center and do the changes for storing user click event
    ~ Enhancement : Command Center UI design change
    ~ Enhancement : Command center new calculation and change the existing logic
    ~ Enhancement : Chat For Member 2 & 3
# Release v5.1.0 on 2025-03-21 

    ~ Fix : KYBP Page open issue in 4k devices
# Release v5.0.0 on 2025-03-12 

    ~ Enhancement : Store user login and logout event store in database
    ~ Fix : NPM audit issue fix
    ~ Fix: Toggle UI added for VIN Search
    ~ Fix: AI Search UI implementation
    ~ Fix : Test drive message HTML code rendering issue fix
    ~ Enhancement : API Integration and UI Development for User and Advisor chat functionality
    ~ Ehancement : Chat Selection Modal Design
    ~ Enhancement : Change command center calculation to same as KYBP page
    ~ Enhancement : Add Loan Question button in the landing page
    ~ Enhancement : Design user and CU chat page
    ~ Enhancement : API Integration in cu and user chat
    ~ Enhancement : Add Env Variable to hide show generic search functionality in the App.
    ~ Enhancement : Create a script to store page and component meta data at the time of build.
    ~ Enhancement : Store user activity in button click for landing page, left & right swipe and view details in inventory page and Garage Page click and ask dealer button
    ~ Enhancement : Capture user click on every page opens and store that data in database
    ~ Enhancement : User total page clicks activity store
    ~ Fix : Handle Salesforce API Error in chat details page
    ~ Fix : Npm Audit issue fix
    ~ Enhancement : Check user card expiry before login and prevent user to login if the card is expired
# Release v4.0.2 on 2025-02-11 

    ~ Enhancement : Add Env Variable to hide show generic search functionality.
# Release v4.0.1 on 2024-12-24 

    ~ Fix : Conversation url issue fix for mobile device
# Release v4.0.0 on 2024-12-18 

    ~ Enhancement : When user right swiped a vehicle, initiate the chat conversation
    ~ Modify the user chat message
    ~ Fix : KYBP Page updated value set in specific search page issue fix
    ~ Enhancement : MC Deleted at Flag Add at My Garage Vehicle List
    ~ Enhancement : Notify the user that make selection has been cleared when adding a body style & Dealer Name Added to Vehicle Details
    ~ Enhancement : Chat Search & Filter Updation at Frontend & Message Count Show
    ~ Enhancement : API Integration for chat process
    ~ Enhancement : Deleted Vehicle Undo Feature Added at My Garage
    ~ Enhancement : Chat UI Enhancement for All Devices
    ~ Enhancement : Develop Onboarding test account numbers
# Release v3.0.0 on 2024-10-21 

    ~ Enhancement : SwipeCards Undo Feature Developed
    ~ Enhancement : For New inventory the year value should be in a defined range
    ~ Enhancement : Make the car brand dropdown depending upon user car looking options for both generic and specific search
    ~ Enhancement : Add vin number in vehicle details modal
    ~ Enhancement : Modified Generic Advance Search with All Filters
    ~ Enhancement : Add Max and Min year dropdown before slider
    ~ Enhancement : Vehicle Type Condition add in Specific Search page
    ~ Enhancement : Add price and year range slider in specific search both web and mobile app
    ~ Enhancement : Add price and year input field before slider
    ~ Enhancement : Reset Search Web & Mobile UI Development
    ~ Enhancement : Retry Search Functionality Development
    ~ Enhancement : Test drive and Ask Dealer api merged and code implementation
    ~ Enhancement : Multiple Vehicle Images Shown
    ~ Enhancement : Specific & Generic Search UI Development & Functionality Implementation
    ~ Enhancement : Ask Dealer Api Integration with Salesforce in my garage
    ~ Fix : Add my garage vehicle Vin in Not In condition in fetch vehicle inventory for better performance
    ~ Fix : Command Center and KYBP Input field issue fix
    ~ Enhancement : Implement Salesforce add to garage and remove from garage API
    ~ Enhancement : Ask Dealer UI Development
    ~ Enhancement : Contact Us & About Us Module UI Development and Integration
# Release v2.0.0 on 2024-08-27 

    ~ Enhancement : Generic Search Module Enhancements
    ~ Enhancement : Club interest rate in Term selection drop down. Remove Max amount enter limit
    ~ Enhancement : Add Calculate Purchase Power button in landing page. Develop calculate purchase power UI and Integrate the API
    ~ Enhancement : Search & Vehicle Inventory Enhancements
    ~ Enhancement : Confirmation Modal page design
    ~ Enhancement : Specific Search design and API integration
    ~ Enhancement : Command Center Design and Development
    ~ Enhancement : PWA App Refresh button new design when new update is availabe
    ~ Fix : PWA App install and icon show issue fix
    ~ Enhancement : Implement Azure Map service to get lat and long from zip code
    ~ Fix : Send OTP and Verfify OTP redirection bug fix.
    ~ Enhancement : Landing Page API Integration.
    ~ Enhancement : Landing Page Development for All Devices
    ~ Enhancement : Design of Initial Screens Before Login
    ~ Enhancement : Design and Developed the New UI Layout
    ~ Fix : Increase the number not found message show time.
    ~ Fix : Increase the number not found message show time.
    ~ Enhancement : Add a refresh button in the PWA app when new build is updated
    ~ Fix : Change the error message in the given otp page when the given mobile number is not found in loanify portal
    ~ Fix : Change the error message in the given otp page when the given mobile number is not found in loanify portal
    ~ Enhancement : Implement azure key vault and fetched the environment variable from the azure key vault in the application.
    ~ Enhancement : Implement eslint extension and fix the errors
    ~ Enhancement : View Vehicle Details Show in modal in Car Garage Page
    ~ Enhancement : Changed the Frontend Structure & Design of Search & Swipe In Inventory Page
    ~ Enhancement : My Garage Page Development and API Integration
    ~ Enhancement : Changed the Frontend Structure & Design of Search & Swipe Page
# Release v1.1.2 on 2024-06-17 

    ~ Fix : Increase the number not found message show time.
# Release v1.1.1 on 2024-06-07 

    ~ Fix : Change the error message in the given otp page when the given mobile number is not found in loanify portal
# Release v1.1.0 on 2024-05-14 

    ~ Enhancement : Implement JWT Token Authentication
    ~ Enhancement : Add dealer help email and phone number in .env and fetch the mail and phone number from .env in the no dealer page
    ~ Enhancement : Create a common Components for loan landing page
# Release v1.0.0 on 2024-05-01 

    ~ Create a new release.sh file and Changelog file.
    ~ Enhancement : Buyer can raise a query for auto-experts from the dealer screen 
    ~ Fix : On giving non registered phone numbers after putting OTP the verify button is still in loading state
    ~ Enhancement : Packages are updated to the latest version
    ~ Enhancement : Optimize the entire codebase and structure of the code
    ~ Enhancement : Add timer functionality in the OTP sending page
    ~ Enhancement : Remove LTV from Loan Approval Amount and Monthly Payment Calculator
    ~ Enhancement : In the Landing page add four buttons
    ~ Enhancement : Voice & Dropdown Search, Auto Inventory & My Garage Page Enhancements