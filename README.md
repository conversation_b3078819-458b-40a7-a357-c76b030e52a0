# FastPass App Readme Document

## Purpose

The README file serves as a comprehensive guide to understanding, setting up, and contributing to this project. Its primary purposes include:

- **Introduction:** Provide a brief overview of the project, its goals, and its intended audience.

- **Installation Instructions:** Guide users through the process of installing and configuring the necessary dependencies, tools, and services to get the project up and running.

- **Configuration Details:** Detail any required configuration settings, environment variables, or external services necessary for the project's proper functioning.

- **Usage Guidelines:** Explain how to use the software, including essential commands, features, and functionalities. This section should help users navigate and interact with the project effectively.

- **Project Architecture:** Offer a high-level view of the project's structure and key components. This section helps users and contributors understand how different parts of the system work together.

- **API Documentation (if applicable):** If the project includes an API, provide details on available endpoints, request/response formats, and authentication mechanisms.

- **Changelog:** Provide a history of notable changes to the project, including new features, bug fixes, and improvements. This helps users stay informed about the project's evolution.

# Introduction

FastPass is the consumer mobile app and the LIFT consumer-facing brand. This app plays a pivotal role to provide a frictionless loan approval for the customers of Credit Union who have interest in the automobile industry. The target customers of this company are the ones who are interested in buying new or old cars.

## Project Documents

- [Architechture Document](https://docs.google.com/presentation/d/1PQTERL0oEq7CMzuBZUHJaQ1S4yqIvCgG3MYqlWJHgmU/edit#slide=id.p)
- [API Documentation](https://docs.google.com/document/d/10SLTqfdHuPWz82nHLtOslNV6e4kGVcRWHwDLG6g4p-M/edit)
- [Production Deployment Document](https://docs.google.com/document/d/127MhBq1SGiRt6ZxCLZP60oCrAy5Zj4DCy9D2jBVgl_A/edit#heading=h.hqzkf2959bh4)

**Note**: Log all feature requests and bugs in Optimore.

### Tools and Techniques / Prerequisites

- React js 18.2.0
- Node js 22.3.0
- npm 10.5.0

## Configuration (local) - First time

1. Clone the repository from correct gitlab account.

2. Open a bash then go to the project root directory and run the following command:

3. Copy `.env.example` and remane it to `.env`

4. Change the .env configuration accordingly

5. Install dependencies `npm install`

## Running the project in development environment

- Open a new terminal and run

  `npm run dev`

## Build the project in development/production environment

- Open a new terminal and run

  `npm run build`
  
## To check is there any eslint issue present or not, run the following command 

- run `npm run lint`