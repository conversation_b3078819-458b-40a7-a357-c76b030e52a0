/* eslint-disable no-undef */
const { execSync } = require('child_process');
const path = require('path');

// Import paths for generating metadata files
const fetchPageMetaDataPath = path.resolve(__dirname, 'src', 'scripts', 'fetchPageMetaData.cjs');
const fetchComponentMetaDataPath = path.resolve(__dirname, 'src', 'scripts', 'fetchComponentMetaData.cjs');

async function loadEnvAndGenerateMakeData() {
  try {
    // Dynamically import the ES module for loading secrets
    const { loadSecretsIntoEnv } = await import('./src/fetchKeyVaulrSecrets.mjs');

    // Fetch secrets and load them into process.env
    await loadSecretsIntoEnv();

    // Generate the pageMetaData.js file
    execSync(`node ${fetchPageMetaDataPath}`, { stdio: 'inherit' });
    console.log('Page meta data imported successfully!');

    // Generate the componentMetaData.js file
    execSync(`node ${fetchComponentMetaDataPath}`, { stdio: 'inherit' });
    console.log('Component meta data imported successfully!');
  } catch (error) {
    console.error('Error importing data:', error);
    process.exit(1); // Exit the process if an error occurs
  }
}

async function main() {
  try {
    // Load environment variables and generate metadata
    await loadEnvAndGenerateMakeData();

    // Build the Vite application
    execSync('vite build', { stdio: 'inherit' });
    console.log('Application built successfully!');
  } catch (error) {
    console.error('Error building application with secrets:', error);
    process.exit(1); // Exit the process if an error occurs
  }
}

main();
