{"name": "my-react-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "node startWithSecrets.cjs", "build": "node buildWithSecrets.cjs", "test": "vite test", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@azure/identity": "^4.3.0", "@azure/keyvault-secrets": "^4.8.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.2", "@mui/icons-material": "^5.15.20", "@mui/material": "^5.15.20", "@mui/x-date-pickers": "^7.16.0", "@reduxjs/toolkit": "^2.2.5", "axios": "^1.8.1", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "esbuild": "^0.25.0", "framer-motion": "^11.2.12", "icomoon-react": "^3.0.0", "lodash.debounce": "^4.0.8", "otp-input-react": "^0.3.0", "pusher-js": "^8.4.0-rc2", "react": "^18.2.0", "react-bootstrap": "^2.10.3", "react-dom": "^18.2.0", "react-icomoon": "^2.5.7", "react-icons": "^5.2.1", "react-image-gallery": "^1.3.0", "react-markdown": "^10.1.0", "react-redux": "^9.1.2", "react-responsive": "^10.0.0", "react-router-dom": "^6.23.1", "react-swipeable": "^7.0.1", "react-toastify": "^11.0.5", "swiper": "^11.1.4", "valid-url": "^1.0.9", "vite": "^6.2.0", "vite-plugin-pwa": "^0.21.1", "workbox-window": "^7.1.0"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "8.25.0", "@vitejs/plugin-react": "^1.3.2", "eslint": "^9.21.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.6", "workbox-sw": "^7.1.0"}}