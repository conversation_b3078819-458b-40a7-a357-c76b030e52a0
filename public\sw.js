// public/sw.js

self.addEventListener('install', () => {

    console.log('Service worker installing...');
  
    // Perform install steps, like caching assets
  
  });
  
  
  
  self.addEventListener('activate', () => {
  
    console.log('Service worker activating...');
  
    // Perform activation steps, like cleaning up old caches
  
  });
  
  
  
  self.addEventListener('fetch', (event) => {
  
    console.log('Fetch intercepted for:', event.request.url);
  
  });