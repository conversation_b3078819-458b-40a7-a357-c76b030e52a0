/* styles.css */
.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.footer-menu {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: #f0f0f0;
  padding: 10px;
}

.footer-menu a {
  text-decoration: none;
  color: #333;
}

.footer-menu a:hover {
  color: #000;
}

.layout {
  display: flex;
  flex-direction: column;
  position: relative;
  /* Ensure footer positioning is relative to the layout */
}

.content {
  flex: 1;
  overflow-y: auto;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
}


header {
  position: fixed;
  top: 0;
  width: 100%;
  background-color: white;
  /* Change this to match your design */
  z-index: 1000;
  /* Ensure the header is above other content */
  padding: 10px 0;
  /* Adjust padding as needed */
  display: flex;
  justify-content: center;
  /* Center the image horizontally */
  align-items: center;
  /* Center the image vertically if needed */
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}

.header-top-logo {
  max-width: 300px;
  /* Adjust the max-width to fit the logo in the header */
  height: auto;
  /* Maintain the aspect ratio */
}

.header-top-logo-mobile {
  max-width: 200px;
  /* Adjust the max-width to fit the logo in the header */
  height: auto;
  /* Maintain the aspect ratio */
}

.header-top-logo-tab {
  max-width: 280px;
  /* Adjust the max-width to fit the logo in the header */
  height: auto;
  /* Maintain the aspect ratio */
}

/* Home.css */
.main-content {
  padding-top: 60px;
  /* Adjust based on your header height */
}

.container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f8f9fa;
  /* Adjust as needed */
}

.grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 20px;
}

.left-side {
  display: flex;
  justify-content: center;
  align-items: center;
}

.left-image {
  max-width: 100%;
  height: auto;
}

.right-side {
  text-align: center;
}

.title {
  font-size: 48px;
  color: #4891ff;
}

.description {
  font-size: 18px;
  margin: 20px 0;
}

.btn.exit {
  background-color: #717171;
}

.terms {
  margin-top: 20px;
}

.terms a {
  color: #4891ff;
  text-decoration: none;
}

.modal-header-fixed {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: #fff;
}

.modal-footer-fixed {
  position: sticky;
  bottom: 0;
  z-index: 1000;
  background-color: #fff;
}

.modal-body-scrollable {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  padding: 15px;
}

.font_color {
  color: #4891ff;
}

.btn_class_landing {
  font-size: 14px;
  text-align: center;
  flex-grow: 1;
}

.btn_class_landing_mobile {
  font-size: 12px;
  text-align: center;
  flex-grow: 1;
}

.btn_class_landing_4k {
  font-size: 20px;
  text-align: center;
  flex-grow: 1;
}

.card-main {
  max-width: 400px;
  height: 300px;
  color: #fff;
  cursor: pointer;
  perspective: 1000px;
  padding: 15px 10px;
  margin: 0 auto;
}

.card-main-mobile {
  max-width: 400px;
  height: 210px;
  color: #fff;
  cursor: pointer;
  perspective: 1000px;
  padding: 0px 35px;
  margin: 0 auto;
}

.card-main-tab {
  max-width: 450px;
  height: 245px;
  color: #fff;
  cursor: pointer;
  perspective: 1000px;
  padding: 0px 35px;
  margin: 0 auto;
}

.card-main-4k {
  max-width: 800px;
  height: 525px;
  color: #fff;
  cursor: pointer;
  perspective: 1000px;
  padding: 15px 10px;
  margin: 0px auto;
}

.card-inner {
  width: 100%;
  height: 100%;
  position: relative;
}

.card-inner-tab {
  width: 100%;
  height: 240px;
  position: relative;
}

.front,
.back {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  padding: 30px;
  border-radius: 15px;
  overflow: hidden;
  z-index: 1;
  backface-visibility: hidden;
}

.front_mobile,
.back_mobile {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  padding: 20px;
  border-radius: 15px;
  overflow: hidden;
  z-index: 1;
  backface-visibility: hidden;
}

.front_4k,
.back_4k {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  padding: 60px;
  border-radius: 15px;
  overflow: hidden;
  z-index: 1;
  backface-visibility: hidden;
}

.card-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.map-img {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}

.card_font {
  font-size: 22px;
  text-align: center;
  font-weight: 600;
}

.card_font_mobile {
  font-size: 15px;
  text-align: center;
  font-weight: 600;
}

.card_font_tab {
  font-size: 20px;
  text-align: center;
  font-weight: 600;
}

.card_font_4k {
  font-size: 35px;
  text-align: center;
  font-weight: 600;
}

.card-holder {
  font-size: 12px;
}

.card-holder-mobile {
  font-size: 10px;
}

.card-holder-4k {
  font-size: 22px;
}

.content_alignment {
  align-content: center;
}

.page_bg {
  background: transparent !important;
}

.loader-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.21);
  z-index: 9999;
  /* Ensure it's above other content */
}

.loader_container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  /* Ensure it's above the overlay */
}

/* Container for positioning the notification box */
.update-button-container {
  position: fixed;
  top: 0;
  /* Position the notification box at the top of the screen */
  left: 0;
  width: 100%;
  height: 100%;
  /* Ensure the container covers the entire viewport */
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Notification box styling */
.notification {
  display: flex;
  flex-direction: row;
  /* Keep icon and text on the same line */
  align-items: center;
  /* Center icon and text vertically */
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 10px 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 500px;
  /* Default width */
  position: relative;
}

/* Text section styling */
.notification-text {
  flex-grow: 1;
  font-size: 15px;
  display: flex;
  /* Flex container for icon and text alignment */
  align-items: center;
  /* Align text and icon vertically */
  margin-left: 10px;
  /* Space between icon and text */
}

/* Refresh button styling */
.notification button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 6px 12px;
  /* Adjust padding for consistent size */
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  font-size: 14px;
  /* Font size */
  margin-left: 20px;
  /* Space between text and button */
}

.notification button:hover {
  background-color: #0056b3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .notification {
    width: 90%;
    /* Make the notification box responsive on tablets */
    padding: 10px 15px;
    /* Adjust padding for tablets */
  }

  .notification-text {
    font-size: 14px;
    /* Smaller text size for tablets */
    margin-left: 8px;
    /* Adjust spacing for tablets */
  }

  .notification button {
    padding: 6px 10px;
    /* Adjust button padding for tablets */
    font-size: 14px;
    /* Font size for tablets */
    margin-left: 16px;
    /* Adjust spacing for tablets */
  }
}

@media (max-width: 480px) {
  .notification {
    width: 95%;
    /* Make the notification box responsive on mobile phones */
    padding: 10px;
    /* Adjust padding for mobile phones */
    flex-direction: column;
    /* Stack icon, text, and button vertically on mobile phones */
    align-items: center;
    /* Center all elements horizontally */
  }

  .notification-text {
    font-size: 12px;
    /* Smaller text size for mobile phones */
    margin-bottom: 5px;
    /* Space between text and button */
    display: flex;
    /* Flex container for icon and text alignment */
    align-items: center;
    /* Align text and icon vertically */
  }

  .notification button {
    padding: 6px 12px;
    /* Adjust button padding for mobile phones */
    font-size: 12px;
    /* Font size for mobile phones */
    display: block;
    /* Ensure button is block-level */
    margin-left: auto;
    /* Center button horizontally */
    margin-right: auto;
    /* Center button horizontally */
  }
}

/* Web specific styles */
@media (min-width: 481px) {
  .notification {
    width: 415px;
    /* Smaller width for web */
    max-height: 200px;
    /* Larger height for web */
    flex-direction: column;
    /* Stack content vertically */
    padding: 20px;
    /* Increase padding for web */
    overflow-y: auto;
    /* Enable scrolling if content exceeds max height */
  }

  .notification-text {
    margin-left: 0;
    /* Remove left margin for text */
    margin-bottom: 15px;
    /* Space between text and button */
  }

  .notification button {
    margin-left: 0;
    /* Remove left margin for button */
  }
}

/* Confirmation Modal Design */
.conf-modal-header-fixed {
  border-bottom: none;
}

.conf-modal-body-scrollable {
  text-align: center;
}

.conf-modal-question {
  font-size: 18px;
  margin-bottom: 20px;
  padding-bottom: 20px;
}

.conf-modal-buttons {
  display: flex;
  justify-content: space-around;
}

.custom-button {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  padding: 10px 20px;
  margin: 0 10px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
}

.custom-button:hover {
  background-color: #007bff;
}

.conf-modal-icon {
  margin-right: 10px;
  font-size: 18px;
}

.input_fonts {
  font-family: inherit !important;
  font-weight: 400 !important;
  color: #4891ff !important;
}

.input_fonts_disabled {
  font-family: inherit !important;
  font-weight: 400 !important;
  color: grey !important;
}

.loan-ids-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  text-align: center;
  font-size: 20px;
}

.loan-ids-grid-4k {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  text-align: center;
  font-size: 50px;
}

.loan-ids-grid-mb {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  /* Creates 4 equal columns */
  gap: 15px;
  text-align: center;
  font-size: 20px;
}

.loan-ids-grid span {
  margin: 0;
  padding: 0;
}

.header_my_garage {
  margin-top: 75px;
  border-top: 1px solid #4891FF;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}

.header_my_garage_mb {
  margin-top: 55px;
  border-top: 1px solid #4891FF;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}

.body-type-button-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* css for vehicle image gallery */
@media (min-width: 1024px) {
  .image-gallery-left-nav {
    left: -12px;
  }

  .image-gallery-right-nav {
    right: -12px;
  }

  .image-gallery-left-nav,
  .image-gallery-right-nav {
    padding: 50px 0px;
    top: 50%;
    transform: translateY(-50%);
  }

  .image-gallery-left-nav .image-gallery-svg,
  .image-gallery-right-nav .image-gallery-svg {
    height: 75px;
    width: 75px;
  }

  .image-gallery-play-button {
    left: -12px;
  }

  .image-gallery-fullscreen-button {
    right: -12px;
  }

  .image-gallery-fullscreen-button .image-gallery-svg,
  .image-gallery-play-button .image-gallery-svg {
    height: 32px;
    width: 32px;
  }


}

.chat_list_shadow {
  box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 1px 3px 1px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  /* Transition for smoothness */
}

.chat_list_shadow:hover {
  transform: translateY(-2px);
  box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.3);
}

/* Fab button base styles */
#fab-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #4891FF;
  /* Default blue color */
  color: #f0f0f0;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

#fab-button:hover {
  background-color: #0052a3;
  /* Slightly darker blue on hover */
  color: #f0f0f0;
  /* Ensure the text/icon color remains white */
}

/* Fab button active state when listening */
.fab-listening {
  background-color: #ff5722;
  /* Orange color */
  animation: pulse 2s infinite;
}

/* Media queries for responsiveness */
@media (max-width: 960px) {
  #fab-button {
    width: 48px;
    height: 48px;
    margin: 0 auto;
    /* Center align */
  }
}

/* Pulse animation */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 87, 34, 0.4);
  }

  50% {
    transform: scale(1.1);
    box-shadow: 0 0 30px 10px rgba(255, 87, 34, 0.6);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 87, 34, 0.4);
  }
}

.garage-deal {
  background-color: #61C454 !important;
  text-transform: none !important;
  font-weight: 600 !important;
  color: #fff;
}

.garage-ai-negotiation-btn {
  background-color: #F58C4B !important;
  color: white !important;
  text-transform: none !important;
  border-radius: 4px !important;
  display: flex !important;
  align-items: center !important;
  gap: 1 !important;
  font-weight: 600 !important;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2) !important;
}

.garage-ai-negotiation-btn:hover {
  background-color: #e07b3c !important;
}

.garage-dealer-btn {
  background-color: #4891FF !important;
  text-transform: none !important;
  font-weight: 600 !important;
  color: #fff;
}

.ribbon-web {
  position: absolute;
  top: 8px;
  right: 125px;
  width: 85px;
  background-color: red;
  color: white;
  padding: 5px 10px;
  font-size: 14px;
  font-weight: bold;
  /* transform: rotate(-30deg); */
  z-index: 1;
  border-radius: 5px;
  text-align: center;
}

.ribbon-mobile {
  position: absolute;
  top: 35%;
  left: -7%;
  background-color: rgb(255, 255, 255);
  opacity: 0.9;
  color: red;
  text-align: center;
  font-weight: bold;
  padding: 5px 10px;
  width: 170px;
  transform: rotate(-30deg);
  border: 1px solid red;
}

.deal {
  background: #fff;
  border: none !important;
  color: #105DC7 !important;
  padding: 0 !important;
  font-weight: bold !important;
  display: flex !important;
  align-items: center !important;
  text-transform: none !important;
}

.view-more-details {
  background: transparent !important;
  border: none !important;
  color: cornflowerblue !important;
  padding: 0 !important;
  font-weight: bold !important;
  text-decoration: underline !important;
  display: flex !important;
  align-items: center !important;
  text-transform: none !important;
}

.button-container-deal {
  flex-direction: row;
  justify-content: center;
  gap: 12px;
}

@media (max-width: 375px) {
  .button-container-deal {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .button-container-deal button {
    flex: 1;
    /* Makes buttons evenly spaced */
  }
}


/* swipecards sorting button & icon */
.sort-button {
  background-color: #fff !important;
  color: #4891FF !important;
  font-weight: 600 !important;
  border: 1px solid #4891FF !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2), 0 6px 20px rgba(0, 0, 0, 0.2) !important;

  &:hover {
    background-color: aliceblue;
  }
}

.info-icon {
  position: absolute;
  top: -5px;
  right: 0;
  background-color: #fff;
  border-radius: 50%;
  color: #F58C4B;
  padding: 2px;
  z-index: 2000;
}

.tutorial-body-img {
  background-size: contain;
  background-repeat: no-repeat;
  width: 85%;
  height: auto;
}

.tutorial-font-color {
  color: #105DC7;
}

.tutorial-bg-img {
  background-size: contain;
  background-repeat: no-repeat;
  border-radius: 7px
}

.tutorial-modal-btn {
  box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.2);
  padding: 20px;
  color: #105DC7;
  font-weight: 600;
  align-content: center;

  &:hover {
    background-color: #e6f0ff;
    box-shadow: 0 4px 12px rgba(16, 93, 199, 0.3), inset 0 4px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
  }

  &:active {
    background-color: #d0e3ff;
    box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.2);
    transform: scale(0.98);
  }
}

.tutorial-close-icon {
  position: absolute;
  top: -18px;
  right: -18px;
  width: 36px;
  height: 36px;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(2px);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  transition: background 0.2s ease;
}

.tutorial-close-icon:hover {
  background: rgba(0, 0, 0, 0.2);
}

.tutorial-modal-dialog {
  margin: 5% !important;
}

@media (max-width: 768px) {
  .tutorial-modal-btn {
    padding: 15px;
    font-size: 14px;
  }
}

.loan-offer-modal-accept-btn {
  background-color: #5c176b;
  color: white !important;

  &:hover {
    background-color: #752C83;
  }
}

.loan-offer-modal-cancel-btn {
  background-color: #DC2727;
  color: white !important;

  &:hover {
    background-color: #f01e1e;
  }
}

.custom-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #b0b0b0 !important;
  color: #f0f0f0 !important;
  border: none;
  pointer-events: none;
}

.garage-btn-labels {
  min-width: 80px;
  text-align: center;
}

.fp-share-icon {
  position: absolute;
  bottom: -3px;
  right: 6px;
  width: 25px;
  height: 25px;
  background-color: #1E68D8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.fp-share-modal-yes-btn {
  background-color: #0B54A7;
  color: white !important;

  &:hover {
    background-color: #2C6FC0;
  }
}

.fp-share-modal-no-btn {
  background-color: #DC2727;
  color: white !important;

  &:hover {
    background-color: #f01e1e;
  }
}

.ai-bot-container {
  position: fixed;
  bottom: 75px;
  right: 2px;
  z-index: 9999;
  width: 150px;
  height: 150px;
  overflow: hidden;
  background-color: transparent;
  cursor: pointer;
  transition: transform 0.2s;
}

.ai-bot-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: transparent;
  display: block;
}

.custom-modal {
  width: 95vw !important;
  max-width: 95vw !important;
  height: 90vh;
  max-height: 90vh;

  /* NEW: Center vertically */
  display: flex !important;
  align-items: center;
  justify-content: center;
}

.custom-modal .modal-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 0 20px 20px 0;
  background-color: #F1F3F5;
  animation: glowBreathe 5s ease-in-out infinite;
}

@keyframes glowBreathe {
  0% {
    box-shadow: 0 0 3px #A16BFE;
  }

  50% {
    box-shadow: 0 0 5px 3px #A16BFE;
  }

  100% {
    box-shadow: 0 0 3px #A16BFE;
  }
}

.modal-body-scroll {
  flex: 1 1 auto;
  overflow-y: auto;
}

.no-padding-modal {
  padding-right: 0 !important;
}

.sidebar {
  width: 275px;
  transition: width 0.3s ease;
  overflow: hidden;
}

.sidebar.collapsed {
  width: 50px;
}

.sidebar button.collapse-btn {
  border: none;
  background: none;
  font-weight: bold;
}

.chat-card-wrapper {
  height: 100%;
  width: 100%;
  overflow-y: auto;
}

.chat-card {
  width: 98% !important;
  max-width: none;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  margin: 25px 10px 25px 5px;
  padding: 0;
  border-radius: 0px 20px 20px 00px;
}

.chat-card-wrapper,
.chat-card,
.main-content {
  box-sizing: border-box;
}

.ai-header-top-logo {
  max-width: 65%;
  /* Adjust the max-width to fit the logo in the header */
  height: auto;
  /* Maintain the aspect ratio */
  object-fit: contain;
}

.ai-header-top-logo-mobile {
  max-width: 200px;
  /* Adjust the max-width to fit the logo in the header */
  height: auto;
  /* Maintain the aspect ratio */
  object-fit: contain;
}

.ai-header-top-logo-tab {
  max-width: 280px;
  /* Adjust the max-width to fit the logo in the header */
  height: auto;
  /* Maintain the aspect ratio */
  object-fit: contain;
}

.chat-list {
  align-self: flex-start !important;
  width: 100% !important;
  font-weight: 500 !important;
  border: 1px solid #D9D9D9;
  padding: 10px !important;
  border-radius: 10px !important;
  margin-bottom: 6px !important;
  font-size: 14px;
  box-sizing: border-box;
  cursor: pointer;

  &:hover {
    background-color: #BFBFBF !important;
  }
}

.chat-list.selected {
  background-color: #D9D9D9 !important;
}

.ai-chat-close-icon {
  position: absolute;
  top: -12px;
  right: -12px;
  width: 36px;
  height: 36px;
  background: #DC143C;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

@media (max-width: 928px) {
  .sidebar {
    position: fixed;
    left: 0;
    width: 280px;
    height: 97vh;
    background-color: #fff;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
    z-index: 1050;
    transform: translateX(0);
    /* default state: visible */
    transition: transform 0.3s ease-in-out;
  }

  .sidebar-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1040;
  }

  .custom-modal .modal-content {
    border-radius: 0;
  }

  .chat-card {
    margin: 10px 10px 10px 4px;
    border-radius: 0;
  }

  .ai-chat-close-icon {
    position: absolute;
    top: 13px;
    right: 16px;
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10;
    background: #DC143C;
    border-radius: 50%;
    box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
  }

  .ai-bot-container {
    width: 100px;
    height: 100px
  }

  .chat-response-vehicle-cards {
    height: 275px;
  }
}

/* Ensure modal body fills modal height for chat modal */
.custom-modal .modal-content,
.custom-modal .modal-body {
  height: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
  padding: 0 !important;
}

.chat-response-vehicle-cards {
  width: 100%;
  max-width: 300px;
  max-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 10px;
  padding-top: 0 !important;
  padding-bottom: 5px !important;
}

.sleek-scrollbar {
  scrollbar-width: thin;              /* Firefox */
  scrollbar-color: #4891FF #f1f1f1;   /* Firefox */
}
.sleek-scrollbar::-webkit-scrollbar {
  width: 6px;
  background: #f1f1f1;
}
.sleek-scrollbar::-webkit-scrollbar-thumb {
  background: #105DC7;
  border-radius: 10px;
}
.sleek-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #105DC7;
}