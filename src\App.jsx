import { useEffect, useState } from "react";
import { useMediaQuery } from "react-responsive";
import {
  BrowserRouter as Router,
  useNavigate,
  useLocation,
} from "react-router-dom";
import { NotificationProvider } from "./context/NotificationContext"; // Import the provider
import AppRoutes from "./routes";
import "./App.css";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { ImageBaseURL } from "./config";
import { subscribeToChannel, unsubscribeFromChannel } from "./util/pusher";
// Inside the useEffect of App.jsx
import { useNotification } from "./context/NotificationContext";
import { UserDataProvider } from "./pages/UserDataContext";
import { ModalProvider } from "./context/ModalContext";
import { TutorialModalProvider } from "./context/TutorialModalContext";

function App() {
  const isTabletOrMobile = useMediaQuery({ query: "(max-width: 1224px)" });
  const isMobile = useMediaQuery({ query: "(max-width: 767px)" });
  // Constructing background image URL based on device type
  let backgroundImageUrl = `${ImageBaseURL}web_background.png`;
  if (isTabletOrMobile) {
    backgroundImageUrl = isMobile
      ? `${ImageBaseURL}mobile_background.png`
      : `${ImageBaseURL}tab_background.png`;
  }

  const backgroundStyle = {
    backgroundImage: `url(${backgroundImageUrl})`,
    backgroundSize: "cover",
    backgroundPosition: "center",
    backgroundAttachment: "fixed", // Ensures the background image is fixed
    overflow: "hidden", // Prevents scrolling
    minHeight: "100vh",
    width: "100%",
  };

  return (
    <UserDataProvider>
      <NotificationProvider>
        <div className="App" style={backgroundStyle}>
          <Router>
            <TutorialModalProvider>
              <ModalProvider>
                <AppContent />
              </ModalProvider>
            </TutorialModalProvider>
          </Router>
          <ToastContainer />
        </div>
      </NotificationProvider>
    </UserDataProvider>
  );
}

function AppContent() {
  const token = localStorage.getItem("token");
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [lastVisitedPage, setLastVisitedPage] = useState("/");
  const navigate = useNavigate();
  const location = useLocation();
  const user_id = localStorage.getItem("user_id");
  const { setHasNewMessage, setPusherData, addUnreadMessage } =
    useNotification(); // Accessing context setter

  useEffect(() => {
    function handleOnline() {
      setIsOnline(true);
      if (location.pathname === "/no-internet") {
        navigate(lastVisitedPage, { replace: true });
      }
    }
    function handleOffline() {
      setIsOnline(false);
      setLastVisitedPage(location.pathname);
      navigate("/no-internet", { replace: true });
    }

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, [navigate, location.pathname, lastVisitedPage]);

  // Pusher Integration for New Messages
  useEffect(() => {
    if (!user_id) return;

    const channel = subscribeToChannel(user_id);

    channel.bind("webhook-processed", (data) => {

      // Store the Pusher data in the context
      setPusherData(data);

      // Update the notification state
      setHasNewMessage(true);

      // Store multiple unread message types
      addUnreadMessage(data.msg_type);

      if (
        location.pathname !== `/conversations` &&
        location.pathname !== `/cu-conversations` &&
        location.pathname !== `/fpadvisor-conversations`
      ) {
        let message = "";
        switch (data.msg_type) {
          case "askMSR":
            message = "New Message Arrived from MSR";
            break;
          case "askAdvisor":
            message = "New Message Arrived from FP Advisor";
            break;
          case "ask_dealer":
            message = "New Message Arrived from Dealer";
            break;
          default:
            message = "New Message Arrived";
        }

        // Show toast notification
        toast.success(message, {
          className: "bg-success text-white fw-600",
          bodyClassName: "text-white",
          icon: <i className="bi bi-exclamation-circle"></i>,
          autoClose: 15000,
        });
      }
    });

    return () => {
      channel.unbind_all();
      unsubscribeFromChannel(user_id);
    };
  }, [location.pathname, setHasNewMessage, user_id]);

  return <AppRoutes isOnline={isOnline} token={token} />;
}

export default App;
