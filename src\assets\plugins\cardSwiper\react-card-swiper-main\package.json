{"name": "react-card-swiper", "private": false, "version": "1.1.25", "type": "module", "main": "./dist/react-card-swiper.umd.cjs", "module": "./dist/react-card-swiper.js", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/react-card-swiper.js"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/react-card-swiper.umd.cjs"}}}, "files": ["dist"], "publishConfig": {"access": "public"}, "author": "<PERSON>iv <PERSON> (https://github.com/nativsibony)", "scripts": {"dev": "vite src/demo", "build": "rm -rf dist && vite build", "build:web": "vite build src/demo", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "prepare": "npm run build", "preview": "vite preview", "pub:patch": "npm version patch --force", "pub:minor": "npm version minor --force", "pub:major": "npm version major --force"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^20.9.4", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.2.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0", "vite-plugin-css-injected-by-js": "^3.3.0", "vite-plugin-dts": "^3.6.3"}, "keywords": ["react", "swiper", "card", "tinder", "card swiper", "react swiper", "tinder swiper", "react card swiper", "tinder card swiper", "react-card-swiper"], "description": "A Tinder Like - Card Swiper", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/nativsibony/react-card-swiper"}, "homepage": "https://react-card-swiper.netlify.app/", "bugs": {"url": "https://github.com/nativsibony/react-card-swiper/issues"}}