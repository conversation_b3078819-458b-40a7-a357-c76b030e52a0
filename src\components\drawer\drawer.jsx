import "../drawer/style.css";
import PropTypes from "prop-types";
import { useNavigate } from "react-router-dom";
import AboutModal from "./modals/aboutModal";
import { useEffect, useState } from "react";
import ConfirmLogOutDialog from "../../pages/authentication/modals/confirmLogOutDialog";
import { pageMetaData } from "../../../data/pageMetaData";
import { pageComponentData } from "../../../data/componentMetaData";
import { callComponentActionApi } from "../../util/callComponenetActionApi";
import { useDispatch } from "react-redux";
import { resetTutorialProgress } from "../../context/TutorialStorage";

const Drawer = ({ show, onHide }) => {
  const navigate = useNavigate();
  const [showModal, setShowModal] = useState(false);
  const handleShow = () => setShowModal(true);
  const handleClose = () => setShowModal(false);
  const [pageId, setPageId] = useState(null); // State to store the page_id
  const [componentMetaDataVal, setComponentMetaData] = useState(null); // State to store the componentMetaData
  const [logoutLoader, setLogoutLoader] = useState(null); // State to store the componentMetaData
  const [learnMoreLoader, setLearnMoreLoader] = useState(false); // State for learn more button loading
  const dispatch = useDispatch();

  //log out dialog
  const [open, setOpen] = useState(false);
  const handleLogout = () => {
    // Open the confirmation dialog
    setOpen(true);
  };
  const handleCloseDialog = () => {
    // Close the confirmation dialog
    setOpen(false);
  };

  /**
   * Handles the "Confirm Logout" dialog confirmation button click event.
   *
   * Tries to call the logout API using the component ID found in componentMetaDataVal.
   * If the API call is successful, clears localStorage and redirects to the login page.
   * If the API call fails, does not clear localStorage or redirect.
   *
   * @param {void} Nothing.
   * @return {Promise<void>} Nothing.
   */
  const handleConfirmLogout = async () => {
    try {
      let slug = "logout";
      setLogoutLoader(true);
      // Find the component by slug
      const component = componentMetaDataVal.find((comp) => comp.slug === slug);

      if (!component) {
        console.error(`Component with slug "${slug}" not found.`);
        return;
      }

      // Call the API with the component ID and wait for it to complete
      await callComponentActionApi(dispatch, component.id);
      setLogoutLoader(false);
      // If API call is successful, clear localStorage and redirect
      localStorage.clear();
      navigate("/send-otp", { replace: true });
      location.reload();
    } catch (error) {
      console.error("Error handling button click:", error);
      // If API call fails, do NOT clear localStorage or redirect
    }
  };

  // Store logout button click //
  useEffect(() => {
    // Get the page_id from pageMetaData based on slug
    const slug = "logout"; // Replace this with the desired slug
    const pageData = pageMetaData.find((page) => page.slug === slug);
    if (pageData) {
      setPageId(pageData.id);
    }

    // Get Page Componenet Meta Data
    const matchingComponents = pageComponentData.filter(
      (pageComponent) => pageComponent.page_id === pageId
    );

    if (matchingComponents.length > 0) {
      setComponentMetaData(matchingComponents);
    }
  }, [pageId]);
  // End //

  const handleLearnMore = async () => {
    await resetTutorialProgress(dispatch, setLearnMoreLoader);
    window.location.reload(); // optional: refresh to apply logic immediately
  };

  return (
    <div
      className={`drawer-half ${show ? "slide-in" : "slide-out"}`}
      style={{ zIndex: 1000 }}
    >
      <div className="row mt-4 mx-4">
        <div className="col-12 mx-0 px-0">
          <button onClick={onHide} className="btn  float-end m-0 p-0">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M13.4099 12L19.7099 5.71C19.8982 5.5217 20.004 5.2663 20.004 5C20.004 4.7337 19.8982 4.47831 19.7099 4.29C19.5216 4.1017 19.2662 3.99591 18.9999 3.99591C18.7336 3.99591 18.4782 4.1017 18.2899 4.29L11.9999 10.59L5.70994 4.29C5.52164 4.1017 5.26624 3.99591 4.99994 3.99591C4.73364 3.99591 4.47824 4.1017 4.28994 4.29C4.10164 4.47831 3.99585 4.7337 3.99585 5C3.99585 5.2663 4.10164 5.5217 4.28994 5.71L10.5899 12L4.28994 18.29C4.19621 18.383 4.12182 18.4936 4.07105 18.6154C4.02028 18.7373 3.99414 18.868 3.99414 19C3.99414 19.132 4.02028 19.2627 4.07105 19.3846C4.12182 19.5064 4.19621 19.617 4.28994 19.71C4.3829 19.8037 4.4935 19.8781 4.61536 19.9289C4.73722 19.9797 4.86793 20.0058 4.99994 20.0058C5.13195 20.0058 5.26266 19.9797 5.38452 19.9289C5.50638 19.8781 5.61698 19.8037 5.70994 19.71L11.9999 13.41L18.2899 19.71C18.3829 19.8037 18.4935 19.8781 18.6154 19.9289C18.7372 19.9797 18.8679 20.0058 18.9999 20.0058C19.132 20.0058 19.2627 19.9797 19.3845 19.9289C19.5064 19.8781 19.617 19.8037 19.7099 19.71C19.8037 19.617 19.8781 19.5064 19.9288 19.3846C19.9796 19.2627 20.0057 19.132 20.0057 19C20.0057 18.868 19.9796 18.7373 19.9288 18.6154C19.8781 18.4936 19.8037 18.383 19.7099 18.29L13.4099 12Z"
                fill="#636262"
              />
            </svg>
          </button>
        </div>
        <div className="col-12 border-bottom border-2 p-0 m-0">
          <button
            className="btn btn-rounded float-start fw-700 btn-menu"
            onClick={handleLogout}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M4 12C4 12.2652 4.10536 12.5196 4.29289 12.7071C4.48043 12.8946 4.73478 13 5 13H12.59L10.29 15.29C10.1963 15.383 10.1219 15.4936 10.0711 15.6154C10.0203 15.7373 9.9942 15.868 9.9942 16C9.9942 16.132 10.0203 16.2627 10.0711 16.3846C10.1219 16.5064 10.1963 16.617 10.29 16.71C10.383 16.8037 10.4936 16.8781 10.6154 16.9289C10.7373 16.9797 10.868 17.0058 11 17.0058C11.132 17.0058 11.2627 16.9797 11.3846 16.9289C11.5064 16.8781 11.617 16.8037 11.71 16.71L15.71 12.71C15.801 12.6149 15.8724 12.5028 15.92 12.38C16.02 12.1365 16.02 11.8635 15.92 11.62C15.8724 11.4972 15.801 11.3851 15.71 11.29L11.71 7.29C11.6168 7.19676 11.5061 7.1228 11.3842 7.07234C11.2624 7.02188 11.1319 6.99591 11 6.99591C10.8681 6.99591 10.7376 7.02188 10.6158 7.07234C10.4939 7.1228 10.3832 7.19676 10.29 7.29C10.1968 7.38324 10.1228 7.49393 10.0723 7.61575C10.0219 7.73757 9.99591 7.86814 9.99591 8C9.99591 8.13186 10.0219 8.26243 10.0723 8.38425C10.1228 8.50607 10.1968 8.61676 10.29 8.71L12.59 11H5C4.73478 11 4.48043 11.1054 4.29289 11.2929C4.10536 11.4804 4 11.7348 4 12ZM17 2H7C6.20435 2 5.44129 2.31607 4.87868 2.87868C4.31607 3.44129 4 4.20435 4 5V8C4 8.26522 4.10536 8.51957 4.29289 8.70711C4.48043 8.89464 4.73478 9 5 9C5.26522 9 5.51957 8.89464 5.70711 8.70711C5.89464 8.51957 6 8.26522 6 8V5C6 4.73478 6.10536 4.48043 6.29289 4.29289C6.48043 4.10536 6.73478 4 7 4H17C17.2652 4 17.5196 4.10536 17.7071 4.29289C17.8946 4.48043 18 4.73478 18 5V19C18 19.2652 17.8946 19.5196 17.7071 19.7071C17.5196 19.8946 17.2652 20 17 20H7C6.73478 20 6.48043 19.8946 6.29289 19.7071C6.10536 19.5196 6 19.2652 6 19V16C6 15.7348 5.89464 15.4804 5.70711 15.2929C5.51957 15.1054 5.26522 15 5 15C4.73478 15 4.48043 15.1054 4.29289 15.2929C4.10536 15.4804 4 15.7348 4 16V19C4 19.7956 4.31607 20.5587 4.87868 21.1213C5.44129 21.6839 6.20435 22 7 22H17C17.7956 22 18.5587 21.6839 19.1213 21.1213C19.6839 20.5587 20 19.7956 20 19V5C20 4.20435 19.6839 3.44129 19.1213 2.87868C18.5587 2.31607 17.7956 2 17 2Z"
                fill="#636262"
              />
            </svg>{" "}
            <span className="ps-3">LOG OUT</span>
          </button>
        </div>
        <div className="col-12 p-0 m-0">
          <button
            className="btn btn-rounded float-start fw-700 btn-menu"
            onClick={handleShow}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M9 10H10C10.2652 10 10.5196 9.89464 10.7071 9.70711C10.8946 9.51957 11 9.26522 11 9C11 8.73478 10.8946 8.48043 10.7071 8.29289C10.5196 8.10536 10.2652 8 10 8H9C8.73478 8 8.48043 8.10536 8.29289 8.29289C8.10536 8.48043 8 8.73478 8 9C8 9.26522 8.10536 9.51957 8.29289 9.70711C8.48043 9.89464 8.73478 10 9 10ZM9 12C8.73478 12 8.48043 12.1054 8.29289 12.2929C8.10536 12.4804 8 12.7348 8 13C8 13.2652 8.10536 13.5196 8.29289 13.7071C8.48043 13.8946 8.73478 14 9 14H15C15.2652 14 15.5196 13.8946 15.7071 13.7071C15.8946 13.5196 16 13.2652 16 13C16 12.7348 15.8946 12.4804 15.7071 12.2929C15.5196 12.1054 15.2652 12 15 12H9ZM20 8.94C19.9896 8.84813 19.9695 8.75763 19.94 8.67V8.58C19.8919 8.47718 19.8278 8.38267 19.75 8.3L13.75 2.3C13.6673 2.22222 13.5728 2.15808 13.47 2.11C13.4402 2.10576 13.4099 2.10576 13.38 2.11C13.2784 2.05174 13.1662 2.01434 13.05 2H7C6.20435 2 5.44129 2.31607 4.87868 2.87868C4.31607 3.44129 4 4.20435 4 5V19C4 19.7956 4.31607 20.5587 4.87868 21.1213C5.44129 21.6839 6.20435 22 7 22H17C17.7956 22 18.5587 21.6839 19.1213 21.1213C19.6839 20.5587 20 19.7956 20 19V9C20 9 20 9 20 8.94ZM14 5.41L16.59 8H15C14.7348 8 14.4804 7.89464 14.2929 7.70711C14.1054 7.51957 14 7.26522 14 7V5.41ZM18 19C18 19.2652 17.8946 19.5196 17.7071 19.7071C17.5196 19.8946 17.2652 20 17 20H7C6.73478 20 6.48043 19.8946 6.29289 19.7071C6.10536 19.5196 6 19.2652 6 19V5C6 4.73478 6.10536 4.48043 6.29289 4.29289C6.48043 4.10536 6.73478 4 7 4H12V7C12 7.79565 12.3161 8.55871 12.8787 9.12132C13.4413 9.68393 14.2044 10 15 10H18V19ZM15 16H9C8.73478 16 8.48043 16.1054 8.29289 16.2929C8.10536 16.4804 8 16.7348 8 17C8 17.2652 8.10536 17.5196 8.29289 17.7071C8.48043 17.8946 8.73478 18 9 18H15C15.2652 18 15.5196 17.8946 15.7071 17.7071C15.8946 17.5196 16 17.2652 16 17C16 16.7348 15.8946 16.4804 15.7071 16.2929C15.5196 16.1054 15.2652 16 15 16Z"
                fill="#636262"
              />
            </svg>{" "}
            <span className="ps-3">ABOUT</span>
          </button>
          <AboutModal
            show={showModal}
            onHide={handleClose}
            src="https://www.loanify.net/loanify-privacy-policy"
          />
        </div>
        <div className="col-12 p-0 m-0">
          <button
            className="btn btn-rounded float-start fw-700 btn-menu"
            onClick={() => navigate("/contact-us")}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="21"
              height="20"
              viewBox="0 0 21 20"
              fill="none"
            >
              <path
                d="M18.4401 11.0004C18.2201 11.0004 17.9901 10.9304 17.7701 10.8804C17.3246 10.7822 16.8868 10.6518 16.4601 10.4904C15.9962 10.3216 15.4862 10.3304 15.0284 10.515C14.5706 10.6996 14.1972 11.047 13.9801 11.4904L13.7601 11.9404C12.7861 11.3985 11.8911 10.7256 11.1001 9.94036C10.3149 9.14937 9.64192 8.25435 9.1001 7.28036L9.5201 7.00036C9.96347 6.78327 10.3109 6.40989 10.4955 5.95205C10.6801 5.49421 10.6889 4.98427 10.5201 4.52036C10.3613 4.09278 10.231 3.65515 10.1301 3.21036C10.0801 2.99036 10.0401 2.76036 10.0101 2.53036C9.88867 1.82598 9.51973 1.1881 8.96972 0.731596C8.41972 0.275093 7.7248 0.0299652 7.0101 0.0403564H4.0101C3.57913 0.0363098 3.15235 0.12517 2.75881 0.300887C2.36527 0.476604 2.01421 0.735052 1.72953 1.05864C1.44485 1.38222 1.23324 1.76335 1.10909 2.17607C0.984942 2.58879 0.95118 3.02342 1.0101 3.45036C1.54284 7.63974 3.45613 11.5322 6.44775 14.513C9.43938 17.4938 13.3388 19.3929 17.5301 19.9104H17.9101C18.6475 19.9114 19.3595 19.6409 19.9101 19.1504C20.2265 18.8674 20.4792 18.5205 20.6516 18.1327C20.8239 17.7448 20.9121 17.3248 20.9101 16.9004V13.9004C20.8979 13.2057 20.6449 12.5369 20.1944 12.008C19.744 11.4791 19.1239 11.123 18.4401 11.0004ZM18.9401 17.0004C18.9399 17.1423 18.9095 17.2827 18.8509 17.412C18.7923 17.5413 18.7068 17.6566 18.6001 17.7504C18.4887 17.8474 18.3581 17.9198 18.2168 17.9629C18.0755 18.0059 17.9267 18.0187 17.7801 18.0004C14.035 17.5202 10.5563 15.8068 7.89282 13.1306C5.2293 10.4545 3.53251 6.96769 3.0701 3.22036C3.05419 3.07387 3.06813 2.92569 3.1111 2.78475C3.15407 2.64381 3.22517 2.51304 3.3201 2.40036C3.41381 2.29369 3.52916 2.2082 3.65848 2.14957C3.7878 2.09095 3.92812 2.06054 4.0701 2.06036H7.0701C7.30265 2.05518 7.52972 2.13124 7.71224 2.27543C7.89476 2.41962 8.02131 2.62293 8.0701 2.85036C8.1101 3.12369 8.1601 3.39369 8.2201 3.66036C8.33562 4.1875 8.48936 4.70554 8.6801 5.21036L7.2801 5.86036C7.1604 5.91528 7.05272 5.9933 6.96326 6.08995C6.87379 6.1866 6.8043 6.29997 6.75877 6.42355C6.71324 6.54713 6.69257 6.67849 6.69795 6.81008C6.70332 6.94167 6.73464 7.0709 6.7901 7.19036C8.2293 10.2731 10.7073 12.7512 13.7901 14.1904C14.0336 14.2904 14.3066 14.2904 14.5501 14.1904C14.6748 14.1457 14.7894 14.0768 14.8873 13.9875C14.9851 13.8983 15.0643 13.7905 15.1201 13.6704L15.7401 12.2704C16.2571 12.4552 16.7847 12.6088 17.3201 12.7304C17.5868 12.7904 17.8568 12.8404 18.1301 12.8804C18.3575 12.9291 18.5608 13.0557 18.705 13.2382C18.8492 13.4207 18.9253 13.6478 18.9201 13.8804L18.9401 17.0004Z"
                fill="#636262"
              />
            </svg>{" "}
            <span className="ps-3">CONTACT US</span>
          </button>
        </div>
        <div className="col-12 p-0 m-0">
          <button
            className="btn btn-rounded float-start fw-700 btn-menu"
            onClick={handleLearnMore}
            disabled={learnMoreLoader}
          >
            {learnMoreLoader ? (
              <div className="spinner-border spinner-border-sm me-2" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            ) : (
              <svg fill="#636262" height="22px" width="22px" version="1.2" baseProfile="tiny" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" viewBox="-63 65 128 128" xmlSpace="preserve">

                <g id="SVGRepo_bgCarrier" strokeWidth="0" />

                <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />

                <g id="SVGRepo_iconCarrier"> <path d="M1,102.1c9.7,0,17.5-7.9,17.5-17.6S10.7,66.9,1,66.9s-17.6,7.8-17.6,17.6C-16.6,94.2-8.7,102.1,1,102.1 M18,106.3 c8,0.3,11.4,6.7,11.4,6.7l25.9,35.6c1.1,1.6,1.7,3.5,1.7,5.6c0,5.5-4.5,10-10,10c-1.3,0-2.5-0.3-3.7-0.7l-15.6-4.4v15.6h-54.2v-15.6 l-15.6,4.4c-1.1,0.4-2.3,0.7-3.7,0.7c-5.5,0-10-4.5-10-10c0-2.1,0.6-4,1.7-5.6l25.9-35.6c0,0,3.4-6.4,11.4-6.7H18z M0.6,163.8 L0.6,163.8l20.3-6.7l-0.5-0.1c-14-4.1-8.6-22.6,5.4-18.5l1.9,0.7v-20.9l-27.1,8.9l-27.2-8.9v20.9l1.9-0.7c14-4.1,19.4,14.3,5.4,18.5 l-0.5,0.1L0.6,163.8z M56.4,188.6c1.4,0,2.5-1.4,2.5-3.3c0-1.9-1.1-3.3-2.5-3.3H-53.2c-1.4,0-2.5,1.5-2.5,3.3c0,1.8,1.1,3.3,2.5,3.3 H56.4z" /> </g>

              </svg>
            )}{" "}
            <span className="ps-3 text-uppercase">
              {learnMoreLoader ? "RESETTING..." : "LEARN MORE"}
            </span>
          </button>
        </div>
      </div>
      <ConfirmLogOutDialog
        open={open}
        onClose={handleCloseDialog}
        onConfirm={handleConfirmLogout}
        logoutLoader={logoutLoader}
      />
    </div>
  );
};
Drawer.propTypes = {
  show: PropTypes.bool.isRequired,
  onHide: PropTypes.func.isRequired,
};
export default Drawer;
