import { Mo<PERSON>, Button } from "react-bootstrap";
import PropTypes from "prop-types";
import AboutUs from "../../../pages/aboutUs";

const AboutModal = ({ show, onHide }) => {
  return (
    <Modal show={show} onHide={onHide} centered>
      <Modal.Header className="modal-header-fixed" closeButton>
        <Modal.Title>About Us</Modal.Title>
      </Modal.Header>
      <Modal.Body className="modal-body-scrollable">
        <AboutUs />
      </Modal.Body>
      <Modal.Footer className="modal-footer-fixed">
        <Button
          variant="btn btn-danger"
          onClick={onHide}
          className="fw-600"
          style={{ width: "25%" }}
        >
          CLOSE
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
AboutModal.propTypes = {
  show: PropTypes.bool.isRequired,
  onHide: PropTypes.func.isRequired,
};
export default AboutModal;
