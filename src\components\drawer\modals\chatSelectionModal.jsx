import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import {
  Box,
  Avatar,
  Typography,
  Stack,
  IconButton,
  Tooltip,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import SendIcon from "@mui/icons-material/Send";
import StorefrontIcon from "@mui/icons-material/Storefront"; // Dealer Icon
import PersonIcon from "@mui/icons-material/Person"; // MSR Icon
import SupportAgentIcon from "@mui/icons-material/SupportAgent"; // Advisor Icon
import { useNavigate } from "react-router-dom";
import PropTypes from "prop-types";
import { useUserData } from "../../../pages/UserDataContext";
import { useState } from "react";
import MemberTwoChatModal from "./memberTwoChatModal";
import MemberTwoAdvisoryChatModal from "./memberTwoAdvisoryChatModal";
import { useNotification } from "../../../context/NotificationContext"; // Import the custom hook
import { useLocation } from "react-router-dom";
import { memberBasedLoanOfferActionUtil } from "../../../util/memberBasedLoanOfferActionUtil";
import { useDispatch } from "react-redux";

const ChatSelectionModal = ({ open, close }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { userData } = useUserData();
  const [isCUModalOpen, setIsCUModalOpen] = useState(false);
  const [isAdvisoryModalOpen, setIsAdvisoryModalOpen] = useState(false);
  const isMobile = window.innerWidth < 768;
  const { hasNewMessage, unreadMessages, clearUnreadMessage } =
    useNotification();
  const location = useLocation();
  const currentPathName = location.pathname;
  const getTooltipMessage = () => {
    const memberType = Number(userData?.member_type);
    switch (memberType) {
      case 1:
        return "Chat with our Credit Union support";
      case 2:
        return "Chat with our Credit Union support";
      case 3:
        return "This link will take you to a Credit Union Application portal";
      default:
        return "Chat with our Credit Union support";
    }
  };
  const cuLoansURL = import.meta.env.VITE_CU_VEHICLE_LOANS_URL; // Credit Union Loans URL
  const customerServiceData = [
    {
      name: "Chat with Dealer",
      color: "green",
      icon: <StorefrontIcon />,
      iconColor: "#FF9800",
      path: "/conversations",
      tooltip: "Chat with Dealer",
      msg_type: "ask_dealer",
      action: () => {
        const token = localStorage.getItem("token");
        const memberType = Number(userData?.member_type);
        if (memberType === 2) {
          let forceUnderProcess = false;
          try {
            const isAcceptedRaw = localStorage.getItem('is_loan_offer_accepted');
            // Accepts both boolean true and string 'true'
            if (isAcceptedRaw === 'true' || isAcceptedRaw === true) {
              forceUnderProcess = true;
            }
          } catch (e) {
            console.log('Error while calling util:', e);
          }
          memberBasedLoanOfferActionUtil(dispatch, token, forceUnderProcess);
          return;
        }
        navigate("/conversations");
        close();
      },
    },
    {
      name: "Chat with Credit Union",
      color: "green",
      icon: <PersonIcon />,
      iconColor: "#2196F3",
      msg_type: "askMSR",
      path: "/cu-conversations",
      action: () => {
        const memberType = Number(userData?.member_type);
        if (memberType === 2) {
          setIsCUModalOpen(true); // Open nested modal
        } else if (memberType === 3) {
          window.open(cuLoansURL, "_blank", "noopener,noreferrer");
        } else {
          navigate("/cu-conversations");
          close();
        }
      },
      tooltip: getTooltipMessage(),
    },
    {
      name: "Chat with Advisor",
      color: "green",
      icon: <SupportAgentIcon />,
      iconColor: "#4CAF50",
      msg_type: "askAdvisor",
      path: "/fpadvisor-conversations",
      action: () => {
        const memberType = Number(userData?.member_type);
        if (memberType === 2) {
          setIsAdvisoryModalOpen(true); // Open nested modal
        } else if (memberType === 3) {
          window.open(cuLoansURL, "_blank", "noopener,noreferrer");
        } else {
          navigate("/fpadvisor-conversations");
          close();
        }
      },
      tooltip:
        Number(userData?.member_type) === 3
          ? "You must apply for a loan to speak with a FastPass Advisor"
          : "Chat with Advisor",
    },
  ];

  const handleNavigation = (path, action, msg_type) => {
    clearUnreadMessage(msg_type);
    close(); // Close the modal before navigating
    if (action) {
      action(); // Execute the function instead of navigating
    } else {
      close(); // Close the modal before navigating
      navigate(path);
    }
  };

  return (
    <>
      {/* First Modal */}
      <Modal
        show={open && (!isCUModalOpen || !isAdvisoryModalOpen)}
        onHide={close}
        centered
      >
        <Modal.Header
          style={{
            backgroundColor: "#4891ff",
            color: "white",
            textAlign: "center",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              width: "100%",
              flexDirection: "column",
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                width: "100%",
                position: "relative",
              }}
            >
              <Modal.Title style={{ flexGrow: 1, fontWeight: "bold" }}>
                {isMobile ? (
                  <>
                    Need Assistance? <br /> Start a Chat
                  </>
                ) : (
                  "Need Assistance? Start a Chat"
                )}
              </Modal.Title>
              <IconButton>
                <CloseIcon onClick={close} sx={{ color: "white" }} />
              </IconButton>
            </Box>
            <Typography fontSize={12} fontWeight="light" mt={1}>
              Hi! Click for communication via Chat :)
            </Typography>
          </Box>
        </Modal.Header>

        <Modal.Body>
          {customerServiceData.map((agent, index) => (
            <Tooltip key={index} title={agent?.tooltip || ""} arrow>
              <Stack
                direction="row"
                alignItems="center"
                justifyContent="space-between"
                sx={{
                  backgroundColor: index % 2 === 0 ? "#f5f5f5" : "#FAFAFA",
                  borderRadius: 1,
                  p: 1,
                  mb: 1,
                  cursor: "pointer",
                  transition: "0.3s",
                  "&:hover": { backgroundColor: "#e0e0e0" },
                  position: "relative",
                }}
                onClick={() =>
                  handleNavigation(agent.path, agent.action, agent.msg_type)
                }
              >
                <Box
                  sx={{
                    width: 4,
                    backgroundColor: agent.iconColor,
                    height: "100%",
                    position: "absolute",
                    left: 0,
                    top: 0,
                    bottom: 0,
                  }}
                />
                <Stack
                  direction="row"
                  alignItems="center"
                  spacing={1.5}
                  sx={{ pl: 1.5 }}
                >
                  <Box sx={{ position: "relative" }}>
                    <Avatar sx={{ bgcolor: agent.iconColor }}>
                      {agent.icon}
                    </Avatar>
                    {unreadMessages.includes(agent.msg_type) && agent.path !== currentPathName && (
                      <Box
                        sx={{
                          position: "absolute",
                          top: 0,
                          right: 0,
                          width: "10px",
                          height: "10px",
                          borderRadius: "50%",
                          backgroundColor: "red",
                          border: "2px solid white",
                        }}
                      />
                    )}
                  </Box>

                  <Box>
                    <Typography fontWeight="bold">{agent.name}</Typography>
                    {isMobile && (
                      <Typography fontSize={12} color={agent.color}>
                        {agent.tooltip}
                      </Typography>
                    )}
                  </Box>
                </Stack>
                <SendIcon sx={{ ml: 1, color: "#4891ff" }} />
              </Stack>
            </Tooltip>
          ))}
        </Modal.Body>

        <Modal.Footer>
          <Button variant="danger" onClick={close}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Second Nested Modal */}
      <MemberTwoChatModal
        open={isCUModalOpen}
        close={() => setIsCUModalOpen(false)}
        memberType={userData?.member_type}
      />
      <MemberTwoAdvisoryChatModal
        open={isAdvisoryModalOpen}
        close={() => setIsAdvisoryModalOpen(false)}
        memberType={userData?.member_type}
      />
    </>
  );
};

ChatSelectionModal.propTypes = {
  open: PropTypes.bool.isRequired,
  close: PropTypes.func.isRequired,
};

export default ChatSelectionModal;
