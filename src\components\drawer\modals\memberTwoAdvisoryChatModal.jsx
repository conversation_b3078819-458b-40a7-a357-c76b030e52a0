import { Box, IconButton, Stack, styled, Typography, useTheme } from '@mui/material';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import CloseIcon from "@mui/icons-material/Close";
import PropTypes from "prop-types";
import { useMediaQuery } from 'react-responsive';
// import { useNavigate, useLocation  } from 'react-router-dom';
// import { useModal } from '../../../context/ModalContext';

const StyledButton = styled(Button)(({ isMobile }) => ({
    borderRadius: "8px",
    fontWeight: "600",
    fontSize: "14px",
    padding: "12px",
    backgroundColor: "#4891FF",
    color: "#fff",
    width: isMobile ? "100%" : "75%",
    textTransform: "none",

    "&:hover": {
        backgroundColor: "#357AE8",
    },

    "&.Mui-disabled": {
        backgroundColor: "#4891FF",
        color: "#fff",
        opacity: 0.6,
        cursor: "not-allowed",
    },
}));

const MemberTwoAdvisoryChatModal = ({ open, close }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery({ query: '(max-width: 767px)' });
    const cuOfferURL = import.meta.env.VITE_CU_LOAN_OFFERS_URL;
    //const { setShowChatSelectionModal } = useModal();

    const handleButtonClick = () => {
        window.open(cuOfferURL, "_blank", "noopener,noreferrer");
    };

    // const handleNavigation = () => {
    //     close();
    //      // Check if the current route is '/cu-conversations'
    //      if (location.pathname !== '/cu-conversations') {
    //         setShowChatSelectionModal(false); // Close the modal if not on the specified route
    //     }
    //     navigate("/cu-conversations");
    // };

    return (
        <Dialog
            open={open}
            onClose={close}
            aria-labelledby="responsive-dialog-title"
            maxWidth="xs"
            fullWidth
            sx={{ "& .MuiDialog-paper": { borderRadius: isMobile ? 2 : 3, padding: isMobile ? "16px" : "24px" } }}
        >
            <DialogActions sx={{ position: "absolute", top: 8, right: 8 }}>
                <IconButton onClick={close} sx={{ color: theme.palette.grey[600], p: isMobile ? "6px" : "8px" }}>
                    <CloseIcon fontSize={isMobile ? "small" : "medium"} />
                </IconButton>
            </DialogActions>

            <DialogContent sx={{ p: 0, pt: 5 }}>
                <Stack
                    direction="row"
                    alignItems="center"
                    justifyContent="space-between"
                    sx={{
                        backgroundColor: "#F5F5F5",
                        borderRadius: 2,
                        p: 1.5,
                        mb: 2,
                        cursor: "pointer",
                        transition: "0.3s",
                        "&:hover": { backgroundColor: "#e0e0e0" },
                        position: "relative",
                    }}
                >
                    <Box
                        sx={{
                            width: 4,
                            backgroundColor: "#4CAF50",
                            height: "100%",
                            position: "absolute",
                            left: 0,
                            top: 0,
                            bottom: 0,
                        }}
                    />
                    <Stack direction="row" alignItems="center" spacing={1.5} sx={{ pl: 1.5 }}>
                        <Typography fontWeight="bold" fontSize={isMobile ? "14px" : "16px"}>
                            You must accept your loan offer to speak with a FastPass Advisor
                        </Typography>
                    </Stack>
                </Stack>
            </DialogContent>

            <DialogActions sx={{ justifyContent: "center", pb: isMobile ? 1 : 2 }}>
                <StyledButton onClick={handleButtonClick} autoFocus>
                    Accept the Loan Offer
                </StyledButton>
            </DialogActions>
        </Dialog>
    );
};

MemberTwoAdvisoryChatModal.propTypes = {
    open: PropTypes.bool,
    close: PropTypes.func,
};

export default MemberTwoAdvisoryChatModal;
