import Box from "@mui/material/Box";
import CssBaseline from "@mui/material/CssBaseline";
import BottomNavigation from "@mui/material/BottomNavigation";
import BottomNavigationAction from "@mui/material/BottomNavigationAction";
import Paper from "@mui/material/Paper";
import { styled } from "@mui/system";
import MenuIcon from "@mui/icons-material/Menu";
import Icon from "../../icon";
import Drawer from "../drawer/drawer";
import { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import ChatSelectionModal from "../drawer/modals/chatSelectionModal";
import { useNotification } from "../../context/NotificationContext"; // Import the custom hook
import { useModal } from "../../context/ModalContext"; // Import the modal context

const ScrollableBottomNavigation = styled(Box)(({ theme }) => ({
  display: "flex",
  overflowX: "auto",
  whiteSpace: "nowrap",
  [theme.breakpoints.up("md")]: {
    justifyContent: "center", // Center items on medium and larger screens
  },
}));

const CenteredBottomNavigation = styled(BottomNavigation)(({ theme }) => ({
  display: "flex",
  width: "100%",
  alignItems: "center", // Center items vertically
  [theme.breakpoints.down("sm")]: {
    justifyContent: "flex-start", // Align items to the start on smaller screens
  },
}));

export default function FixedBottomNavigation() {
  const [value, setValue] = useState(0);
  const ref = useRef(null);
  const navigate = useNavigate();
  const location = useLocation();
  const [showModal, setShowModal] = useState(false);
  const token = localStorage.getItem("token");
  const [showChatSelectionModal, setShowChatSelectionModal] = useState(false);

  // Import the notification state from context
  const { hasNewMessage, setHasNewMessage, unreadMessages, setUnreadMessages } = useNotification();
  const currentPathName = location.pathname;

  // Effect to clear unread messages when the user is in the current path
  useEffect(() => {
    // Check if any unread messages are for the current path
    const isOnCurrentPath = unreadMessages.some((msgType) => {
      const chatPath = msgTypePathMap.find(
        (item) => item.msg_type === msgType
      )?.path;
      return chatPath === currentPathName;
    });

    // If on the current path, set unread messages to false
    if (isOnCurrentPath) {
      setUnreadMessages([]); // Reset unread messages if on the current path
    }
  }, [unreadMessages, currentPathName, setUnreadMessages]);

  useEffect(() => {
    ref.current.ownerDocument.body.scrollTop = 0;
  }, [value]);

  useEffect(() => {
    if (!token) {
      setValue(-1); // Token doesn't exist
      return;
    }

    switch (location.pathname) {
      case "/landing":
        setValue(0);
        break;
      case "/specific-search":
        setValue(1);
        break;
      case "/my-garage":
        setValue(2);
        break;
      case "/chat":
        setValue(3);
        break;
      default:
        setValue(0);
    }
  }, [location.pathname, token]); // Add token to dependencies

  const handleShow = () => setShowModal(true);
  const handleClose = () => setShowModal(false);
  const handleChatSelectionShow = () => setShowChatSelectionModal(true);
  const handleChatSelectionModalClose = () => setShowChatSelectionModal(false);
  const msgTypePathMap = [
    {
      path: "/conversations",
      msg_type: "ask_dealer",
    },
    {
      path: "/cu-conversations",
      msg_type: "askMSR",
    },
    {
      path: "/fpadvisor-conversations",
      msg_type: "askAdvisor",
    },
  ];
  const handleNavigation = (newValue) => {
    if (!token) {
      switch (newValue) {
        case 0:
          navigate("/landing");
          break;
        case 1:
          navigate("/specific-search");
          break;
        case 2:
          navigate("/my-garage");
          break;
        case 3:
          setShowChatSelectionModal(true);
          handleChatSelectionShow();
          //setHasNewMessage(false); // Reset the new message notification when the user goes to chat
          break;
        case 4:
          handleShow();
          break;
        default:
          navigate("/");
      }
    } else {
      // Token exists
      switch (newValue) {
        case 0:
          navigate("/landing");
          break;
        case 1:
          navigate("/specific-search");
          break;
        case 2:
          navigate("/my-garage");
          break;
        case 3:
          setShowChatSelectionModal(true);
          handleChatSelectionShow();
          // setHasNewMessage(false); // Reset the new message notification when the user goes to chat
          break;
        case 4:
          handleShow();
          break;
        default:
          navigate("/landing");
      }
    }
  };

  return (
    <Box sx={{ pb: 7 }} ref={ref}>
      <CssBaseline />
      <Paper
        sx={{
          position: "fixed",
          bottom: 0,
          left: 0,
          right: 0,
          borderTop: "2px solid #4891FF",
          height: "80px",
          display: "grid",
          alignItems: "center",
          zIndex: 1,
        }}
        elevation={3}
      >
        <ScrollableBottomNavigation>
          <CenteredBottomNavigation
            showLabels
            value={value}
            onChange={(event, newValue) => {
              handleNavigation(newValue);
            }}
          >
            <BottomNavigationAction
              sx={{
                minWidth: "56px",
                "& .MuiBottomNavigationAction-label": { fontWeight: "bold" },
              }} // Bold label
              label="HOME"
              icon={<Icon icon="home" size={30} />}
            />
            <BottomNavigationAction
              sx={{
                minWidth: "56px",
                "& .MuiBottomNavigationAction-label": { fontWeight: "bold" },
              }}
              label="SEARCH"
              icon={<Icon icon="searching-car" size={30} />}
            />
            <BottomNavigationAction
              sx={{
                minWidth: "56px",
                "& .MuiBottomNavigationAction-label": { fontWeight: "bold" },
              }}
              label="GARAGE"
              icon={<Icon icon="home-garage" size={30} />}
            />
            <BottomNavigationAction
              sx={{
                minWidth: "56px",
                "& .MuiBottomNavigationAction-label": { fontWeight: "bold" },
                position: "relative", // Allow positioning the dot on top of the icon
              }}
              label="CHAT"
              icon={
                <div style={{ position: "relative" }}>
                  <Icon icon="chat-square" size={30} />
                  {unreadMessages.some((msgType) => {
                    const chatPath = msgTypePathMap.find(
                      (item) => item.msg_type === msgType
                    )?.path;
                    return chatPath && chatPath !== currentPathName; // Only show dot if message is from a different chat type
                  }) && (
                      <div
                        style={{
                          position: "absolute",
                          top: 0,
                          right: 0,
                          width: "10px",
                          height: "10px",
                          borderRadius: "50%",
                          backgroundColor: "red",
                        }}
                      />
                    )}
                </div>
              }
            />
            <BottomNavigationAction
              sx={{
                minWidth: "56px",
                "& .MuiBottomNavigationAction-label": { fontWeight: "bold" },
              }}
              label="MENU"
              icon={<MenuIcon style={{ fontSize: 30, color: "#4891FF" }} />}
            />
          </CenteredBottomNavigation>
        </ScrollableBottomNavigation>
      </Paper>
      <Drawer show={showModal} onHide={handleClose} />
      <ChatSelectionModal
        open={showChatSelectionModal}
        close={handleChatSelectionModalClose}
      />
    </Box>
  );
}
