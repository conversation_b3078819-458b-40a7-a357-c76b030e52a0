import { ImageBaseURL } from '../../config';
import { useMediaQuery } from 'react-responsive';

const Header = () => {
  const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' });
  const isMobile = useMediaQuery({ maxWidth: 767 });
  let logoClassName;
  if (isTabletOrMobile) {
    if (isMobile) {
      logoClassName = "header-top-logo-mobile";
    } else {
      logoClassName = "header-top-logo-tab";
    }
  } else {
    logoClassName = "header-top-logo";
  }
  
  return (
    <header>
      <div>
        <a
          href="/landing"
          style={{
            display: 'inline-block',
            cursor: 'pointer'
          }}
        >
          <img
            src={ImageBaseURL + "fp_logo.png"}
            className={logoClassName}
            alt="header"
          />
        </a>
      </div>
    </header>
  );
};

export default Header;