import Header from "./header";
import Footer from "./footer";
import PropTypes from "prop-types";
const MasterLayout = ({ children }) => {
  return (
    <div className="layout">
      <Header />
      <div className="content">
        {children}
      </div>
      <Footer className="footer" />
    </div>
  );
};
MasterLayout.propTypes = {
  children: PropTypes.node.isRequired,
};
export default MasterLayout;
