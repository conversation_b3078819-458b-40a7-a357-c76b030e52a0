import { <PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON> } from "react-bootstrap";
import PropTypes from "prop-types";
import { FaCheck, FaTimes } from "react-icons/fa";

const MemberBasedLoanOfferModal = ({
  show,
  onHide,
  onAccept,
  loading,
  underProcess
}) => {
  return (
    <Modal show={show} onHide={onHide} centered>
      <Modal.Header className="conf-modal-header-fixed"></Modal.Header>
      <Modal.Body className="conf-modal-body-scrollable">
        {underProcess ? (
          <>
            <div className="conf-modal-question" style={{ textAlign: "center", fontWeight: 600 }}>
              Your acceptance has been submitted to the Credit Union for approval. Please note that response times may vary. We thank you for your patience during this process.
            </div>
            <div className="conf-modal-buttons">
              <Button
                className="custom-button"
                onClick={onHide}
              >
                OK
              </Button>
            </div>
          </>
        ) : (
          <>
            <div className="conf-modal-question">
              To continue you must accept your loan. Do you wish to accept your loan?
            </div>
            <div className="conf-modal-buttons">
              <Button
                className={`custom-button no-button loan-offer-modal-cancel-btn${loading ? ' disabled' : ''}`}
                onClick={onHide}
                disabled={loading}
              >
                <FaTimes className="conf-modal-icon" /> Cancel
              </Button>
              <Button
                className={`custom-button yes-button loan-offer-modal-accept-btn${loading ? ' disabled' : ''}`}
                onClick={onAccept}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Spinner className="conf-modal-icon" animation="border" size="sm" />
                    <span>Accepting...</span>
                  </>
                ) : (
                  <>
                    <FaCheck className="conf-modal-icon" />
                    <span>Accept</span>
                  </>
                )}
              </Button>
            </div>
          </>
        )}
      </Modal.Body>
    </Modal>
  );
};

MemberBasedLoanOfferModal.propTypes = {
  show: PropTypes.bool.isRequired,
  onHide: PropTypes.func.isRequired,
  onAccept: PropTypes.func.isRequired,
  loading: PropTypes.bool,
  underProcess: PropTypes.bool
};

export default MemberBasedLoanOfferModal;
