import { useEffect, useState } from "react";
import { Workbox } from "workbox-window";
import InfoIcon from '@mui/icons-material/Info';

const UpdateButton = () => {
  const [waitingWorker, setWaitingWorker] = useState(null);

  useEffect(() => {
    if ("serviceWorker" in navigator) {
      const wb = new Workbox("/sw.js");

      wb.addEventListener("waiting", (event) => {
        setWaitingWorker(event.sw);
      });

      wb.register()
        .then(registration => {
          console.log('Service Worker registered:', registration);
        })
        .catch(error => {
          console.log('Service Worker registration failed:', error);
        });
    }
  }, []);

  const updateServiceWorker = () => {
    if (waitingWorker) {
      waitingWorker.postMessage({ type: "SKIP_WAITING" });
      waitingWorker.addEventListener('statechange', () => {
        if (waitingWorker.state === 'activated') {
          window.location.reload();
        }
      });
    }
  };

  return (
    <>
      {waitingWorker && (
        <div className="update-button-container">
          <div className="blur-background"></div>
          <div className="notification">
            <span className="notification-text">
              <InfoIcon style={{ color:"#4891ff" }}/>&nbsp;<b>A new version of FastPass App is available</b>
            </span>
            <button onClick={updateServiceWorker}>UPDATE</button>
          </div>
        </div>
      )}
    </>
  );
};

export default UpdateButton;
