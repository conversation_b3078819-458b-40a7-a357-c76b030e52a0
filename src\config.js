// src/config.js
export const ImageBaseURL = import.meta.env.VITE_ASSETS_BASE_URL; // For image base URL
export const FuelTypes = [
    "Diesel",
    "Unleaded",
    "Unleaded / E85",
    "Premium Unleaded",
    "Biodiesel",
    "Electric / Premium Unleaded",
    "Premium Unleaded / Unleaded",
    "Hydrogen",
    "Electric",
    "Electric / Unleaded",
    "E85 / Unleaded",
    "E85",
    "E85 / Premium Unleaded"
]; // For fuel type dropdown
export const EvFuelTypes = [
    "Electric / Premium Unleaded",
    "Electric",
    "Electric / Unleaded",
]; // For EV fuel type values
export const VehicleColors = [
    { title: 'Black', colorCode: '#000000' },
    { title: 'White', colorCode: '#FFFFFF' },
    { title: 'Silver', colorCode: '#C0C0C0' },
    { title: 'Gray', colorCode: '#808080' },
    { title: 'Red', colorCode: '#FF0000' },
    { title: 'Blue', colorCode: '#0000FF' },
    { title: 'Green', colorCode: '#008000' },
    { title: 'Brown', colorCode: '#A52A2A' },
    { title: 'Beige', colorCode: '#F5F5DC' },
    { title: 'Yellow', colorCode: '#FFFF00' },
    { title: 'Orange', colorCode: '#FFA500' },
    { title: 'Gold', colorCode: '#FFD700' },
    { title: 'Purple', colorCode: '#800080' },
    { title: 'Burgundy', colorCode: '#800000' },
    { title: 'Teal', colorCode: '#008080' }
]; // For vehicle color dropdown
export const DistanceOptions = [
    { label: "10 miles", value: 10 },
    { label: "20 miles", value: 20 },
    { label: "50 miles", value: 50 },
    { label: "100 miles", value: 100 },
    { label: "250 miles", value: 250 },
]; // For distance dropdown
export const CarLookingOptions = [
    { label: "New or Used", value: "new or used" },
    { label: "New", value: "new" },
    { label: "Used", value: "used" },
]; // For car looking dropdown
