import { useState, useEffect, useCallback, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import { fetchSpecificConversations, getChatHistoryList, messsageFeedback, sendUserQuery } from "../../../store/apps/AIChat";
import { useNavigate, useLocation } from "react-router-dom";
import { selectLoggedInUserData } from "../../../store/apps/user";
import { getcarInventory, getGarageVehicleVins } from "../../../store/apps/car";

const UseAiChatApi = () => {
  const dispatch = useDispatch(); // Redux dispatch
  const [showBot, setShowBot] = useState(false); // set AI bot visibility
  const [query, setQuery] = useState(""); // store user's message
  const [userMessage, setUserMessage] = useState(""); // store user's message
  const [aiQueryResponse, setAiQueryResposne] = useState(""); // store AI response against user's query
  const [conversationId, setConversationId] = useState(null);
  const [loading, setLoading] = useState(false); // local loading state
  const [loadMoreLoading, setLoadMoreLoading] = useState(false); // loading state for load more
  const [searchText, setSearchText] = useState(""); // search input state
  const [conversation, setConversation] = useState([]);
  const [showConversationInterface, setShowConversationInterface] = useState(false); // flag to show conversation interface
  const [selectedChatId, setSelectedChatId] = useState(null); // store selected chat id
  const [loadSpecificConversation, setLoadSpecificConversation] = useState(false); // loading state for load more
  const [hasInitialDataLoaded, setHasInitialDataLoaded] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false); // Track load more operations
  const [renderKey, setRenderKey] = useState(0); // Force re-render trigger
  const [feedbackRes, setFeedbackRes] = useState('');
  const [feedbackFlagRes, setFeedbackFlagRes] = useState(null);
  const [feedbackLoading, setFeedbackLoading] = useState(false);
  const loadMoreLockRef = useRef(false); // Ref-based lock to prevent duplicate calls
  const lastLoadMoreTimeRef = useRef(0); // Track last load more time for debouncing
  // Add a state to track vins in garage for current chat
  const [garageVins, setGarageVins] = useState([]);
  // Add a callback to set garageAdded state in conversation interface
  const [garageAdded, setGarageAdded] = useState({}); // { [row_id]: true }
  // Add state for pagination in the hook
  const [individualChatPage, setIndividualChatPage] = useState(1);
  const [individualChatPageSize] = useState(10); // You can make this configurable
  const [hasMoreIndividualChat, setHasMoreIndividualChat] = useState(false);

  // Access user first name, latitude and longitude from Redux
  const loggedInUserData = useSelector(selectLoggedInUserData);

  let userLat = loggedInUserData?.data?.[0]?.latitude;
  let userLong = loggedInUserData?.data?.[0]?.longitude;
  let userFirstName = loggedInUserData?.data?.[0]?.member_first_name || 'User';
  let userLastName = loggedInUserData?.data?.[0]?.member_last_name || '';
  let userPurchasePower = loggedInUserData?.data?.[0]?.user_total_purchase_power;
  let userLoanAmount = loggedInUserData?.data?.[0]?.preapproved_max_amt;
  let userZipCode = loggedInUserData?.data?.[0]?.zip_code;

  // set showBot to true when user data is available
  useEffect(() => {
    if (loggedInUserData &&
      loggedInUserData.data &&
      loggedInUserData.data.length > 0) {
      setShowBot(true);
    }
  }, [loggedInUserData]);

  const [chatHistoryListParams, setChatHistoryListParams] = useState({
    page: 1,
    page_size: 10,
    search: null,
  });

  const [initalCondition, setInitialcondition] = useState(true);
  const [hasMoreData, setHasMoreData] = useState(true);

  const [value, setValue] = useState({
    today: { message: [] },
    yesterday: { message: [] },
    this_week: { message: [] },
    this_month: { message: [] },
    older: { message: [] },
  });

  // Store original chat history data (before search)
  const [originalChatHistory, setOriginalChatHistory] = useState({
    today: { message: [] },
    yesterday: { message: [] },
    this_week: { message: [] },
    this_month: { message: [] },
    older: { message: [] },
  });
  const [totalChatCount, setTotalChatCount] = useState(0);

  function categorizeMessagesByDate(data) {
    const result = {
      today: { message: [] },
      yesterday: { message: [] },
      this_week: { message: [] },
      this_month: { message: [] },
      older: { message: [] },
    };

    const now = new Date();

    const startOfToday = new Date(now);
    startOfToday.setHours(0, 0, 0, 0);

    const startOfYesterday = new Date(startOfToday);
    startOfYesterday.setDate(startOfYesterday.getDate() - 1);

    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay()); // Sunday as start
    startOfWeek.setHours(0, 0, 0, 0);

    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    data.forEach(item => {
      const createdAt = new Date(item.created_at);

      if (createdAt >= startOfToday) {
        result.today.message.push(item);
      } else if (createdAt >= startOfYesterday && createdAt < startOfToday) {
        result.yesterday.message.push(item);
      } else if (createdAt >= startOfWeek) {
        result.this_week.message.push(item);
      } else if (createdAt >= startOfMonth) {
        result.this_month.message.push(item);
      } else {
        result.older.message.push(item);
      }
    });

    return result;
  }

  // Move fetchChatHistory out of useEffect so it can be called directly
  const fetchChatHistory = useCallback(async (highlightConversationId = null) => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await dispatch(
        getChatHistoryList({
          page: chatHistoryListParams.page,
          page_size: chatHistoryListParams.page_size,
          search: chatHistoryListParams.search || null,
          token,
        })
      );

      if (response.payload) {
        // Handle both cases: data exists or data is empty/null
        const apiData = response.payload.data || [];
        const updatedData = categorizeMessagesByDate(apiData);
        const newData = {
          today: { message: [...updatedData.today.message] },
          yesterday: { message: [...updatedData.yesterday.message] },
          this_week: { message: [...updatedData.this_week.message] },
          this_month: { message: [...updatedData.this_month.message] },
          older: { message: [...updatedData.older.message] },
        };

        setValue(newData);
        // Store original data for search reset functionality
        setOriginalChatHistory(newData);
        // Calculate total chat count
        setTotalChatCount(apiData.length);
        // Check if there's more data available
        setHasMoreData(apiData.length === chatHistoryListParams.page_size);
        // Mark that initial data has been loaded
        setHasInitialDataLoaded(true);
        // Highlight the new conversation if provided
        if (highlightConversationId) {
          setSelectedChatId(highlightConversationId);
        }
      } else {
        // Handle case where API returns no payload
        const emptyData = {
          today: { message: [] },
          yesterday: { message: [] },
          this_week: { message: [] },
          this_month: { message: [] },
          older: { message: [] },
        };
        setValue(emptyData);
        setOriginalChatHistory(emptyData);
        setTotalChatCount(0);
        setHasMoreData(false);
      }
    } catch (error) {
      console.error("Error while fetching chat history:", error);
    } finally {
      setLoading(false);
    }
  }, [dispatch, chatHistoryListParams.page_size, chatHistoryListParams.search]);

  // Create a separate function for restoring original chat history
  const restoreOriginalChatHistory = useCallback(() => {
    setValue(originalChatHistory);
    setChatHistoryListParams(prev => ({
      ...prev,
      search: null,
      page: 1
    }));
    setHasMoreData(true);
  }, [originalChatHistory]);

  // Debounced search effect
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchText.trim() === "") {
        setValue(originalChatHistory);
        setChatHistoryListParams(prev => {
          return {
            ...prev,
            search: null,
            page: 1
          };
        });
        setHasMoreData(true);
      } else {
        // Perform search
        const fetchSearchResults = async () => {
          try {
            setLoading(true);
            const token = localStorage.getItem('token');
            const response = await dispatch(
              getChatHistoryList({
                page: 1, // Always start from page 1 for search
                page_size: chatHistoryListParams.page_size,
                search: searchText,
                token,
              })
            );

            if (response.payload) {
              // Handle both cases: data exists or data is empty/null
              const apiData = response.payload.data || [];
              const updatedData = categorizeMessagesByDate(apiData);

              // Reset data for search results
              setValue({
                today: { message: updatedData.today.message },
                yesterday: { message: updatedData.yesterday.message },
                this_week: { message: updatedData.this_week.message },
                this_month: { message: updatedData.this_month.message },
                older: { message: updatedData.older.message },
              });

              // Update search parameter and reset page
              setChatHistoryListParams(prev => ({
                ...prev,
                search: searchText,
                page: 1
              }));

              // Calculate total chat count for search
              setTotalChatCount(apiData.length);

              // Check if there's more data available
              setHasMoreData(apiData.length === chatHistoryListParams.page_size);
            } else {
              // Handle case where API returns no payload for search
              setValue({
                today: { message: [] },
                yesterday: { message: [] },
                this_week: { message: [] },
                this_month: { message: [] },
                older: { message: [] },
              });
              setChatHistoryListParams(prev => ({
                ...prev,
                search: searchText,
                page: 1
              }));
              setTotalChatCount(0);
              setHasMoreData(false);
            }
          } catch (error) {
            console.error("Error while fetching search results:", error);
          } finally {
            setLoading(false);
          }
        };

        fetchSearchResults();
      }
    }, 500); // 500ms debounce

    return () => clearTimeout(delayedSearch);
  }, [searchText, dispatch, chatHistoryListParams.page_size]);

  const loadMoreChatHistory = async () => {
    const operationId = Math.random().toString(36).substring(2, 11);
    try {
      // Prevent multiple simultaneous calls using both state and ref
      if (loadMoreLoading || isLoadingMore || loadMoreLockRef.current) {
        return;
      }

      // Check for rapid clicking (debounce)
      const now = Date.now();
      if (now - lastLoadMoreTimeRef.current < 1000) { // 1 second debounce
        return;
      }
      lastLoadMoreTimeRef.current = now;

      // Set ref lock immediately
      loadMoreLockRef.current = true;
      setLoadMoreLoading(true);
      setIsLoadingMore(true);
      const nextPage = chatHistoryListParams.page + 1;
      const token = localStorage.getItem('token');
      const response = await dispatch(
        getChatHistoryList({
          page: nextPage,
          page_size: chatHistoryListParams.page_size,
          search: chatHistoryListParams.search || null,
          token,
        })
      );

      if (response.payload) {
        // Handle both cases: data exists or data is empty/null
        const apiData = response.payload.data || [];
        // Update page immediately after successful API call
        setChatHistoryListParams(prev => {
          return { ...prev, page: nextPage };
        });
        const updatedData = categorizeMessagesByDate(apiData);
        const newData = {
          today: { message: [...value.today.message, ...updatedData.today.message] },
          yesterday: { message: [...value.yesterday.message, ...updatedData.yesterday.message] },
          this_week: { message: [...value.this_week.message, ...updatedData.this_week.message] },
          this_month: { message: [...value.this_month.message, ...updatedData.this_month.message] },
          older: { message: [...value.older.message, ...updatedData.older.message] },
        };

        // Batch state updates to prevent race conditions
        // Set the merged data immediately - no setTimeout to prevent interference
        setValue(newData);

        // Update total count to reflect cumulative loaded messages
        const totalMessages = newData.today.message.length +
          newData.yesterday.message.length +
          newData.this_week.message.length +
          newData.this_month.message.length +
          newData.older.message.length;
        setTotalChatCount(totalMessages);

        // Force re-render
        setRenderKey(prev => prev + 1);
        // If not searching, also update original chat history
        if (!chatHistoryListParams.search) {
          // Use setTimeout to update originalChatHistory after current execution
          setOriginalChatHistory(newData);
        }

        // Check if there's more data available
        setHasMoreData(apiData.length === chatHistoryListParams.page_size);
      } else {
        // Handle case where API returns no payload for load more
        setChatHistoryListParams(prev => ({ ...prev, page: nextPage }));
        setHasMoreData(false);
        loadMoreLockRef.current = false; //
      }
    } catch (error) {
      console.error("Error while loading more chat history:", error);
      loadMoreLockRef.current = false; //
    } finally {
      setLoadMoreLoading(false);
      setIsLoadingMore(false);
      loadMoreLockRef.current = false; //
    }
  };

  const sendInitialQuery = async (userQuery, convId = conversationId) => {
    try {
      // Store the user's message and switch to conversation interface
      setUserMessage(userQuery);
      // Add user message to conversation
      setConversation(prev => ([...prev, { sender: 'user', text: userQuery, timestamp: new Date().toISOString() }]));
      // Add placeholder AI message (loading)
      setConversation(prev => ([...prev, { sender: 'ai', text: '', timestamp: new Date().toISOString(), loading: true }]));
      // Use selectedChatId if present, otherwise convId
      const conversation_id_to_use = selectedChatId || convId || null;
      const isNewConversation = conversation_id_to_use === null;
      const queryPayload = {
        "conversation_id": conversation_id_to_use,
        "filters": null,
        "lat": userLat,
        "user_name": `${userFirstName} ${userLastName}`,
        "user_purchase_power_amount": userPurchasePower,
        "user_loan_approval_amount": userLoanAmount,
        "zipcode": userZipCode,
        "long": userLong,
        "page": 1,
        "page_size": 5,
        "query": userQuery,
        "sort_by": "price",
        "sort_order": "desc"
      }
      const token = localStorage.getItem('token');
      dispatch(
        sendUserQuery({
          ...queryPayload,
          token,
        })
      )
        .then((response) => {
          const aiResponse = response?.payload?.data;
          setAiQueryResposne(aiResponse?.message);
          // Update the placeholder AI message with the real response
          setConversation(prev => {
            // Find the last AI message with loading: true
            const lastAiIdx = [...prev].reverse().findIndex(msg => msg.sender === 'ai' && msg.loading);
            let updated = prev;
            if (lastAiIdx !== -1) {
              const idx = prev.length - 1 - lastAiIdx;
              updated = prev.map((msg, i) =>
                i === idx
                  ? {
                    ...msg,
                    text: aiResponse?.message || '',
                    loading: false,
                    timestamp: new Date().toISOString(),
                    message_id: aiResponse?.metadata?.message_id,
                    is_inventory_fetch: aiResponse?.metadata?.is_inventory_fetch,
                    inventory_vehicles: aiResponse?.vehicles,
                  }
                  : msg
              );
            } else {
              // Fallback: just append if not found
              updated = [...prev, {
                sender: 'ai',
                text: aiResponse?.message || '',
                timestamp: new Date().toISOString(),
                loading: false,
                message_id: aiResponse?.metadata?.message_id,
                is_inventory_fetch: aiResponse?.metadata?.is_inventory_fetch,
                inventory_vehicles: aiResponse?.vehicles,
              }];
            }
            // Check for vehicle cards condition
            const meta = aiResponse?.metadata;
            const vehicles = aiResponse?.vehicles;
            if (
              meta &&
              meta.is_inventory_fetch === 1 &&
              Number(meta.inventory_vehicles) > 0 &&
              Array.isArray(vehicles) &&
              vehicles.length > 0
            ) {
              // Append a special message for vehicle cards
              updated = [
                ...updated,
                {
                  sender: 'ai',
                  type: 'vehicle_cards',
                  vehicles: vehicles,
                  timestamp: new Date().toISOString(),
                },
              ];
            }
            return updated;
          });
          // Set conversationId if present in response
          if (aiResponse?.metadata?.conversation_id) {
            setConversationId(aiResponse.metadata.conversation_id);
          }
          // If this was a new conversation, refresh chat history and highlight the new chat
          if (isNewConversation && aiResponse?.metadata?.conversation_id) {
            fetchChatHistory(aiResponse.metadata.conversation_id);
          } else if (isNewConversation) {
            fetchChatHistory();
          }
        })
        .catch((error) => {
          console.error("Error while fetching chat history:", error);
        });
    } catch (error) {
      console.error("Error while fetching chat history:", error);
    }
  };

  // Update: Accept filters for debug replay (instead of aiResponseJson)
  const fetchAiDebugResponse = async (distance, convId = conversationId, filters = null) => {
    // Destructure values from filters object for clarity
    const {
      make,
      model,
      year,
      trim,
      vin,
      bodyType,
      exterior_colors,
      interior_colors,
      fuel_types,
      zip,
      sort_by,
      sort_order,
      price_max,
      price_min,
      type,
      yearRangeValue,
      yearValuesMatch,
      priceValuesMatch
    } = filters || {};

    // Helper to ensure value is always an array if not undefined/null
    const toArray = (val) => {
      if (val === undefined || val === null) return [];
      return Array.isArray(val) ? val : [val];
    };

    // First, get garage vins
    const token = localStorage.getItem('token');
    const vins = await dispatch(getGarageVehicleVins(token));
    let vinsArr = vins?.payload || [];

    // Now construct params for getcarInventory using destructured filter values
    const params = {
      token,
      bodyType: toArray(bodyType),
      vin: vin ?? "",
      carbrand: toArray(make),
      carmodel: toArray(model),
      caryear: toArray(year),
      latitude: userLat ?? null,
      longitude: userLong ?? null,
      trims: toArray(trim),
      exterior_colors: toArray(exterior_colors),
      interior_colors: toArray(interior_colors),
      fuel_types: toArray(fuel_types),
      distance: distance ?? null,
      zip: zip ?? userZipCode ?? null,
      page: 1,
      page_size: "6",
      sort_by: sort_by ?? "price",
      sort_order: sort_order ?? "desc",
      userAvailableAmount: price_max ?? userPurchasePower ?? userLoanAmount ?? null,
      userCarLookingOption: type ?? "new or used",
      priceRangeValue: [price_min ?? 0, price_max ?? (userPurchasePower || userLoanAmount)],
      yearRangeValue: toArray(yearRangeValue),
      yearValuesMatch: yearValuesMatch ?? false,
      priceValuesMatch: priceValuesMatch ?? true,
      addedCarinGarageVins: vinsArr ?? [],
    };
    const response = await dispatch(getcarInventory(params));
    return response;
  };

  // Function to handle chat item click
  const handleChatItemClick = (chatId) => {
    setHasMoreIndividualChat(false);
    const token = localStorage.getItem('token');
    setSelectedChatId(chatId);
    setShowConversationInterface(true);
    getIndividualChat(chatId, token);
  };

  // Modify getIndividualChat to accept page and pageSize
  const getIndividualChat = async (conversationId, token, page = 1, pageSize = individualChatPageSize, append = false) => {
    setLoadSpecificConversation(true);
    try {
      const queryPayloadChat = {
        "conversation_id": conversationId, // Use the passed conversationId
        "page": page,
        "page_size": pageSize,
        token,
      }
      const response = await dispatch(
        fetchSpecificConversations(queryPayloadChat)
      );
      const chatResponse = response?.payload?.data;
      if (Array.isArray(chatResponse)) {
        // Reverse the array so the latest message is last
        let formatted = chatResponse
          .slice() // copy array
          .reverse()
          .map(msg => ({
            sender: msg.role === 'user' ? 'user' : 'ai',
            text: msg.content,
            timestamp: msg.created_at,
            message_id: msg.message_id, // <-- include message_id
            // Optionally, keep inventory_vehicles and is_inventory_fetch for later use
            inventory_vehicles: msg.inventory_vehicles,
            is_inventory_fetch: msg.is_inventory_fetch,
            // Feedback state for UI
            ...(msg.feedback_flag === 0 ? { _disliked: true } : {}),
            ...(msg.feedback_flag === 1 ? { thumbUpClicked: true } : {}),
          }));
        // Insert vehicle_cards message after each AI message with inventory_vehicles
        let withVehicleCards = [];
        let allVehicleCards = [];
        formatted.forEach((msg) => {
          withVehicleCards.push(msg);
          if (
            msg.sender === 'ai' &&
            msg.is_inventory_fetch === 1 &&
            Array.isArray(msg.inventory_vehicles) &&
            msg.inventory_vehicles.length > 0
          ) {
            withVehicleCards.push({
              sender: 'ai',
              type: 'vehicle_cards',
              vehicles: msg.inventory_vehicles,
              timestamp: msg.timestamp,
            });
            allVehicleCards = allVehicleCards.concat(msg.inventory_vehicles);
          }
        });
        // If append is true, prepend older messages (for load more)
        setConversation(prev => append ? [...withVehicleCards, ...prev] : withVehicleCards);
        setLoadSpecificConversation(false);
        // If there are vehicle cards, fetch garage vins and update garageAdded
        if (allVehicleCards.length > 0) {
          try {
            const vins = await dispatch(getGarageVehicleVins(token));
            let vinsArr = vins?.payload || [];
            setGarageVins(vinsArr);
            // Mark vehicles as added if their vin is in the garage vins
            let addedMap = {};
            allVehicleCards.forEach(vehicle => {
              if (vinsArr.includes(vehicle.vin)) {
                addedMap[vehicle.row_id] = true;
              }
            });
            setGarageAdded(addedMap);
          } catch (e) {
            // ignore error
          }
        }
        // Pagination: if less than pageSize, no more data
        setHasMoreIndividualChat(chatResponse.length === pageSize && true);
        setIndividualChatPage(page);
      }
    } catch (error) {
      console.error("Error while fetching chats:", error);
      setLoadSpecificConversation(false);
    }
  };

  // Expose a function to load more (older) messages
  const loadMoreIndividualChat = async (convId, token) => {
    // Use convId (from UI), or fallback to selectedChatId, or conversationId
    const idToUse = convId || selectedChatId || conversationId;
    if (!idToUse || !hasMoreIndividualChat || loadSpecificConversation) return;
    const nextPage = individualChatPage + 1;
    await getIndividualChat(idToUse, token, nextPage, individualChatPageSize, true);
  };

  // Update feedback to accept all params
  const feedback = async (messageId, feedbackFlag = 0, feedbackMessage = '') => {
    const token = localStorage.getItem('token');
    setFeedbackLoading(true);
    try {
      const queryPayloadFeedback = {
        "message_id": messageId, // Use the passed messageId
        "feedback_message": feedbackMessage,
        "feedback_flag": feedbackFlag,
        token,
      }
      const response = await dispatch(
        messsageFeedback(queryPayloadFeedback)
      );
      const feedbackResponse = response?.payload?.data;
      setFeedbackRes(feedbackResponse?._id);
      setFeedbackFlagRes(feedbackFlag);
      setFeedbackLoading(false);
    } catch (error) {
      console.error("Error while submitting feedback:", error);
      setFeedbackLoading(false);
    }
  };

  return {
    chatHistoryListParams,
    setChatHistoryListParams,
    initalCondition,
    setInitialcondition,
    value,
    sendInitialQuery,
    showBot,
    userFirstName,
    userMessage,
    setUserMessage,
    query,
    setQuery,
    aiQueryResponse,
    setAiQueryResposne,
    conversationId,
    setConversationId,
    conversation,
    setConversation,
    loading,
    loadMoreLoading,
    loadMoreChatHistory,
    hasMoreData,
    searchText,
    setSearchText,
    totalChatCount,
    showConversationInterface,
    setShowConversationInterface,
    selectedChatId,
    setSelectedChatId,
    handleChatItemClick,
    loadSpecificConversation,
    fetchChatHistory, // Expose fetchChatHistory function
    restoreOriginalChatHistory, // Expose restoreOriginalChatHistory function
    hasInitialDataLoaded, // Expose flag to check if initial data has been loaded
    renderKey,
    feedback,
    feedbackRes,
    feedbackFlagRes,
    feedbackLoading,
    garageVins,
    setGarageVins,
    garageAdded,
    setGarageAdded,
    fetchAiDebugResponse,
    getIndividualChat,
    loadMoreIndividualChat,
    individualChatPage,
    hasMoreIndividualChat,
    setHasMoreIndividualChat,
  };
};

export default UseAiChatApi;
