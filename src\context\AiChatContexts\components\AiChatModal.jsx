import { Modal } from "react-bootstrap";
import PropTypes from "prop-types";
import { useEffect, useState, useRef } from "react";
import { useMediaQuery } from "react-responsive";
import { Box } from "@mui/material";
import { styled } from "@mui/material/styles";
import InitialChatInterfaceIndex from "./initialChatInterface/InitialChatInterfaceIndex";
import ConversationInterfaceIndex from "./conversationInterface/conversationInterfaceIndex";
import SidebarIndex from "./sidebar/sidebarIndex";
import UseAiChatApi from "../api/aiChatApi";
import CloseIcon from '@mui/icons-material/Close';

const StyledMainContent = styled(Box)(() => ({
  flex: 1,
  height: "100%",
  width: "100%",
  position: "relative",
}));

const AiChatModal = ({ show, onHide }) => {
  const isTabletOrMobile = useMediaQuery({ query: '(max-width: 928px)' });
  const isMobile = useMediaQuery({ maxWidth: 767 });

  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isChatSend, setIsChatSend] = useState(false); // checking which interface to show user

  // LIFTED STATE: UseAiChatApi
  const aiChatApi = UseAiChatApi();
  const fetchInProgressRef = useRef(false); // Track if fetch is in progress

  // Reset page to 1 when modal opens
  useEffect(() => {
    if (show && aiChatApi.setChatHistoryListParams) {
      aiChatApi.setChatHistoryListParams(prev => ({
        ...prev,
        page: 1
      }));
    }
  }, [show, aiChatApi.setChatHistoryListParams]);

  // Fetch chat history when modal opens (always fetch fresh data)
  useEffect(() => {
    if (show && aiChatApi.fetchChatHistory && !fetchInProgressRef.current) {
      fetchInProgressRef.current = true;
      aiChatApi.fetchChatHistory().finally(() => {
        fetchInProgressRef.current = false;
      });
    }
  }, [show, aiChatApi.fetchChatHistory]);

  // Helper to reset chat state for a fresh interface
  const resetChatState = () => {
    if (aiChatApi.setQuery) aiChatApi.setQuery("");
    if (aiChatApi.setInitialcondition) aiChatApi.setInitialcondition(true);
    if (aiChatApi.setUserMessage) aiChatApi.setUserMessage("");
    if (aiChatApi.setAiQueryResposne) aiChatApi.setAiQueryResposne("");
    if (aiChatApi.setConversation) aiChatApi.setConversation([]);
    if (aiChatApi.setConversationId) aiChatApi.setConversationId(null);
    if (aiChatApi.setSelectedChatId) aiChatApi.setSelectedChatId(null);
    if (aiChatApi.setShowConversationInterface) aiChatApi.setShowConversationInterface(false);
    // Reset page to 1 when modal opens or chat state is reset
    if (aiChatApi.setChatHistoryListParams) {
      aiChatApi.setChatHistoryListParams(prev => ({
        ...prev,
        page: 1
      }));
    }
    // Add more resets if needed
  };

  const closeChat = () => {
    onHide();
    setIsChatSend(false);
    resetChatState();
    // Reset fetch progress ref for next modal open
    fetchInProgressRef.current = false;
  };

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  const truncateText = (text, maxLength) => {
    if (text.length > maxLength) {
      return text.slice(0, maxLength) + "...";
    }
    return text;
  }; // Truncate text if necessary

  // Patch setIsChatSend to reset state when going to initial interface
  const handleSetIsChatSend = (val) => {
    if (!val) resetChatState();
    setIsChatSend(val);
  };

  useEffect(() => {
    if (isTabletOrMobile) {
      setIsSidebarCollapsed(true);
    }
  }, [isTabletOrMobile]);

  return (
    <Modal
      show={show}
      onHide={onHide}
      centered
      backdrop="static"
      dialogClassName="custom-modal"
      className="no-padding-modal"
    >
      <Modal.Body className="p-0">
        <div className="d-flex" style={{ height: "100%", position: isTabletOrMobile && "relative", }}>
          <SidebarIndex
            truncateText={truncateText}
            isSidebarCollapsed={isSidebarCollapsed}
            toggleSidebar={toggleSidebar}
            closeChat={closeChat}
            setIsChatSend={handleSetIsChatSend}
            resetChatState={resetChatState}
            value={aiChatApi.value}
            loading={aiChatApi.loading}
            loadMoreLoading={aiChatApi.loadMoreLoading}
            loadMoreChatHistory={aiChatApi.loadMoreChatHistory}
            hasMoreData={aiChatApi.hasMoreData}
            searchText={aiChatApi.searchText}
            setSearchText={aiChatApi.setSearchText}
            totalChatCount={aiChatApi.totalChatCount}
            handleChatItemClick={aiChatApi.handleChatItemClick}
            selectedChatId={aiChatApi.selectedChatId}
          />
          {/* Main Content */}
          <StyledMainContent isMobile={isMobile} isSidebarCollapsed={isSidebarCollapsed}>
            {isChatSend ? (
              <ConversationInterfaceIndex {...aiChatApi} truncateText={truncateText} />
            ) : (
              <InitialChatInterfaceIndex setIsChatSend={handleSetIsChatSend} {...aiChatApi} />
            )}
          </StyledMainContent>
        </div>
      </Modal.Body>
      {!isTabletOrMobile &&
        <div
          className="ai-chat-close-icon"
          onClick={closeChat}
        >
          <CloseIcon sx={{ color: '#fff' }} />
        </div>
      }
    </Modal>
  );
};

AiChatModal.propTypes = {
  show: PropTypes.bool.isRequired,
  onHide: PropTypes.func.isRequired,
};

export default AiChatModal;
