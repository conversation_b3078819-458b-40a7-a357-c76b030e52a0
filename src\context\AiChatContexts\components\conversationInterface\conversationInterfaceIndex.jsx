import { useMediaQuery } from "react-responsive";
import ConversationInterfaceWeb from "./conversationInterfaceWeb";
import ConversationInterfaceMobile from "./conversationInterfaceMobile";
import { useDispatch } from "react-redux";
import { getVehicleDetailsInventory, rightSwipe } from "../../../../store/apps/car";
import { pageMetaData } from "../../../../../data/pageMetaData";
import { callComponentActionApi } from "../../../../util/callComponenetActionApi";
import { useEffect, useState } from "react";
import { pageComponentData } from "../../../../../data/componentMetaData";
import { toast } from "react-toastify";

const ConversationInterfaceIndex = (aiChatApi) => {
    const baseUrl = `${window.location.protocol}//${window.location.host}`;
    let conversation_details_url = `${baseUrl}/conversation`;
    const [componentMetaData, setComponentMetaData] = useState(null); // State to store the componentMetaData
    const [pageId, setPageId] = useState(null);
    useEffect(() => {
        // Get the page_id from pageMetaData based on slug
        const slug = "swipe-cards"; // Replace this with the desired slug
        const pageData = pageMetaData.find((page) => page.slug === slug);
        if (pageData) {
            setPageId(pageData.id);
        }

        // Get Page Componenet Meta Data
        const matchingComponents = pageComponentData.filter(
            (pageComponent) => pageComponent.page_id === pageId
        );

        if (matchingComponents.length > 0) {
            setComponentMetaData(matchingComponents);
        }
    }, [pageId]);
    // Format: YYYY-MM-DD HH:mm:ss in local time
    function formatTimestampLocal(isoString) {
        if (!isoString) return '';
        // If no timezone info, treat as UTC
        let date;
        if (/Z|[+-]\d{2}:?\d{2}$/.test(isoString)) {
            date = new Date(isoString);
        } else {
            // Append Z to treat as UTC
            date = new Date(isoString + 'Z');
        }
        // Format date in local time with GMT offset
        const locale = 'en-US';
        const options = {
            year: 'numeric',
            month: 'short',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        };
        const dateStr = date.toLocaleString(locale, options);
        // Get GMT offset in "+5:30" format
        const offsetMinutes = date.getTimezoneOffset();
        const absOffset = Math.abs(offsetMinutes);
        const sign = offsetMinutes <= 0 ? '+' : '-';
        const hours = String(Math.floor(absOffset / 60)).padStart(2, '0');
        const minutes = String(absOffset % 60).padStart(2, '0');
        const gmt = `GMT${sign}${hours}:${minutes}`;
        return `${dateStr} ${gmt}`;
    }

    // Function to add vehicle to garage using rightSwipe
    async function handleGarageIconClick(vehicle) {
        if (!vehicle) return;
        const { vin, heading, row_id, stock_no } = vehicle;
        const token = localStorage.getItem('token');
        const swipedData = {
            inventory_row_id: row_id,
            stock_number: stock_no,
            conversation_details_url,
            vin,
        };
        try {
            const response = await dispatch(
                rightSwipe({ swipedData, token })
            );
            console.log('Garage Icon Clicked & rightSwipe dispatched:', { vin, heading, row_id, stock_no }, 'Response:', response);
            toast.success(
                `${heading ? heading : "Vehicle"
                } Moved to My Garage`
            );
            const component = componentMetaData.find((comp) => comp.slug === "right-swipe");
            if (component) {
                // Run this function without waiting for it to complete
                (async () => {
                    try {
                        await callComponentActionApi(dispatch, component.id, swipedData.inventory_row_id);
                    } catch (error) {
                        console.error("Error while calling callComponentActionApi:", error);
                    }
                })();
            }
        } catch (error) {
            console.error('Error calling rightSwipe:', error);
        }
    }

    const isTabletOrMobile = useMediaQuery({ query: '(max-width: 928px)' });
    const dispatch = useDispatch();
    const fetchCardVehicleDetails = async (row_id) => {
        const token = localStorage.getItem("token");
        try {
            const response = await dispatch(
                getVehicleDetailsInventory({ row_id, token })
            );
            return response.payload; // Return the response payload
        } catch (error) {
            console.error("Error while Fetching Vehicles:", error);
            throw error; // Re-throw the error to handle it in the child component
        }
    };
    // Use props from parent instead of calling UseAiChatApi
    const { userFirstName, sendInitialQuery, conversationId, setUserMessage, conversation, setConversation, selectedChatId, loadSpecificConversation, truncateText, feedback, feedbackRes, feedbackLoading, feedbackFlagRes, garageAdded, setGarageAdded, fetchAiDebugResponse, loadMoreIndividualChat, hasMoreIndividualChat, individualChatPage, setIndividualChatPage, setHasMoreIndividualChat } = aiChatApi;
    const userParams = { userFirstName, sendInitialQuery, conversationId, setUserMessage, conversation, setConversation, formatTimestampLocal, selectedChatId, loadSpecificConversation, truncateText, fetchCardVehicleDetails, feedback, feedbackRes, feedbackLoading, feedbackFlagRes, handleGarageIconClick, garageAdded, setGarageAdded, fetchAiDebugResponse, loadMoreIndividualChat, hasMoreIndividualChat, individualChatPage, setIndividualChatPage, setHasMoreIndividualChat };
    return (
        isTabletOrMobile ? <ConversationInterfaceMobile {...userParams} /> : <ConversationInterfaceWeb {...userParams} />
    );
};

export default ConversationInterfaceIndex;