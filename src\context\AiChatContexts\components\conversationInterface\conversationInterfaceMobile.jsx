import { Avatar, Box, Button, IconButton, InputAdornment, TextField, Typography, styled, Tooltip, CircularProgress, Skeleton, Slider } from "@mui/material";
import { Card } from "react-bootstrap";
import { ImageBaseURL } from "../../../../config";
import ReactMarkdown from "react-markdown";
import { useState, useEffect, useRef } from "react";
import useSpeechRecognition from '../../useSpeechRecognition';
import SpeechNotRecognizedDialog from '../../../../pages/search/modals/SpeechNotRecognizedDialog';
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import Icon from "../../../../icon";
import VehicleInventoryDetailsModal from '../../../../pages/swipeCards/modal/vehicleInventoryDetailsModal';
import HighValueFeaturessModal from '../../../../pages/myGarage/modal/highvaluFeatures';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import ThumbDownIcon from '@mui/icons-material/ThumbDown';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import React from 'react';

const CustomQueryTextField = styled(TextField)(() => ({
    "& .MuiInputLabel-root": {
        color: "rgba(113, 113, 113, 0.5)",
        fontWeight: "bold",
        fontSize: "12px", // Smaller label
    },
    "& .MuiOutlinedInput-root": {
        "& fieldset": {
            border: 'none',
        },
        "&:hover fieldset": {
            borderColor: "#000",
        },
        "&.Mui-focused fieldset": {
            borderColor: "#000",
        },
        "& textarea": {
            fontSize: "12px", // Smaller input text
            fontWeight: 500,
            lineHeight: "1.4",
            resize: "none",
            "&::placeholder": {
                fontSize: "12px", // Smaller placeholder
                color: "rgba(113, 113, 113, 0.5)",
                opacity: 0.8,
            },
        },
        minHeight: "2.75rem",
        padding: "5px",
        // borderRadius: "10px",
    },
}));

const labelStyle = {
    color: "#4891FF",
};

const CustomTextField = styled(TextField)({
    "& .MuiInputLabel-root": labelStyle, // Apply label style
    "& .MuiInputLabel-root.Mui-focused": {
        color: "#4891FF",
    },
    "& .MuiOutlinedInput-root": {
        "& fieldset": {
            borderColor: "#4891FF",
            borderWidth: "2px",
        },
        "&:hover fieldset": {
            borderColor: "#4891FF",
        },
        "&.Mui-focused fieldset": {
            borderColor: "#4891FF",
        },
        "& input": {
            fontWeight: 600, // Use a valid font weight
        },
    },
});

const ConversationInterfaceMobile = ({ userFirstName, sendInitialQuery, conversationId, setUserMessage, conversation, formatTimestampLocal, selectedChatId, loadSpecificConversation, truncateText, fetchCardVehicleDetails, feedback, feedbackLoading, feedbackRes, feedbackFlagRes, garageAdded, setGarageAdded, handleGarageIconClick, fetchAiDebugResponse, loadMoreIndividualChat, hasMoreIndividualChat, individualChatPage }) => {

    const [input, setInput] = useState("");
    // Animation state for AI typing effect
    const [animatedText, setAnimatedText] = useState("");
    // State to track disliked messages and force re-renders
    const [dislikedMessages, setDislikedMessages] = useState(new Set());
    const [isAnimating, setIsAnimating] = useState(false);
    const animationTimeout = useRef(null);

    // Modal state for vehicle details
    const [vehicleDetailsModalOpen, setVehicleDetailsModalOpen] = useState(false);
    const [vehicleDetailsLoading, setVehicleDetailsLoading] = useState(false);
    const [vehicleDetails, setVehicleDetails] = useState(null);
    const [selectedVehicle, setSelectedVehicle] = useState(null);
    const [vehicleDetailsError, setVehicleDetailsError] = useState(null);
    // High value features modal state
    const [highValueModalOpen, setHighValueModalOpen] = useState(false);
    const [highValueVehicle, setHighValueVehicle] = useState(null);
    const [highValueVehicleDetails, setHighValueVehicleDetails] = useState(null);

    // Feedback pending state for like/dislike
    const [feedbackPending, setFeedbackPending] = useState({}); // { [message_id]: 'like' | 'dislike' | null }

    // Add to garage loading state per vehicle row_id
    const [garageLoading, setGarageLoading] = useState({}); // { [row_id]: boolean }

    // Ref for auto-scrolling to the end of conversation
    const endOfMessagesRef = useRef(null);
    // Ref for the scrollable conversation container
    const conversationContainerRef = useRef(null);
    // State for scroll-to-bottom button
    const [showScrollToBottom, setShowScrollToBottom] = useState(false);// State to control showing current timestamp under AI message after Apply is clicked
    const [aiApplyTimestamp, setAiApplyTimestamp] = useState(null);

    // Voice recognition hook
    const {
        isListening,
        toggleSpeechRecognition,
        isSpeechDialogOpen,
        dialogText,
        handleDialogClose,
    } = useSpeechRecognition((transcript) => {
        setInput((prev) => (prev + ' ' + transcript).trim());
    });

    // Find the latest AI message that is not loading
    const latestAiIdx = conversation ? [...conversation].reverse().findIndex(msg => msg.sender === 'ai' && !msg.loading && msg.text) : -1;
    const latestAiMsgIdx = latestAiIdx !== -1 ? conversation.length - 1 - latestAiIdx : -1;
    const latestAiMsg = latestAiMsgIdx !== -1 ? conversation[latestAiMsgIdx] : null;

    // Animate the latest AI message when it appears
    useEffect(() => {
        if (latestAiMsg && latestAiMsg.text && isAnimating === false) {
            setAnimatedText("");
            setIsAnimating(true);
            let i = 0;
            const animate = () => {
                setAnimatedText(latestAiMsg.text.slice(0, i));
                if (i < latestAiMsg.text.length) {
                    animationTimeout.current = setTimeout(() => {
                        i++;
                        animate();
                    }, 5); // speed of animation (ms per char)
                } else {
                    setIsAnimating(false);
                }
            };
            animate();
            return () => clearTimeout(animationTimeout.current);
        }
        // If no latest AI message, reset
        if (!latestAiMsg) {
            setAnimatedText("");
            setIsAnimating(false);
        }
        // Only re-run when the latest AI message changes
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [latestAiMsg && latestAiMsg.text, latestAiMsgIdx]);

    const handleSend = () => {
        if (input.trim()) {
            sendInitialQuery(input, conversationId);
            setUserMessage(input); // update user message for display (optional, for legacy compatibility)
            setInput("");
        }
    };

    const handleInputKeyDown = (e) => {
        if (e.key === 'Enter' && !e.shiftKey && input.trim()) {
            e.preventDefault();
            handleSend();
        }
    };

    // Copy to clipboard handler for AI message
    const [copiedIdx, setCopiedIdx] = useState(null);
    const handleCopyMarkdown = (text, idx) => {
        if (!text) return;
        // Copy as markdown (plain text)
        navigator.clipboard.writeText(text);
        setCopiedIdx(idx);
        setTimeout(() => setCopiedIdx(null), 1200);
    };

    // Scroll event handler
    useEffect(() => {
        const container = conversationContainerRef.current;
        if (!container) return;
        const handleScroll = () => {
            const threshold = 30; // px
            const atBottom = container.scrollHeight - container.scrollTop - container.clientHeight < threshold;
            setShowScrollToBottom(!atBottom);
        };
        container.addEventListener('scroll', handleScroll);
        // Initial check
        handleScroll();
        return () => container.removeEventListener('scroll', handleScroll);
    }, [conversation]);
    // Scroll to bottom function
    const scrollToBottom = () => {
        if (endOfMessagesRef.current) {
            endOfMessagesRef.current.scrollIntoView({ behavior: "smooth" });
        }
    };
    // Auto-scroll to bottom on new messages
    useEffect(() => {
        if (endOfMessagesRef.current && conversationContainerRef.current) {
            const container = conversationContainerRef.current;
            const threshold = 30;
            const atBottom = container.scrollHeight - container.scrollTop - container.clientHeight < threshold;
            if (atBottom) {
                endOfMessagesRef.current.scrollIntoView({ behavior: "smooth" });
            }
        }
    }, [conversation]);

    // Only scroll to bottom when individualChatPage === 1
    useEffect(() => {
        if (
            individualChatPage === 1 &&
            endOfMessagesRef.current &&
            conversation &&
            conversation.length > 0
        ) {
            endOfMessagesRef.current.scrollIntoView({ behavior: "smooth" });
        }
    }, [selectedChatId, conversation, individualChatPage]);

    function DynamicLoadingText() {
        const messages = [
            'Loading conversation',
            'Fetching your chat',
            'Almost there',
            'Preparing messages',
        ];
        const [idx, setIdx] = useState(0);
        useEffect(() => {
            const interval = setInterval(() => {
                setIdx(i => (i + 1) % messages.length);
            }, 1400);
            return () => clearInterval(interval);
        }, []);
        return (
            <Typography sx={{ color: '#4891FF', fontWeight: 400, fontSize: 18, minHeight: 28 }}>
                {messages[idx]}<span className="loading-ellipsis">...</span>
            </Typography>
        );
    }

    // Animated waving dots for loading
    function WavingDots() {
        return (
            <span style={{ display: 'inline-block', marginLeft: 2 }}>
                <span className="dot" style={{ animation: 'wave 1s infinite', animationDelay: '0s' }}>.</span>
                <span className="dot" style={{ animation: 'wave 1s infinite', animationDelay: '0.2s' }}>.</span>
                <span className="dot" style={{ animation: 'wave 1s infinite', animationDelay: '0.4s' }}>.</span>
            </span>
        );
    }

    function TypingLoadingText() {
        const messages = [
            'AI is thinking',
            'Analyzing your query',
            'Generating a response',
            'Finding the best answer',
        ];
        const [idx, setIdx] = useState(0);
        useEffect(() => {
            const interval = setInterval(() => {
                setIdx(i => (i + 1) % messages.length);
            }, 1400);
            return () => clearInterval(interval);
        }, []);
        return (
            <span style={{ color: '#A16BFE', fontStyle: 'italic', fontWeight: 500 }}>
                {messages[idx]} <WavingDots />
            </span>
        );
    }

    // Handler to open vehicle details modal
    const handleOpenVehicleDetailsModal = async (vehicle) => {
        setSelectedVehicle(vehicle);
        setVehicleDetailsLoading(true);
        setVehicleDetailsError(null);
        setVehicleDetails(null);
        try {
            const details = await fetchCardVehicleDetails(vehicle.row_id);
            setVehicleDetails(details);
        } catch (err) {
            setVehicleDetailsError('Failed to fetch vehicle details');
        } finally {
            setVehicleDetailsLoading(false);
            setVehicleDetailsModalOpen(true);
        }
    };
    const handleCloseVehicleDetailsModal = () => {
        setVehicleDetailsModalOpen(false);
        setVehicleDetails(null);
        setSelectedVehicle(null);
        setVehicleDetailsError(null);
    };

    // Handler to open high value features modal
    const handleShowHighValueFeatures = (vehicle, details) => {
        setHighValueVehicle(vehicle);
        setHighValueVehicleDetails(details || vehicle); // fallback to vehicle if details not available
        setHighValueModalOpen(true);
    };
    const handleCloseHighValueModal = () => {
        setHighValueModalOpen(false);
        setHighValueVehicle(null);
        setHighValueVehicleDetails(null);
    };

    // Handler for garage icon click
    const handleGarageClick = async (vehicle) => {
        if (!vehicle || garageLoading[vehicle.row_id] || (garageAdded && garageAdded[vehicle.row_id])) return;
        setGarageLoading(prev => ({ ...prev, [vehicle.row_id]: true }));
        try {
            await handleGarageIconClick(vehicle);
            setGarageAdded && setGarageAdded(prev => ({ ...prev, [vehicle.row_id]: true }));
        } catch (e) {
            // Optionally show error UI
        } finally {
            setGarageLoading(prev => ({ ...prev, [vehicle.row_id]: false }));
        }
    };

    return (
        <div className="d-flex flex-column w-100 px-0" style={{ flexGrow: 1, height: '100%', minHeight: 0 }}>
            <Card className="flex-grow-1 w-100 shadow-sm chat-card" style={{ display: 'flex', flexDirection: 'column', flex: 1, height: '100%', minHeight: 0 }}>
                {loadSpecificConversation ? (
                    <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', background: '#fff', minHeight: 0, mt: 4 }}>
                        <iframe src="https://lottie.host/embed/e1e044dd-9f9c-449f-84da-af15cc08cd69/BdcxQrungy.lottie" style={{ width: 200, height: 200, border: 'none', background: 'transparent' }}></iframe>
                        <DynamicLoadingText />
                    </Box>
                ) : (
                    <>
                        <Box ref={conversationContainerRef} sx={{ flex: 1, overflowY: 'auto', padding: 3, background: '#fff', minHeight: 0, mt: 4 }}>
                            {/* Load More Button at the top */}
                            {hasMoreIndividualChat && (
                                <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                                    <Button
                                        variant="filled"
                                        size="small"
                                        sx={{
                                            background: '#4891FF',
                                            color: '#fff',
                                            '&:hover': {
                                                background: undefined,
                                                color: '#4891ff',
                                            },
                                        }}
                                        onClick={() => loadMoreIndividualChat(conversationId, localStorage.getItem('token'))}
                                        disabled={loadSpecificConversation}
                                    >
                                        {loadSpecificConversation ? <CircularProgress size={18} /> : 'Load Past Messages'}
                                    </Button>
                                </Box>
                            )}
                            {conversation && conversation.length > 0 ? conversation.map((msg, idx) => (
                                msg.sender === 'user' ? (

                                    <Box key={idx} sx={{ display: 'flex', alignItems: 'flex-start', my: 3, flexDirection: 'row-reverse' }}>
                                        <Box sx={{ ml: 1 }}>
                                            <Avatar sx={{ bgcolor: '#A16BFE', fontWeight: 600 }}>
                                                {userFirstName?.charAt(0)?.toUpperCase() || 'U'}
                                            </Avatar>
                                        </Box>
                                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
                                            <Box
                                                sx={{
                                                    background: '#fff',
                                                    borderRadius: '10px 0px 10px 10px',
                                                    p: 1.5,
                                                    minWidth: 0,
                                                    maxWidth: '100%',
                                                    wordBreak: 'break-word',
                                                    overflowWrap: 'anywhere',
                                                    boxShadow: '1px 1px 4px #4891ff'
                                                }}
                                            >
                                                <Typography variant="body2" sx={{ fontWeight: 500, color: '#222' }}>
                                                    {msg.text}
                                                </Typography>
                                            </Box>
                                            <Typography
                                                variant="caption"
                                                sx={{
                                                    color: '#888',
                                                    mt: 1,
                                                    display: 'block',
                                                    textAlign: 'right',
                                                    fontSize: '10px'
                                                }}
                                            >
                                                {msg.timestamp ? formatTimestampLocal(msg.timestamp) : formatTimestampLocal(new Date().toISOString())}
                                            </Typography>
                                        </Box>
                                    </Box>
                                ) : (

                                    <Box key={idx} sx={{ display: 'flex', alignItems: 'flex-start', my: 4 }}>
                                        {/* If msg.type === 'vehicle_cards', render vehicle cards */}
                                        {msg.type === 'vehicle_cards' ? (
                                            <Box sx={{ width: '100%', mt: 2, mb: 2 }}>
                                                <VehicleCardsCarouselMobile
                                                    vehicles={sortVehiclesByPrice(msg.vehicles)}
                                                    fallbackImg={ImageBaseURL + 'fallback_image.png'}
                                                    truncateText={truncateText}
                                                    cardsToShow={1}
                                                    onInfoClick={handleOpenVehicleDetailsModal}
                                                    vehicleDetailsLoading={vehicleDetailsLoading}
                                                    selectedVehicle={selectedVehicle}
                                                    onShowHighValueFeatures={handleShowHighValueFeatures}
                                                    vehicleDetails={vehicleDetails}
                                                    garageLoading={garageLoading}
                                                    garageAdded={garageAdded}
                                                    onGarageClick={handleGarageClick}
                                                />
                                            </Box>
                                        ) : (
                                            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                                                <Box
                                                    sx={{
                                                        background: '#fff',
                                                        borderRadius: '0px 10px 10px 10px',
                                                        padding: '1px 8px',
                                                        wordBreak: 'break-word',
                                                        overflowWrap: 'anywhere',
                                                    }}
                                                >
                                                    <Typography variant="body2" sx={{ fontWeight: 500, color: '#222' }}>
                                                        {msg.loading ? (
                                                            <TypingLoadingText />
                                                        ) : (
                                                            (msg.is_inventory_fetch === 1 && msg.inventory_vehicles && msg.inventory_vehicles.length > 0)
                                                                ? (
                                                                    <Box sx={{ mb: 2 }}>
                                                                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                                                            Thanks for the clarification. Based on everything we've discussed, here are a few vehicles from our database that align well with your preferences.
                                                                        </Typography>
                                                                        <br />
                                                                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                                                            Take a moment to explore these options — each one reflects the priorities you've shared: comfort, performance, and value within your budget.
                                                                        </Typography>
                                                                        <br /><br />
                                                                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                                                            Next Steps:
                                                                        </Typography>
                                                                        <br />
                                                                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                                                            I can assist you in any of the following:
                                                                        </Typography>
                                                                        <ul style={{ marginTop: 4, marginBottom: 4, paddingLeft: 24 }}>
                                                                            <li>Scheduling a test drive</li>
                                                                            <li>Connecting you with local dealers</li>
                                                                            <li>Offering side-by-side comparisons with similar models</li>
                                                                            <li>Or refining your search if you're still exploring</li>
                                                                        </ul><br />
                                                                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                                                            Let me know how you'd like to continue — I'm here to help you make the most confident and informed choice.
                                                                        </Typography>
                                                                    </Box>
                                                                )
                                                                : (msg.is_inventory_fetch === 1 && (!msg.inventory_vehicles || msg.inventory_vehicles.length === 0))
                                                                    ? <NoVehiclesFound fetchAiDebugResponse={fetchAiDebugResponse} conversationId={conversationId} conversation={conversation} onApplyClick={() => setAiApplyTimestamp(new Date())} />
                                                                    : (msg.text && <ReactMarkdown>{msg.text}</ReactMarkdown>)
                                                        )}
                                                    </Typography>
                                                </Box>
                                                {/* Wrap timestamp and icons together */}
                                                {!msg.loading && (
                                                    <>
                                                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 1, paddingLeft: '8px' }}>
                                                            {/* Show aiApplyTimestamp if set and this is the relevant AI message */}
                                                            {(msg.is_inventory_fetch === 1 && (!msg.inventory_vehicles || msg.inventory_vehicles.length === 0) && aiApplyTimestamp) ? (
                                                                <Typography
                                                                    variant="caption"
                                                                    sx={{
                                                                        color: '#888',
                                                                        fontSize: '11px',
                                                                        whiteSpace: 'nowrap',
                                                                    }}
                                                                >
                                                                    {formatTimestampAi(aiApplyTimestamp)}
                                                                </Typography>
                                                            ) : (
                                                                <Typography
                                                                    variant="caption"
                                                                    sx={{
                                                                        color: '#888',
                                                                        fontSize: '11px',
                                                                        whiteSpace: 'nowrap',
                                                                    }}
                                                                >
                                                                    {msg.timestamp ? formatTimestampLocal(msg.timestamp) : formatTimestampLocal(new Date().toISOString())}
                                                                </Typography>
                                                            )}
                                                            <Tooltip
                                                                title={copiedIdx === idx ? 'Copied to clipboard' : 'Copy Response'}
                                                                open={copiedIdx === idx ? true : undefined}
                                                                placement="bottom"
                                                                arrow
                                                            >
                                                                <svg style={{ cursor: 'pointer' }} onClick={() => handleCopyMarkdown(msg.text, idx)} width="20px" height="20px" viewBox="0 -0.5 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <g id="SVGRepo_bgCarrier" stroke-width="0" />
                                                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" />
                                                                    <g id="SVGRepo_iconCarrier"> <path d="M8.25005 8.5C8.25005 8.91421 8.58584 9.25 9.00005 9.25C9.41426 9.25 9.75005 8.91421 9.75005 8.5H8.25005ZM9.00005 8.267H9.75006L9.75004 8.26283L9.00005 8.267ZM9.93892 5.96432L10.4722 6.49171L9.93892 5.96432ZM12.2311 5V4.24999L12.2269 4.25001L12.2311 5ZM16.269 5L16.2732 4.25H16.269V5ZM18.5612 5.96432L18.0279 6.49171V6.49171L18.5612 5.96432ZM19.5 8.267L18.75 8.26283V8.267H19.5ZM19.5 12.233H18.75L18.7501 12.2372L19.5 12.233ZM18.5612 14.5357L18.0279 14.0083L18.5612 14.5357ZM16.269 15.5V16.25L16.2732 16.25L16.269 15.5ZM16 14.75C15.5858 14.75 15.25 15.0858 15.25 15.5C15.25 15.9142 15.5858 16.25 16 16.25V14.75ZM9.00005 9.25C9.41426 9.25 9.75005 8.91421 9.75005 8.5C9.75005 8.08579 9.41426 7.75 9.00005 7.75V9.25ZM8.73105 8.5V7.74999L8.72691 7.75001L8.73105 8.5ZM6.43892 9.46432L6.97218 9.99171L6.43892 9.46432ZM5.50005 11.767H6.25006L6.25004 11.7628L5.50005 11.767ZM5.50005 15.734L6.25005 15.7379V15.734H5.50005ZM8.73105 19L8.72691 19.75H8.73105V19ZM12.769 19V19.75L12.7732 19.75L12.769 19ZM15.0612 18.0357L14.5279 17.5083L15.0612 18.0357ZM16 15.733H15.25L15.2501 15.7372L16 15.733ZM16.75 15.5C16.75 15.0858 16.4143 14.75 16 14.75C15.5858 14.75 15.25 15.0858 15.25 15.5H16.75ZM9.00005 7.75C8.58584 7.75 8.25005 8.08579 8.25005 8.5C8.25005 8.91421 8.58584 9.25 9.00005 9.25V7.75ZM12.7691 8.5L12.7732 7.75H12.7691V8.5ZM15.0612 9.46432L15.5944 8.93694V8.93694L15.0612 9.46432ZM16.0001 11.767L15.2501 11.7628V11.767H16.0001ZM15.2501 15.5C15.2501 15.9142 15.5858 16.25 16.0001 16.25C16.4143 16.25 16.7501 15.9142 16.7501 15.5H15.2501ZM9.75005 8.5V8.267H8.25005V8.5H9.75005ZM9.75004 8.26283C9.74636 7.60005 10.0061 6.96296 10.4722 6.49171L9.40566 5.43694C8.65985 6.19106 8.24417 7.21056 8.25006 8.27117L9.75004 8.26283ZM10.4722 6.49171C10.9382 6.02046 11.5724 5.75365 12.2352 5.74999L12.2269 4.25001C11.1663 4.25587 10.1515 4.68282 9.40566 5.43694L10.4722 6.49171ZM12.2311 5.75H16.269V4.25H12.2311V5.75ZM16.2649 5.74999C16.9277 5.75365 17.5619 6.02046 18.0279 6.49171L19.0944 5.43694C18.3486 4.68282 17.3338 4.25587 16.2732 4.25001L16.2649 5.74999ZM18.0279 6.49171C18.494 6.96296 18.7537 7.60005 18.7501 8.26283L20.25 8.27117C20.2559 7.21056 19.8402 6.19106 19.0944 5.43694L18.0279 6.49171ZM18.75 8.267V12.233H20.25V8.267H18.75ZM18.7501 12.2372C18.7537 12.8999 18.494 13.537 18.0279 14.0083L19.0944 15.0631C19.8402 14.3089 20.2559 13.2894 20.25 12.2288L18.7501 12.2372ZM18.0279 14.0083C17.5619 14.4795 16.9277 14.7463 16.2649 14.75L16.2732 16.25C17.3338 16.2441 18.3486 15.8172 19.0944 15.0631L18.0279 14.0083ZM16.269 14.75H16V16.25H16.269V14.75ZM9.00005 7.75H8.73105V9.25H9.00005V7.75ZM8.72691 7.75001C7.6663 7.75587 6.65146 8.18282 5.90566 8.93694L6.97218 9.99171C7.43824 9.52046 8.07241 9.25365 8.73519 9.24999L8.72691 7.75001ZM5.90566 8.93694C5.15985 9.69106 4.74417 10.7106 4.75006 11.7712L6.25004 11.7628C6.24636 11.1001 6.50612 10.463 6.97218 9.99171L5.90566 8.93694ZM4.75005 11.767V15.734H6.25005V11.767H4.75005ZM4.75006 15.7301C4.73847 17.9382 6.51879 19.7378 8.72691 19.75L8.7352 18.25C7.35533 18.2424 6.2428 17.1178 6.25004 15.7379L4.75006 15.7301ZM8.73105 19.75H12.769V18.25H8.73105V19.75ZM12.7732 19.75C13.8338 19.7441 14.8486 19.3172 15.5944 18.5631L14.5279 17.5083C14.0619 17.9795 13.4277 18.2463 12.7649 18.25L12.7732 19.75ZM15.5944 18.5631C16.3402 17.8089 16.7559 16.7894 16.75 15.7288L15.2501 15.7372C15.2537 16.3999 14.994 17.037 14.5279 17.5083L15.5944 18.5631ZM16.75 15.733V15.5H15.25V15.733H16.75ZM9.00005 9.25H12.7691V7.75H9.00005V9.25ZM12.7649 9.24999C13.4277 9.25365 14.0619 9.52046 14.5279 9.99171L15.5944 8.93694C14.8486 8.18282 13.8338 7.75587 12.7732 7.75001L12.7649 9.24999ZM14.5279 9.99171C14.994 10.463 15.2537 11.1001 15.2501 11.7628L16.75 11.7712C16.7559 10.7106 16.3402 9.69106 15.5944 8.93694L14.5279 9.99171ZM15.2501 11.767V15.5H16.7501V11.767H15.2501Z" fill="#000000" /> </g>
                                                                </svg>
                                                            </Tooltip>
                                                            <ThumbUpToggle
                                                                msg={msg}
                                                                feedback={feedback}
                                                                feedbackLoading={feedbackLoading}
                                                                feedbackRes={feedbackRes}
                                                                feedbackFlagRes={feedbackFlagRes}
                                                                onFeedbackGiven={() => msg._feedbackGiven = true}
                                                                feedbackPending={feedbackPending[msg.message_id] === 'like'}
                                                                setFeedbackPending={pending => setFeedbackPending(prev => ({ ...prev, [msg.message_id]: pending ? 'like' : null }))}
                                                            />
                                                            <ThumbDownToggle
                                                                msg={msg}
                                                                feedback={feedback}
                                                                feedbackRes={feedbackRes}
                                                                feedbackFlagRes={feedbackFlagRes}
                                                                onDislike={() => {
                                                                    setDislikedMessages(prev => new Set([...prev, msg.message_id]));
                                                                }}
                                                                isDisliked={dislikedMessages.has(msg.message_id)}
                                                                feedbackPending={feedbackPending[msg.message_id] === 'dislike'}
                                                                setFeedbackPending={pending => setFeedbackPending(prev => ({ ...prev, [msg.message_id]: pending ? 'dislike' : null }))}
                                                            />
                                                        </Box>

                                                        {/* Feedback callout moved outside the action buttons row */}
                                                        <Box sx={{ paddingLeft: '16px', mt: 1 }}>
                                                            <FeedbackCallout
                                                                msg={msg}
                                                                feedback={feedback}
                                                                feedbackRes={feedbackRes}
                                                                feedbackFlagRes={feedbackFlagRes}
                                                                hidden={feedbackRes === msg.message_id && feedbackFlagRes === 1 || !!msg._feedbackGiven || !!msg.thumbUpClicked}
                                                                isDisliked={dislikedMessages.has(msg.message_id)}
                                                                onCancel={() => {
                                                                    setDislikedMessages(prev => {
                                                                        const newSet = new Set(prev);
                                                                        newSet.delete(msg.message_id);
                                                                        return newSet;
                                                                    });
                                                                }}
                                                            />
                                                        </Box>
                                                    </>
                                                )}
                                            </Box>
                                        )}
                                    </Box>
                                )
                            )) : (
                                <Typography variant="body2" sx={{ color: '#888', textAlign: 'center', mt: 2 }}>
                                    No conversation yet. Start chatting!
                                </Typography>
                            )}
                            <div ref={endOfMessagesRef} />
                        </Box>
                        {!loadSpecificConversation && (
                            <Box sx={{ borderTop: '1px solid #eee', p: 2, background: '#fff', display: 'flex', alignItems: 'center', gap: 1 }}>
                                {/* BOTTOM SECTION */}
                                <div className="flex-grow-1 d-flex justify-content-center align-items-center">
                                    <div className="w-100 d-flex flex-column px-2 py-2" style={{ border: '1px solid #ccc', borderRadius: '8px', }}>
                                        {/* TextArea */}
                                        <CustomQueryTextField
                                            fullWidth
                                            multiline
                                            maxRows={5}
                                            id="outlined-basic"
                                            placeholder="Ask Your Query..."
                                            variant="outlined"
                                            autoComplete="off"
                                            value={input}
                                            onChange={e => setInput(e.target.value)}
                                            onKeyDown={handleInputKeyDown}
                                            InputProps={{
                                                sx: {
                                                    fontSize: "16px",
                                                    paddingRight: 0, // No icon inside input
                                                },
                                            }}
                                            InputLabelProps={{
                                                sx: { fontSize: "16px" },
                                            }}
                                        />

                                        {/* Icons aligned bottom-right */}
                                        <div className="d-flex justify-content-between align-items-center">
                                            {/* Voice Icon */}
                                            <IconButton className={isListening ? "fab-listening" : ""} style={{ cursor: "pointer", marginRight: "8px" }} onClick={toggleSpeechRecognition}>
                                                <svg fill="#105DC7" width="30px" height="30px" viewBox="0 0 56 56" xmlns="http://www.w3.org/2000/svg">

                                                    <g id="SVGRepo_bgCarrier" strokeWidth="0" />

                                                    <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />

                                                    <g id="SVGRepo_iconCarrier">

                                                        <path d="M 27.9999 51.9063 C 41.0546 51.9063 51.9063 41.0781 51.9063 28 C 51.9063 14.9453 41.0312 4.0937 27.9765 4.0937 C 14.8983 4.0937 4.0937 14.9453 4.0937 28 C 4.0937 41.0781 14.9218 51.9063 27.9999 51.9063 Z M 27.9999 47.9219 C 16.9374 47.9219 8.1014 39.0625 8.1014 28 C 8.1014 16.9609 16.9140 8.0781 27.9765 8.0781 C 39.0155 8.0781 47.8983 16.9609 47.9219 28 C 47.9454 39.0625 39.0390 47.9219 27.9999 47.9219 Z M 27.9999 31.3047 C 30.2968 31.3047 31.8905 29.5234 31.8905 27.1563 L 31.8905 17.9453 C 31.8905 15.5781 30.2968 13.7968 27.9999 13.7968 C 25.7030 13.7968 24.1093 15.5781 24.1093 17.9453 L 24.1093 27.1563 C 24.1093 29.5234 25.7030 31.3047 27.9999 31.3047 Z M 22.0468 40.6328 L 33.9765 40.6328 C 34.5155 40.6328 34.9843 40.1406 34.9843 39.6016 C 34.9843 39.0390 34.5155 38.5703 33.9765 38.5703 L 29.0312 38.5703 L 29.0312 35.7812 C 33.6952 35.3125 36.8124 31.8437 36.8124 27.1328 L 36.8124 24.1328 C 36.8124 23.5703 36.3671 23.1250 35.8280 23.1250 C 35.2890 23.1250 34.7968 23.5703 34.7968 24.1328 L 34.7968 27.1328 C 34.7968 31.0703 32.0312 33.9063 27.9999 33.9063 C 23.9452 33.9063 21.2030 31.0703 21.2030 27.1328 L 21.2030 24.1328 C 21.2030 23.5703 20.7109 23.1250 20.1718 23.1250 C 19.6562 23.1250 19.1874 23.5703 19.1874 24.1328 L 19.1874 27.1328 C 19.1874 31.8437 22.3046 35.3359 26.9687 35.7812 L 26.9687 38.5703 L 22.0468 38.5703 C 21.4843 38.5703 20.9921 39.0390 20.9921 39.6016 C 20.9921 40.1406 21.4843 40.6328 22.0468 40.6328 Z" />

                                                    </g>
                                                </svg>
                                            </IconButton>

                                            {/* Send Icon */}
                                            <IconButton style={{ cursor: "pointer" }} onClick={handleSend}>
                                                <svg width="28px" height="28px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">

                                                    <g id="SVGRepo_bgCarrier" strokeWidth="0" />

                                                    <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />

                                                    <g id="SVGRepo_iconCarrier"> <path d="M10.3009 13.6949L20.102 3.89742M10.5795 14.1355L12.8019 18.5804C13.339 19.6545 13.6075 20.1916 13.9458 20.3356C14.2394 20.4606 14.575 20.4379 14.8492 20.2747C15.1651 20.0866 15.3591 19.5183 15.7472 18.3818L19.9463 6.08434C20.2845 5.09409 20.4535 4.59896 20.3378 4.27142C20.2371 3.98648 20.013 3.76234 19.7281 3.66167C19.4005 3.54595 18.9054 3.71502 17.9151 4.05315L5.61763 8.2523C4.48114 8.64037 3.91289 8.83441 3.72478 9.15032C3.56153 9.42447 3.53891 9.76007 3.66389 10.0536C3.80791 10.3919 4.34498 10.6605 5.41912 11.1975L9.86397 13.42C10.041 13.5085 10.1295 13.5527 10.2061 13.6118C10.2742 13.6643 10.3352 13.7253 10.3876 13.7933C10.4468 13.87 10.491 13.9585 10.5795 14.1355Z" stroke="#105DC7" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /> </g>
                                                </svg>
                                            </IconButton>
                                        </div>
                                    </div>
                                </div>
                            </Box>
                        )}
                    </>
                )}
                {/* Scroll to bottom button: direct child of Card, overlays bottom center of Card */}
                {!loadSpecificConversation && showScrollToBottom && (
                    <Box
                        sx={{
                            position: 'absolute',
                            left: '50%',
                            bottom: 125,
                            transform: 'translateX(-50%)',
                            zIndex: 10,
                            display: 'flex',
                            alignItems: 'center',
                        }}
                    >
                        <Button
                            variant="contained"
                            color="primary"
                            onClick={scrollToBottom}
                            sx={{
                                borderRadius: '50%',
                                minWidth: 0,
                                width: 32,
                                height: 32,
                                boxShadow: '0 2px 8px rgba(72,145,255,0.3)',
                                background: '#A16BFE',
                                '&:hover': { background: 'purple' },
                                p: 0,
                            }}
                        >
                            <ArrowDownwardIcon sx={{ color: '#fff', fontSize: 18 }} />
                        </Button>
                    </Box>
                )}
            </Card>
            <VehicleInventoryDetailsModal
                open={vehicleDetailsModalOpen}
                onClose={handleCloseVehicleDetailsModal}
                vehicleDetails={vehicleDetails}
                selectedCar={selectedVehicle}
            />
            <HighValueFeaturessModal
                highValueopen={highValueModalOpen}
                onClose={handleCloseHighValueModal}
                vehicleDetails={highValueVehicleDetails}
                selectedCar={highValueVehicle}
            />
            <SpeechNotRecognizedDialog
                open={isSpeechDialogOpen}
                onClose={handleDialogClose}
                text={dialogText}
            />
        </div >
    );
};

export default ConversationInterfaceMobile;

function VehicleCardWithDynamicFeaturesMobile({ vehicle, fallbackImg, truncateText, onInfoClick, vehicleDetailsLoading, selectedVehicle, onShowHighValueFeatures, vehicleDetails, garageLoading, garageAdded, onGarageClick }) {
    const [imageLoaded, setImageLoaded] = useState(false);
    const featuresContainerRef = useRef(null);
    const [maxFeatures, setMaxFeatures] = useState(null); // null = not measured yet

    // Split features
    const features = vehicle.high_value_features ? vehicle.high_value_features.split('|') : [];

    // After image and details are rendered, measure available space for chips
    useEffect(() => {
        if (!imageLoaded || !featuresContainerRef.current) return;
        // Measure available height in the parent (card body)
        const parent = featuresContainerRef.current.parentElement;
        const parentHeight = parent.offsetHeight;
        const usedHeight = featuresContainerRef.current.offsetTop;
        const availableHeight = parentHeight - usedHeight - 16; // 16px bottom padding for mobile
        // Estimate chip height (assume 22px per row, with 4px gap)
        let totalHeight = 0;
        let count = 0;
        for (let i = 0; i < features.length; i++) {
            totalHeight += 26; // 22px chip + 4px gap
            if (totalHeight > availableHeight) break;
            count++;
        }
        // Always show at least one chip if there are features
        if (features.length > 0 && count < 1) count = 1;
        // If not all fit, leave space for '+N more' chip (but always show at least one chip)
        if (count < features.length && count > 1) count = Math.max(1, count - 1);
        setMaxFeatures(count);
    }, [imageLoaded, features.length]);

    const isLoadingThisCard = vehicleDetailsLoading && selectedVehicle && selectedVehicle.row_id === vehicle.row_id;

    return (
        <Card sx={{ width: 220, minHeight: '300px', boxShadow: 2, borderRadius: 2, p: 0, display: 'flex', flexDirection: 'column', alignItems: 'stretch', overflow: 'hidden', position: 'relative', m: 1 }} className="chat-response-vehicle-cards">
            {/* Upper: Vehicle Image in container */}
            <Box sx={{ width: '100%', height: 110, background: '#f5f5f5', display: 'flex', alignItems: 'center', justifyContent: 'center', overflow: 'hidden', position: 'relative' }}>
                <img
                    src={vehicle.photo_url || fallbackImg}
                    alt={vehicle.make + ' ' + vehicle.model}
                    style={{ width: '100%', height: '100%', objectFit: 'contain', transition: 'transform 0.3s', display: 'block' }}
                    onError={e => { e.target.src = fallbackImg; }}
                    onLoad={() => setImageLoaded(true)}
                />
                {!imageLoaded && (
                    <Skeleton variant="rectangular" width="100%" height={110} sx={{ position: 'absolute', top: 0, left: 0, zIndex: 2 }} />
                )}
            </Box>
            {/* Lower: All vehicle info, only after image loads */}
            <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'flex-start', p: 1.5, pt: 1, position: 'relative' }}>
                {!imageLoaded ? (
                    <>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 0.5 }}>
                            <Skeleton variant="rectangular" width={40} height={14} />
                            <Skeleton variant="circular" width={18} height={18} />
                        </Box>
                        <Skeleton variant="text" width="80%" height={18} sx={{ mb: 0.5 }} />
                        <Skeleton variant="text" width="60%" height={12} sx={{ mb: 0.5 }} />
                        <Skeleton variant="text" width="50%" height={14} sx={{ mb: 0.5 }} />
                        <Box sx={{ display: 'flex', gap: 0.5, mb: 1 }}>
                            {[...Array(2)].map((_, i) => (
                                <Skeleton key={i} variant="rectangular" width={40} height={12} />
                            ))}
                        </Box>
                        <Box sx={{ mt: 1.5, width: '100%' }}>
                            <Skeleton variant="rectangular" width="100%" height={28} />
                        </Box>
                    </>
                ) : (
                    <>
                        {/* Inventory type & year with info icon at top right */}
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 0.5 }}>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                                {vehicle.inventory_type && (
                                    <Box sx={{ bgcolor: '#105DC7', color: '#fff', fontWeight: 600, fontSize: 9, borderRadius: 2, px: 1, py: 0.25, display: 'inline-block' }}>
                                        {vehicle.inventory_type.toUpperCase()}
                                    </Box>
                                )}
                                {vehicle.year && (
                                    <Box sx={{ bgcolor: '#FFD600', color: '#222', fontWeight: 600, fontSize: 9, borderRadius: 2, px: 1, py: 0.25, display: 'inline-block' }}>
                                        {vehicle.year}
                                    </Box>
                                )}
                            </Box>
                            {/* Info icon at top right */}
                            {isLoadingThisCard ? (
                                <CircularProgress size={18} />
                            ) : (
                                <Icon
                                    icon="info"
                                    size={18}
                                    style={{ cursor: 'pointer' }}
                                    onClick={() => onInfoClick && onInfoClick(vehicle)}
                                />
                            )}
                        </Box>
                        {/* Title */}
                        {vehicle.heading && (
                            <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#222', fontSize: 12, lineHeight: 1.2, my: 0.5 }}>
                                {truncateText(vehicle.heading, 20)}
                            </Typography>
                        )}
                        {/* Miles */}
                        {vehicle.miles && (
                            <Typography variant="body2" sx={{ color: '#888', fontSize: 10, fontWeight: 500, mb: 0.5 }}>
                                {vehicle.miles?.toLocaleString()} Miles
                            </Typography>
                        )}
                        {/* Price */}
                        {vehicle.price && (
                            <Typography variant="subtitle2" sx={{ color: '#105DC7', fontWeight: 600, fontSize: 11, mb: 0.5 }}>
                                $ {vehicle.price?.toLocaleString()}
                            </Typography>
                        )}
                        {/* High value features as chips, only after details */}
                        <Box ref={featuresContainerRef} sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1, minHeight: 18 }}>
                            {maxFeatures !== null && features.length > 0 && (
                                <>
                                    {features.slice(0, maxFeatures).map((feature, fIdx) => (
                                        <Box key={fIdx} sx={{ bgcolor: '#F2F2F2', color: '#222', fontWeight: 600, fontSize: 9, borderRadius: 2, px: 0.5, py: 0.1, display: 'inline-block', whiteSpace: 'nowrap', maxWidth: 60, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                                            {truncateText(feature, 15)}
                                        </Box>
                                    ))}
                                    {maxFeatures < features.length && (
                                        <Box sx={{ bgcolor: '#E3F0FF', color: '#4891FF', fontWeight: 600, fontSize: 9, borderRadius: 2, px: 0.5, py: 0.1, display: 'inline-block', cursor: 'pointer', whiteSpace: 'nowrap' }}
                                            onClick={() => onShowHighValueFeatures(vehicle, vehicleDetails)}
                                        >
                                            +{features.length - maxFeatures} more
                                        </Box>
                                    )}
                                </>
                            )}
                        </Box>
                        {/* Add to Garage Button at the bottom */}
                        <Box sx={{ mt: 1.5, width: '100%' }}>
                            <Button
                                variant='outlined'
                                color={garageAdded ? 'success' : 'primary'}
                                fullWidth
                                disabled={garageLoading}
                                onClick={garageAdded
                                    ? () => window.open('/my-garage', '_blank')
                                    : () => onGarageClick && onGarageClick(vehicle)
                                }
                                sx={{
                                    borderRadius: 2,
                                    fontWeight: 600,
                                    fontSize: 10,
                                    py: 1,
                                    background: garageAdded ? '#008000' : undefined,
                                    color: '#fff',
                                    '&:hover': {
                                        background: garageAdded ? undefined : '#4891FF',
                                        color: '#fff',
                                    },
                                }}
                            >
                                {garageLoading ? (
                                    <CircularProgress size={12} sx={{ color: garageAdded ? '#fff' : '#4891FF' }} />
                                ) : garageAdded ? (
                                    'View in Garage'
                                ) : (
                                    'Add to Garage'
                                )}
                            </Button>
                        </Box>
                    </>
                )}
            </Box>
        </Card>
    );
}

// Custom Carousel for Vehicle Cards (Mobile)
function VehicleCardsCarouselMobile({ vehicles, fallbackImg, truncateText, cardsToShow = 1, onInfoClick, vehicleDetailsLoading, selectedVehicle, onShowHighValueFeatures, vehicleDetails, garageLoading, garageAdded, onGarageClick }) {
    const [startIdx, setStartIdx] = useState(0);
    const total = vehicles.length;
    const maxStart = Math.max(0, total - cardsToShow);
    const canGoLeft = startIdx > 0;
    const canGoRight = startIdx < maxStart;

    const handlePrev = () => {
        setStartIdx(idx => Math.max(0, idx - 1));
    };
    const handleNext = () => {
        setStartIdx(idx => Math.min(maxStart, idx + 1));
    };

    return (
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', width: '100%', position: 'relative', gap: 1 }}>
            {/* Left arrow */}
            <IconButton onClick={handlePrev} disabled={!canGoLeft} sx={{ visibility: canGoLeft ? 'visible' : 'hidden' }}>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M15 6l-6 6 6 6" stroke="#4891FF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /></svg>
            </IconButton>
            {/* Cards */}
            <Box sx={{ display: 'flex', gap: 1, overflow: 'hidden', minWidth: 0 }}>
                {vehicles.slice(startIdx, startIdx + cardsToShow).map((vehicle, vIdx) => (
                    <VehicleCardWithDynamicFeaturesMobile
                        key={startIdx + vIdx}
                        vehicle={vehicle}
                        fallbackImg={fallbackImg}
                        truncateText={truncateText}
                        onInfoClick={onInfoClick}
                        vehicleDetailsLoading={vehicleDetailsLoading}
                        selectedVehicle={selectedVehicle}
                        onShowHighValueFeatures={onShowHighValueFeatures}
                        vehicleDetails={vehicleDetails}
                        garageLoading={garageLoading && garageLoading[vehicle.row_id]}
                        garageAdded={garageAdded && garageAdded[vehicle.row_id]}
                        onGarageClick={onGarageClick}
                    />
                ))}
            </Box>
            {/* Right arrow */}
            <IconButton onClick={handleNext} disabled={!canGoRight} sx={{ visibility: canGoRight ? 'visible' : 'hidden' }}>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M9 6l6 6-6 6" stroke="#4891FF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /></svg>
            </IconButton>
        </Box>
    );
}

function ThumbUpToggle({ msg, feedback, feedbackLoading, feedbackRes, feedbackFlagRes, onFeedbackGiven, onDislike, feedbackPending, setFeedbackPending }) {
    const [pending, setPending] = useState(false);
    useEffect(() => {
        if (pending && feedbackRes === msg.message_id && feedbackFlagRes === 1) {
            msg.thumbUpClicked = true;
            setPending(false);
            if (typeof onFeedbackGiven === 'function') onFeedbackGiven();
            if (setFeedbackPending) setFeedbackPending(false);
        }
    }, [feedbackRes, feedbackFlagRes, pending, onFeedbackGiven, msg, setFeedbackPending]);
    const handleClick = () => {
        if (typeof msg.onThumbUpClick === 'function') {
            msg.onThumbUpClick();
        }
        if (feedback && msg.message_id) {
            feedback(msg.message_id, 1, ''); // Like: flag=1, no message
            setPending(true);
            if (setFeedbackPending) setFeedbackPending(true);
        }
    };
    // Hide like icon if disliked
    if (msg._disliked) return null;
    if (pending || feedbackPending) {
        return <CircularProgress size={19} sx={{ color: '#4891FF' }} />;
    }
    return (feedbackRes === msg.message_id && feedbackFlagRes === 1) || msg.thumbUpClicked ? (
        <ThumbUpIcon sx={{ color: '#008000', fontSize: 15 }} />
    ) : (
        <IconButton onClick={handleClick} disabled={pending || feedbackPending}>
            <svg width="18px" height="18px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="SVGRepo_bgCarrier" strokeWidth="0" />
                <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />
                <g id="SVGRepo_iconCarrier"> <path d="M8 10V20M8 10L4 9.99998V20L8 20M8 10L13.1956 3.93847C13.6886 3.3633 14.4642 3.11604 15.1992 3.29977L15.2467 3.31166C16.5885 3.64711 17.1929 5.21057 16.4258 6.36135L14 9.99998H18.5604C19.8225 9.99998 20.7691 11.1546 20.5216 12.3922L19.3216 18.3922C19.1346 19.3271 18.3138 20 17.3604 20L8 20" stroke="#000000" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" /> </g>
            </svg>
        </IconButton>
    );
}

// Thumbs down icon component for action buttons row
const ThumbDownToggle = React.memo(function ThumbDownToggle({ msg, feedback, feedbackRes, feedbackFlagRes, onDislike, isDisliked, feedbackPending, setFeedbackPending }) {
    const [pending, setPending] = useState(false);
    const [localDisliked, setLocalDisliked] = useState(false);

    useEffect(() => {
        if (pending && feedbackRes === msg.message_id && feedbackFlagRes === 0) {
            msg._disliked = true;
            setPending(false);
            setLocalDisliked(true);
            if (typeof onDislike === 'function') onDislike();
            if (setFeedbackPending) setFeedbackPending(false);
        }
    }, [feedbackRes, feedbackFlagRes, pending, onDislike, msg, setFeedbackPending]);

    // If feedback was submitted locally (chip or 'Other'), keep the colored icon
    useEffect(() => {
        if (isDisliked) setLocalDisliked(true);
    }, [isDisliked]);

    const handleClick = () => {
        if (feedback && msg.message_id) {
            feedback(msg.message_id, 0, ''); // Dislike: flag=0, no message initially
            setPending(true);
            if (setFeedbackPending) setFeedbackPending(true);
        }
    };
    // Hide dislike icon if liked
    if (msg.thumbUpClicked) return null;

    if (pending || feedbackPending) {
        return <CircularProgress size={18} sx={{ color: '#FF0000' }} />;
    }

    // Show red icon if feedback just submitted, or if disliked
    if ((feedbackRes === msg.message_id && feedbackFlagRes === 0) || msg._disliked) {
        return (
            <IconButton disabled>
                <ThumbDownIcon sx={{ color: '#FF0000', fontSize: 18 }} />
            </IconButton>
        );
    }

    return (
        <IconButton size="small" onClick={handleClick} disabled={pending || feedbackPending}>
            <svg width="18px" height="18px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" transform="matrix(-1, 0, 0, 1, 0, 0)">
                <g id="SVGRepo_bgCarrier" strokeWidth="0" />
                <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />
                <g id="SVGRepo_iconCarrier"> <path d="M8 14V4M8 14L4 14V4.00002L8 4M8 14L13.1956 20.0615C13.6886 20.6367 14.4642 20.884 15.1992 20.7002L15.2467 20.6883C16.5885 20.3529 17.1929 18.7894 16.4258 17.6387L14 14H18.5604C19.8225 14 20.7691 12.8454 20.5216 11.6078L19.3216 5.60779C19.1346 4.67294 18.3138 4.00002 17.3604 4.00002L8 4" stroke="#000000" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" /> </g>
            </svg>
        </IconButton>
    );
});

// Feedback form component (callout)
const FeedbackCallout = React.memo(function FeedbackCallout({ msg, feedback, feedbackRes, feedbackFlagRes, hidden, isDisliked, onCancel }) {
    const [feedbackText, setFeedbackText] = useState("");
    const [selectedSuggestion, setSelectedSuggestion] = useState(null);
    const [submitted, setSubmitted] = useState(false);

    // Feedback suggestions
    const feedbackSuggestions = [
        "Response was inaccurate",
        "Response was incomplete",
        "Response was too long",
        "Response was not helpful",
        "Other"
    ];

    useEffect(() => {
        if (submitted) {
            const timeout = setTimeout(() => {
                setSubmitted(false);
                if (typeof onCancel === 'function') onCancel();
            }, 1200);
            return () => clearTimeout(timeout);
        }
    }, [submitted, onCancel]);

    const handleSuggestionClick = (suggestion) => {
        setSelectedSuggestion(suggestion);
        if (suggestion === "Other") {
            setFeedbackText("");
        } else {
            // Immediately submit feedback for non-Other
            if (feedback && msg.message_id) {
                feedback(msg.message_id, 0, suggestion);
            }
            setSubmitted(true);
        }
    };

    const handleSubmit = () => {
        if (feedback && msg.message_id) {
            feedback(msg.message_id, 0, feedbackText); // Dislike: flag=0, with message
        }
        setFeedbackText("");
        setSelectedSuggestion(null);
        setSubmitted(true);
    };

    const handleCancel = () => {
        setFeedbackText("");
        setSelectedSuggestion(null);
        if (typeof onCancel === 'function') onCancel();
    };
    if (hidden || !isDisliked) return null;

    if (submitted) {
        return (
            <Box sx={{ width: '100%', background: '#F8F9FA', borderRadius: 2, p: 2, border: '1px solid #E9ECEF', position: 'relative', textAlign: 'center' }}>
                <Typography variant="body2" sx={{ fontWeight: 600, color: '#212529', mb: 1 }}>
                    Feedback Submitted &nbsp;<CheckCircleOutlineIcon style={{ color: '#008000' }} />
                </Typography>
            </Box>
        );
    }
    return (
        <Box sx={{
            width: '100%',
            background: '#F8F9FA',
            borderRadius: 2,
            p: 2,
            border: '1px solid #E9ECEF',
            position: 'relative'
        }}>
            {/* Close button */}
            <IconButton
                onClick={handleCancel}
                sx={{
                    position: 'absolute',
                    top: 8,
                    right: 8,
                    color: '#6C757D',
                    '&:hover': { color: '#495057' }
                }}
            >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
            </IconButton>

            {/* Header */}
            <Typography variant="h6" sx={{
                fontWeight: 600,
                color: '#212529',
                mb: 1,
                pr: 4 // Space for close button
            }}>
                Help us improve
            </Typography>

            <Typography variant="body2" sx={{
                color: '#6C757D',
                mb: 2,
                fontSize: '14px'
            }}>
                What went wrong with this response?
            </Typography>

            {/* Suggestion chips */}
            <Box sx={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: 1,
                mb: selectedSuggestion === 'Other' ? 2 : 0
            }}>
                {feedbackSuggestions.map((suggestion, index) => (
                    <Box
                        key={index}
                        onClick={() => handleSuggestionClick(suggestion)}
                        sx={{
                            bgcolor: selectedSuggestion === suggestion ? '#4891FF' : '#E9ECEF',
                            color: selectedSuggestion === suggestion ? '#fff' : '#495057',
                            px: 2,
                            py: 0.75,
                            borderRadius: 2,
                            fontSize: '12px',
                            fontWeight: 500,
                            cursor: 'pointer',
                            transition: 'all 0.2s',
                            '&:hover': {
                                bgcolor: selectedSuggestion === suggestion ? '#357AE8' : '#DEE2E6'
                            }
                        }}
                    >
                        {suggestion}
                    </Box>
                ))}
            </Box>

            {/* Custom feedback text field, only for 'Other' */}
            {selectedSuggestion === 'Other' && (
                <>
                    <TextField
                        autoFocus
                        label="Additional feedback (required)"
                        multiline
                        minRows={2}
                        maxRows={4}
                        fullWidth
                        value={feedbackText}
                        onChange={e => setFeedbackText(e.target.value)}
                        variant="outlined"
                        size="small"
                        sx={{
                            mb: 2,
                            mt: 2,
                            '& .MuiOutlinedInput-root': {
                                borderRadius: 2,
                                '& fieldset': {
                                    borderColor: '#CED4DA',
                                },
                                '&:hover fieldset': {
                                    borderColor: '#ADB5BD',
                                },
                                '&.Mui-focused fieldset': {
                                    borderColor: '#4891FF',
                                },
                            },
                        }}
                    />
                    {/* Action buttons */}
                    <Box sx={{
                        display: 'flex',
                        gap: 1,
                        justifyContent: 'flex-end'
                    }}>
                        <Button
                            variant="outlined"
                            onClick={handleCancel}
                            sx={{
                                borderRadius: 2,
                                px: 3,
                                borderColor: '#CED4DA',
                                color: '#6C757D',
                                '&:hover': {
                                    borderColor: '#ADB5BD',
                                    backgroundColor: '#F8F9FA'
                                }
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="contained"
                            onClick={handleSubmit}
                            disabled={!feedbackText.trim()}
                            sx={{
                                borderRadius: 2,
                                px: 3,
                                bgcolor: '#4891FF',
                                '&:hover': {
                                    bgcolor: '#357AE8'
                                },
                                '&.Mui-disabled': {
                                    bgcolor: '#CED4DA',
                                    color: '#6C757D'
                                }
                            }}
                        >
                            Submit
                        </Button>
                    </Box>
                </>
            )}
        </Box>
    );
});

const NoVehiclesFound = ({ fetchAiDebugResponse, conversationId, conversation, onApplyClick }) => {
    const [distance, setDistance] = useState(100);
    // Only keep the latest callout
    const [callouts, setCallouts] = useState([]); // Array of { type, data, distance }
    const [loading, setLoading] = useState(false);
    const [lastApplyTime, setLastApplyTime] = useState(null);

    const handleApply = async () => {
        if (onApplyClick) onApplyClick();
        setLastApplyTime(new Date());
        setLoading(true);
        // Find the last AI message with is_inventory_fetch===1 and no vehicles
        const lastAiMsg = Array.isArray(conversation)
            ? [...conversation].reverse().find(
                msg => msg.sender === 'ai' && msg.is_inventory_fetch === 1 && (!msg.inventory_vehicles || msg.inventory_vehicles.length === 0)
            )
            : null;
        let aiResponseJson = null;
        if (lastAiMsg && typeof lastAiMsg.text === 'string') {
            // Extract JSON code block
            const match = lastAiMsg.text.match(/```json\s*([\s\S]*?)```/i);
            if (match && match[1]) {
                try {
                    aiResponseJson = JSON.parse(match[1]);
                } catch (e) {
                    aiResponseJson = null;
                }
            }
        }
        // Extract filters from aiResponseJson.filters if present
        const filters = aiResponseJson?.filters || null;
        const resp = await fetchAiDebugResponse(distance, conversationId, filters);
        setLoading(false);
        // Check for data in response
        const data = resp?.payload?.data || resp?.payload || resp?.data || [];
        if (Array.isArray(data) && data.length > 0) {
            setCallouts([
                { type: 'vehicles', data, distance }
            ]);
            setLastApplyTime(new Date());
        } else {
            if (distance < 250) {
                setCallouts([
                    { type: 'noVehicles', distance }
                ]);
                setDistance(distance === 50 ? 100 : 250); // next logical step
                setLastApplyTime(new Date());
            } else {
                setCallouts([
                    { type: 'noData', distance }
                ]);
                setLastApplyTime(new Date());
            }
        }
    };
    const marks = [
        {
            value: 50,
            label: (
                <span style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', fontSize: 10, }}>
                    50 miles
                    <span style={{ fontSize: 9, color: '#888' }}>(Previously Searched)</span>
                </span>
            ),
        },
        {
            value: 100,
            label: (
                <span style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', fontSize: 10, }}>
                    100 miles
                </span>
            ),
        },
        {
            value: 250,
            label: (
                <span style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', fontSize: 10, }}>
                    250 miles
                </span>
            ),
        },
    ];

    // Determine if Apply button should be disabled
    let disableApply = false;
    if (callouts.length > 0) {
        const lastCallout = callouts[callouts.length - 1];
        if (lastCallout.type === 'vehicles' || lastCallout.type === 'noData') {
            disableApply = true;
        }
    }

    return (
        <Box>
            <Typography variant="body2" sx={{ fontWeight: 500, color: '#222' }}>
                Thanks for sharing your preferences! Unfortunately I couldn't find any vehicles that perfectly match all the criteria you provided, within your locality.
            </Typography>
            <Typography variant="body2" sx={{ mt: 1, fontWeight: 500, color: '#222' }}>
                That said, we might still find some close alternatives with a small tweak — perhaps increasing the distance from the slider below. Once increased click on the apply button to get the updated result.
            </Typography>
            <Box sx={{ m: 4, display: 'flex', alignItems: 'center', gap: 2, flexDirection: 'column', }}>
                <Slider
                    value={distance}
                    onChange={(e, newValue) => setDistance(newValue)}
                    aria-labelledby="distance-slider"
                    valueLabelDisplay="auto"
                    step={null}
                    marks={marks}
                    min={50}
                    max={250}
                    sx={{ flex: 1, maxWidth: '100%' }}
                />
                <Button
                    variant="contained"
                    onClick={handleApply}
                    sx={{ whiteSpace: 'nowrap', minWidth: 80 }}
                    disabled={loading || disableApply}
                >
                    Apply
                </Button>
            </Box>
            <Typography variant="body2" sx={{ mt: 1, fontWeight: 500, color: '#222' }}>
                Want to search with a different make, model or may be another trim? type below, I am happy to help you. Happy Purchasing!
            </Typography>
            {/* Show loading indicator below encouragement message when loading */}
            {loading ? (
                <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <span style={{ color: '#A16BFE', fontStyle: 'italic', fontWeight: 500 }}>Fetching vehicles...</span>
                    <span className="dot" style={{ animation: 'wave 1s infinite', animationDelay: '0s' }}>.</span>
                    <span className="dot" style={{ animation: 'wave 1s infinite', animationDelay: '0.2s' }}>.</span>
                    <span className="dot" style={{ animation: 'wave 1s infinite', animationDelay: '0.4s' }}>.</span>
                </Box>
            ) : (
                callouts.length > 0 && callouts.map((callout, idx) => {
                    if (callout.type === 'vehicles') {
                        return (
                            <Box key={idx} sx={{ mt: 3 }}>
                                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 2 }}>Found Vehicles (distance: {callout.distance} miles):</Typography>
                                <VehicleCardsCarousel
                                    vehicles={sortVehiclesByPrice(callout.data)}
                                    fallbackImg={"/public/assets/gifs/loader.gif"}
                                    truncateText={str => str}
                                    cardsToShow={3}
                                />
                            </Box>
                        );
                    } else if (callout.type === 'noVehicles') {
                        return (
                            <Typography key={idx} variant="body2" sx={{ mt: 2, color: '#A16BFE', fontWeight: 600 }}>
                                No vehicles found for {callout.distance} miles. Try increasing the distance and searching again.
                            </Typography>
                        );
                    } else if (callout.type === 'noData') {
                        return (
                            <Typography key={idx} variant="body2" sx={{ mt: 2, color: '#FF0000', fontWeight: 600 }}>
                                Sorry, no vehicles found even within 250 miles. Please try changing your search criteria.
                            </Typography>
                        );
                    }
                    return null;
                })
            )}
        </Box>
    );
}

// Custom formatter for IST (Asia/Kolkata)
function formatTimestampAi(date) {
    if (!date) return '';
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
        timeZoneName: 'short'
    });
}

// Utility function to sort vehicles by price (ascending, missing prices last)
function sortVehiclesByPrice(vehicles) {
    if (!Array.isArray(vehicles)) return [];
    return [...vehicles].sort((a, b) => {
        const priceA = typeof a.price === 'number' ? a.price : Number(a.price);
        const priceB = typeof b.price === 'number' ? b.price : Number(b.price);
        if (isNaN(priceA)) return 1;
        if (isNaN(priceB)) return -1;
        return priceA - priceB;
    });
}