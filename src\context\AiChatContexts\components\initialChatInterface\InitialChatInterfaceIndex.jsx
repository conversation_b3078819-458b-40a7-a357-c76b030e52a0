import { useMediaQuery } from "react-responsive";
import InitialChatInterfaceMobile from "./InitialChatInterfaceMobile";
import InitialChatInterfaceWeb from "./InitialChatInterfaceWeb";

const InitialChatInterfaceIndex = ({ setIsChatSend, ...aiChatApi }) => {
    const isTabletOrMobile = useMediaQuery({ query: '(max-width: 928px)' });
    // Use props from parent instead of calling UseAiChatApi
    const chatProps = { ...aiChatApi, setIsChatSend };

    return (
        isTabletOrMobile ? <InitialChatInterfaceMobile {...chatProps} /> : <InitialChatInterfaceWeb {...chatProps} />
    );
};

export default InitialChatInterfaceIndex;

