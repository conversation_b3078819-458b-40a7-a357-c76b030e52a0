import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, InputAdornment, styled, TextField, Typography } from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import { Card } from "react-bootstrap";
import SpeechNotRecognizedDialog from '../../../../pages/search/modals/SpeechNotRecognizedDialog';
import useSpeechRecognition from '../../useSpeechRecognition';

const CustomQueryTextField = styled(TextField)(() => ({
    "& .MuiInputLabel-root": {
        color: "rgba(113, 113, 113, 0.5)",
        fontWeight: "bold",
        fontSize: "12px", // Smaller label
    },
    "& .MuiOutlinedInput-root": {
        "& fieldset": {
            border: 'none',
        },
        "&:hover fieldset": {
            borderColor: "#000",
        },
        "&.Mui-focused fieldset": {
            borderColor: "#000",
        },
        "& textarea": {
            fontSize: "12px", // Smaller input text
            fontWeight: 500,
            lineHeight: "1.4",
            resize: "none",
            "&::placeholder": {
                fontSize: "12px", // Smaller placeholder
                color: "rgba(113, 113, 113, 0.5)",
                opacity: 0.8,
            },
        },
        minHeight: "2.75rem",
        padding: "5px",
        // borderRadius: "10px",
    },
}));

const SuggestedStyledChip = styled(Chip)({
    marginTop: "8px",
    borderRadius: "10px",
    padding: "4px 6px",
    fontSize: "12px",
    fontWeight: 600,
    backgroundColor: "#fff",
    color: "#105DC7",
    textTransform: "capitalize",
    border: "2px solid #4891FF",
    boxShadow: "0 4px 12px rgba(72, 145, 255, 0.3)", // Soft blue shadow
    ".MuiChip-icon": {
        color: "#4891FF", // Icon color
    },

    "&:hover": {
        backgroundColor: "#105DC7",
        color: "#fff",
        cursor: "pointer",
        ".MuiChip-icon": {
            color: "#fff",
        },
    },
});

const InitialChatInterfaceMobile = ({ sendInitialQuery, userFirstName, query, setQuery, setIsChatSend }) => {
    // Use the custom speech recognition hook
    const {
        isListening,
        toggleSpeechRecognition,
        isSpeechDialogOpen,
        dialogText,
        handleDialogClose,
    } = useSpeechRecognition((transcript) => {
        setQuery((prev) => (prev + ' ' + transcript).trim());
    });

    const handleSend = () => {
        setIsChatSend(true);
        sendInitialQuery(query);
    };
    // Helper for chip click
    const handleChipClick = (chipValue) => {
        setQuery(chipValue);
        setIsChatSend(true);
        sendInitialQuery(chipValue);
    };

    return (

        <div className="d-flex flex-column h-100 w-100 px-2">
            <Card className="flex-grow-1 w-100 shadow-sm chat-card">
                <div className="d-flex flex-column h-100 p-4">
                    {/* TOP SECTION */}
                    <div className="flex-grow-1 d-flex justify-content-center align-items-end">
                        {/* Centered container for text + chips */}
                        <div
                            className="pb-4 d-flex flex-column"
                            style={{
                                textAlign: "left",
                                maxWidth: "700px",
                                width: "100%",
                            }}
                        >
                            {/* TEXT SECTION */}
                            <div className="mb-3">
                                <Typography
                                    gutterBottom
                                    variant="h4"
                                    sx={{
                                        background: "linear-gradient(90deg, #105DC7, #3E7FFF, #6EC4FF, #A16BFE)",
                                        backgroundSize: "65% auto",
                                        WebkitBackgroundClip: "text",
                                        WebkitTextFillColor: "transparent",
                                        fontWeight: "bold",
                                        marginBottom: 0,
                                    }}
                                >
                                    Hello {userFirstName ? userFirstName.charAt(0).toUpperCase() + userFirstName.slice(1).toLowerCase() : "User"}
                                </Typography>
                                <Typography
                                    gutterBottom
                                    variant="h6"
                                    sx={{ color: "#717171", opacity: "0.53", marginTop: 0 }}
                                >
                                    How can I assist you today?
                                </Typography>
                            </div>

                            {/* CHIPS SECTION */}
                            <div className="d-flex flex-wrap gap-2">
                                <SuggestedStyledChip icon={<SearchIcon />} label="Audi A4 Safety Info" onClick={() => handleChipClick("Audi A4 Safety Info")} />
                                <SuggestedStyledChip icon={<SearchIcon />} label="Electric cars for long-distance commuting under $80k" onClick={() => handleChipClick("Electric cars for long-distance commuting under $80k")} />
                                <SuggestedStyledChip icon={<SearchIcon />} label="ADAS-equipped sedans" onClick={() => handleChipClick("ADAS-equipped sedans")} />
                                <SuggestedStyledChip icon={<SearchIcon />} label="Show 4WD Ford Cars for mountain long drives" onClick={() => handleChipClick("Show 4WD Ford Cars for mountain long drives")} />
                                <SuggestedStyledChip icon={<SearchIcon />} label="Hybrid cars with over 40 MPG" onClick={() => handleChipClick("Hybrid cars with over 40 MPG")} />
                            </div>
                        </div>
                    </div>

                    {/* BOTTOM SECTION */}
                    <div className="flex-grow-1 d-flex justify-content-center align-items-center">
                        <div className="w-100 d-flex flex-column px-2 py-2" style={{ border: '1px solid #ccc', borderRadius: '8px', }}>
                            {/* TextArea */}
                            <CustomQueryTextField
                                fullWidth
                                multiline
                                maxRows={5}
                                id="outlined-basic"
                                placeholder="Ask Your Query..."
                                variant="outlined"
                                autoComplete="off"
                                value={query}
                                onChange={e => setQuery(e.target.value)}
                                onKeyDown={e => {
                                    if (e.key === 'Enter' && !e.shiftKey && query.trim()) {
                                        e.preventDefault();
                                        handleSend();
                                    }
                                }}
                                InputProps={{
                                    sx: {
                                        fontSize: "16px",
                                        paddingRight: 0, // No icon inside input
                                    },
                                }}
                                InputLabelProps={{
                                    sx: { fontSize: "16px" },
                                }}
                            />

                            {/* Icons aligned bottom-right */}
                            <div className="d-flex justify-content-between align-items-center">
                                {/* Voice Icon */}
                                <IconButton className={isListening ? "fab-listening" : ""} style={{ marginRight: "8px" }} onClick={toggleSpeechRecognition}>
                                    <svg fill="#105DC7" width="30px" height="30px" viewBox="0 0 56 56" xmlns="http://www.w3.org/2000/svg">

                                        <g id="SVGRepo_bgCarrier" strokeWidth="0" />

                                        <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />

                                        <g id="SVGRepo_iconCarrier">

                                            <path d="M 27.9999 51.9063 C 41.0546 51.9063 51.9063 41.0781 51.9063 28 C 51.9063 14.9453 41.0312 4.0937 27.9765 4.0937 C 14.8983 4.0937 4.0937 14.9453 4.0937 28 C 4.0937 41.0781 14.9218 51.9063 27.9999 51.9063 Z M 27.9999 47.9219 C 16.9374 47.9219 8.1014 39.0625 8.1014 28 C 8.1014 16.9609 16.9140 8.0781 27.9765 8.0781 C 39.0155 8.0781 47.8983 16.9609 47.9219 28 C 47.9454 39.0625 39.0390 47.9219 27.9999 47.9219 Z M 27.9999 31.3047 C 30.2968 31.3047 31.8905 29.5234 31.8905 27.1563 L 31.8905 17.9453 C 31.8905 15.5781 30.2968 13.7968 27.9999 13.7968 C 25.7030 13.7968 24.1093 15.5781 24.1093 17.9453 L 24.1093 27.1563 C 24.1093 29.5234 25.7030 31.3047 27.9999 31.3047 Z M 22.0468 40.6328 L 33.9765 40.6328 C 34.5155 40.6328 34.9843 40.1406 34.9843 39.6016 C 34.9843 39.0390 34.5155 38.5703 33.9765 38.5703 L 29.0312 38.5703 L 29.0312 35.7812 C 33.6952 35.3125 36.8124 31.8437 36.8124 27.1328 L 36.8124 24.1328 C 36.8124 23.5703 36.3671 23.1250 35.8280 23.1250 C 35.2890 23.1250 34.7968 23.5703 34.7968 24.1328 L 34.7968 27.1328 C 34.7968 31.0703 32.0312 33.9063 27.9999 33.9063 C 23.9452 33.9063 21.2030 31.0703 21.2030 27.1328 L 21.2030 24.1328 C 21.2030 23.5703 20.7109 23.1250 20.1718 23.1250 C 19.6562 23.1250 19.1874 23.5703 19.1874 24.1328 L 19.1874 27.1328 C 19.1874 31.8437 22.3046 35.3359 26.9687 35.7812 L 26.9687 38.5703 L 22.0468 38.5703 C 21.4843 38.5703 20.9921 39.0390 20.9921 39.6016 C 20.9921 40.1406 21.4843 40.6328 22.0468 40.6328 Z" />

                                        </g>
                                    </svg>
                                </IconButton>

                                {/* Send Icon */}
                                <IconButton
                                    style={{ cursor: "pointer" }}
                                    onClick={() => {
                                        if (query.trim()) {
                                            handleSend();
                                        }
                                    }}
                                >
                                    <svg width="28px" height="28px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">

                                        <g id="SVGRepo_bgCarrier" strokeWidth="0" />

                                        <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />

                                        <g id="SVGRepo_iconCarrier"> <path d="M10.3009 13.6949L20.102 3.89742M10.5795 14.1355L12.8019 18.5804C13.339 19.6545 13.6075 20.1916 13.9458 20.3356C14.2394 20.4606 14.575 20.4379 14.8492 20.2747C15.1651 20.0866 15.3591 19.5183 15.7472 18.3818L19.9463 6.08434C20.2845 5.09409 20.4535 4.59896 20.3378 4.27142C20.2371 3.98648 20.013 3.76234 19.7281 3.66167C19.4005 3.54595 18.9054 3.71502 17.9151 4.05315L5.61763 8.2523C4.48114 8.64037 3.91289 8.83441 3.72478 9.15032C3.56153 9.42447 3.53891 9.76007 3.66389 10.0536C3.80791 10.3919 4.34498 10.6605 5.41912 11.1975L9.86397 13.42C10.041 13.5085 10.1295 13.5527 10.2061 13.6118C10.2742 13.6643 10.3352 13.7253 10.3876 13.7933C10.4468 13.87 10.491 13.9585 10.5795 14.1355Z" stroke="#105DC7" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /> </g>
                                    </svg>
                                </IconButton>
                            </div>
                        </div>
                    </div>
                </div>
            </Card>
            <SpeechNotRecognizedDialog
                open={isSpeechDialogOpen}
                onClose={handleDialogClose}
                text={dialogText}
            />
        </div>
    );
};

export default InitialChatInterfaceMobile;
