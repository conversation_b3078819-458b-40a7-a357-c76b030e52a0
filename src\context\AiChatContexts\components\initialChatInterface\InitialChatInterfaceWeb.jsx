import { But<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>on, InputAdornment, styled, TextField, Typography } from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import { Card } from "react-bootstrap";
import SpeechNotRecognizedDialog from '../../../../pages/search/modals/SpeechNotRecognizedDialog';
import useSpeechRecognition from '../../useSpeechRecognition';

const StyledSendButton = styled(Button)({
    borderRadius: "10px",
    fontWeight: "bold",
    color: "#105DC7",
    padding: "12px 8px",
    backgroundColor: "#fff",
    border: "2px solid #4891FF",
    textTransform: "capitalize",
    boxShadow: "0 1px 5px #4891FF",

    "&:hover": {
        backgroundColor: "#105DC7",
        color: "#fff",

        "& svg": {
            fill: "#fff",
        },
    },

    // Disabled state
    "&.Mui-disabled": {
        backgroundColor: "rgba(72, 145, 255, 0.2)",
        color: "#fff",
        opacity: 0.6,
        cursor: "not-allowed",
    },
});

const CustomQueryTextField = styled(TextField)(() => ({
    "& .MuiInputLabel-root": {
        color: "rgba(113, 113, 113, 0.5)",
        fontWeight: "bold",
        fontSize: "14px", // Smaller label
    },
    "& .MuiOutlinedInput-root": {
        boxShadow: "0 0 5px 1px #A16BFE",
        "& fieldset": {
            borderColor: "#BDBCBC",
            borderWidth: "2px",
            borderRadius: "10px",
        },
        "&:hover fieldset": {
            borderColor: "#000",
        },
        "&.Mui-focused fieldset": {
            borderColor: "#000",
        },
        "& textarea": {
            fontSize: "14px", // Smaller input text
            fontWeight: 500,
            lineHeight: "1.5",
            paddingLeft: "10px",
            resize: "none",
            "&::placeholder": {
                fontSize: "14px", // Smaller placeholder
                color: "rgba(113, 113, 113, 0.5)",
                opacity: 0.8,
            },
        },
        minHeight: "3.5rem",
        padding: "5px",
        borderRadius: "10px",
    },
}));

const SuggestedStyledChip = styled(Chip)({
    marginTop: "8px",
    borderRadius: "10px",
    padding: "8px 12px",
    fontWeight: 600,
    backgroundColor: "#fff",
    color: "#105DC7",
    textTransform: "capitalize",
    border: "2px solid #4891FF",
    boxShadow: "0 4px 12px rgba(72, 145, 255, 0.3)", // Soft blue shadow
    ".MuiChip-icon": {
        color: "#4891FF", // Icon color
        marginRight: "6px",
    },

    "&:hover": {
        backgroundColor: "#105DC7",
        color: "#fff",
        cursor: "pointer",
        ".MuiChip-icon": {
            color: "#fff",
        },
    },
});

const InitialChatInterfaceWeb = ({ sendInitialQuery, userFirstName, query, setQuery, setIsChatSend }) => {
    // Use the custom speech recognition hook
    const {
        isListening,
        toggleSpeechRecognition,
        isSpeechDialogOpen,
        dialogText,
        handleDialogClose,
    } = useSpeechRecognition((transcript) => {
        setQuery((prev) => (prev + ' ' + transcript).trim());
    });

    const handleSend = () => {
        setIsChatSend(true);
        sendInitialQuery(query);
    };
    // Helper for chip click
    const handleChipClick = (chipValue) => {
        setQuery(chipValue);
        setIsChatSend(true);
        sendInitialQuery(chipValue);
    };
    return (
        <div className="d-flex flex-column h-100 w-100 px-0">
            <Card className="flex-grow-1 w-100 shadow-sm chat-card">
                <div className="d-flex flex-column h-100">
                    {/* TOP SECTION */}
                    <div className="flex-grow-1 d-flex justify-content-center align-items-end">
                        {/* Centered container for text + chips */}
                        <div
                            className="pb-4 d-flex flex-column"
                            style={{
                                textAlign: "left",
                                maxWidth: "700px",
                                width: "100%",
                            }}
                        >
                            {/* TEXT SECTION */}
                            <div className="mb-3">
                                <Typography
                                    gutterBottom
                                    variant="h2"
                                    sx={{
                                        background: "linear-gradient(90deg, #105DC7, #3E7FFF, #6EC4FF, #A16BFE)",
                                        backgroundSize: "45% auto",
                                        WebkitBackgroundClip: "text",
                                        WebkitTextFillColor: "transparent",
                                        fontWeight: "bold",
                                        marginBottom: 0,
                                    }}
                                >
                                    Hello {userFirstName ? userFirstName.charAt(0).toUpperCase() + userFirstName.slice(1).toLowerCase() : "User"}
                                </Typography>
                                <Typography
                                    gutterBottom
                                    variant="h4"
                                    sx={{ color: "#717171", opacity: "0.53", marginTop: 0 }}
                                >
                                    How can I assist you today?
                                </Typography>
                            </div>

                            {/* CHIPS SECTION */}
                            <div className="d-flex flex-wrap gap-2">
                                <SuggestedStyledChip
                                    icon={<SearchIcon />}
                                    label="Audi A4 Safety Info"
                                    onClick={() => handleChipClick("Audi A4 Safety Info")}
                                />
                                <SuggestedStyledChip
                                    icon={<SearchIcon />}
                                    label="Electric cars for long-distance commuting under $80k"
                                    onClick={() => handleChipClick("Electric cars for long-distance commuting under $80k")}
                                />
                                <SuggestedStyledChip
                                    icon={<SearchIcon />}
                                    label="ADAS-equipped sedans"
                                    onClick={() => handleChipClick("ADAS-equipped sedans")}
                                />
                                <SuggestedStyledChip
                                    icon={<SearchIcon />}
                                    label="Show 4WD Ford Cars for mountain long drives"
                                    onClick={() => handleChipClick("Show 4WD Ford Cars for mountain long drives")}
                                />
                                <SuggestedStyledChip
                                    icon={<SearchIcon />}
                                    label="Hybrid cars with over 40 MPG"
                                    onClick={() => handleChipClick("Hybrid cars with over 40 MPG")}
                                />
                            </div>
                        </div>
                    </div>

                    {/* BOTTOM SECTION */}
                    <div className="flex-grow-1 d-flex justify-content-center align-items-center">
                        <div className="d-flex align-items-center w-100 px-3" style={{ maxWidth: "850px" }}>
                            {/* Left: Text Field */}
                            <div className="flex-grow-1 me-2 mb-2">
                                <CustomQueryTextField
                                    fullWidth
                                    multiline
                                    maxRows={5}
                                    id="outlined-basic"
                                    placeholder="Ask Your Query to FastPass Vehicles AI Expert..."
                                    variant="outlined"
                                    autoComplete="off"
                                    value={query}
                                    onChange={e => setQuery(e.target.value)}
                                    onKeyDown={e => {
                                        if (e.key === 'Enter' && !e.shiftKey && query.trim()) {
                                            e.preventDefault();
                                            handleSend();
                                        }
                                    }}
                                    InputProps={{
                                        endAdornment: (
                                            <InputAdornment position="end" style={{ paddingRight: 4 }}>
                                                <IconButton className={isListening ? "fab-listening" : ""} style={{ cursor: "pointer" }} onClick={toggleSpeechRecognition}>
                                                    <svg fill="#105DC7" width="32px" height="32px" viewBox="-5.6 -5.6 67.20 67.20" xmlns="http://www.w3.org/2000/svg" stroke="#105DC7">

                                                        <g id="SVGRepo_bgCarrier" strokeWidth="0">

                                                            <rect x="-5.6" y="-5.6" width="67.20" height="67.20" rx="33.6" fill="#fff" strokewidth="0" />

                                                        </g>

                                                        <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />

                                                        <g id="SVGRepo_iconCarrier">

                                                            <path d="M 27.9999 51.9063 C 41.0546 51.9063 51.9063 41.0781 51.9063 28 C 51.9063 14.9453 41.0312 4.0937 27.9765 4.0937 C 14.8983 4.0937 4.0937 14.9453 4.0937 28 C 4.0937 41.0781 14.9218 51.9063 27.9999 51.9063 Z M 27.9999 47.9219 C 16.9374 47.9219 8.1014 39.0625 8.1014 28 C 8.1014 16.9609 16.9140 8.0781 27.9765 8.0781 C 39.0155 8.0781 47.8983 16.9609 47.9219 28 C 47.9454 39.0625 39.0390 47.9219 27.9999 47.9219 Z M 27.9999 31.3047 C 30.2968 31.3047 31.8905 29.5234 31.8905 27.1563 L 31.8905 17.9453 C 31.8905 15.5781 30.2968 13.7968 27.9999 13.7968 C 25.7030 13.7968 24.1093 15.5781 24.1093 17.9453 L 24.1093 27.1563 C 24.1093 29.5234 25.7030 31.3047 27.9999 31.3047 Z M 22.0468 40.6328 L 33.9765 40.6328 C 34.5155 40.6328 34.9843 40.1406 34.9843 39.6016 C 34.9843 39.0390 34.5155 38.5703 33.9765 38.5703 L 29.0312 38.5703 L 29.0312 35.7812 C 33.6952 35.3125 36.8124 31.8437 36.8124 27.1328 L 36.8124 24.1328 C 36.8124 23.5703 36.3671 23.1250 35.8280 23.1250 C 35.2890 23.1250 34.7968 23.5703 34.7968 24.1328 L 34.7968 27.1328 C 34.7968 31.0703 32.0312 33.9063 27.9999 33.9063 C 23.9452 33.9063 21.2030 31.0703 21.2030 27.1328 L 21.2030 24.1328 C 21.2030 23.5703 20.7109 23.1250 20.1718 23.1250 C 19.6562 23.1250 19.1874 23.5703 19.1874 24.1328 L 19.1874 27.1328 C 19.1874 31.8437 22.3046 35.3359 26.9687 35.7812 L 26.9687 38.5703 L 22.0468 38.5703 C 21.4843 38.5703 20.9921 39.0390 20.9921 39.6016 C 20.9921 40.1406 21.4843 40.6328 22.0468 40.6328 Z" />

                                                        </g>

                                                    </svg>
                                                </IconButton>
                                            </InputAdornment>
                                        ),
                                        sx: { fontSize: "14px" },
                                    }}
                                    InputLabelProps={{ sx: { fontSize: "14px" } }}
                                />
                            </div>

                            {/* Right: Button */}
                            <StyledSendButton onClick={() => {
                                if (query.trim()) {
                                    handleSend();
                                }
                            }} sx={{ mb: 1 }}>
                                <svg height="25px" width="25px" version="1.1" id="_x32_" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" viewBox="0 0 512 512" xmlSpace="preserve" fill="#105DC7">

                                    <g id="SVGRepo_bgCarrier" strokeWidth="0" />

                                    <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />

                                    <g id="SVGRepo_iconCarrier"> <g> <path className="st0" d="M247.355,106.9C222.705,82.241,205.833,39.18,197.46,0c-8.386,39.188-25.24,82.258-49.899,106.917 c-24.65,24.642-67.724,41.514-106.896,49.904c39.188,8.373,82.254,25.235,106.904,49.895c24.65,24.65,41.522,67.72,49.908,106.9 c8.373-39.188,25.24-82.258,49.886-106.917c24.65-24.65,67.724-41.514,106.896-49.904 C315.08,148.422,272.014,131.551,247.355,106.9z" /> <path className="st0" d="M407.471,304.339c-14.714-14.721-24.81-40.46-29.812-63.864c-5.011,23.404-15.073,49.142-29.803,63.872 c-14.73,14.714-40.464,24.801-63.864,29.812c23.408,5.01,49.134,15.081,63.864,29.811c14.73,14.722,24.81,40.46,29.82,63.864 c5.001-23.413,15.081-49.142,29.802-63.872c14.722-14.722,40.46-24.802,63.856-29.82 C447.939,329.14,422.201,319.061,407.471,304.339z" /> <path className="st0" d="M146.352,354.702c-4.207,19.648-12.655,41.263-25.019,53.626c-12.362,12.354-33.968,20.82-53.613,25.027 c19.645,4.216,41.251,12.656,53.613,25.027c12.364,12.362,20.829,33.96,25.036,53.618c4.203-19.658,12.655-41.255,25.023-53.626 c12.354-12.362,33.964-20.82,53.605-25.035c-19.64-4.2-41.251-12.656-53.613-25.019 C159.024,395.966,150.555,374.351,146.352,354.702z" /> </g> </g>

                                </svg>&nbsp;
                                Send
                            </StyledSendButton>
                        </div>
                    </div>
                </div>
            </Card >
            <SpeechNotRecognizedDialog
                open={isSpeechDialogOpen}
                onClose={handleDialogClose}
                text={dialogText}
            />
        </div >
    );
};

export default InitialChatInterfaceWeb;