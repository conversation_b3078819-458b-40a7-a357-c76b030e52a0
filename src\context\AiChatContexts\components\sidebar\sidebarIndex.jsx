import { useMediaQuery } from "react-responsive";
import SidebarMobile from "./sidebarMobile";
import <PERSON><PERSON><PERSON><PERSON> from "./sidebarWeb";

const SidebarIndex = ({ truncateText, isSidebarCollapsed, toggleSidebar, closeChat, setIsChatSend, resetChatState, value, loading, loadMoreLoading, loadMoreChatHistory, hasMoreData, searchText, setSearchText, totalChatCount, handleChatItemClick, selectedChatId, setIndividualChatPage, setHasMoreIndividualChat }) => {
    const isTabletOrMobile = useMediaQuery({ query: '(max-width: 928px)' });

    return (
        isTabletOrMobile ? <SidebarMobile isSidebarCollapsed={isSidebarCollapsed} truncateText={truncateText} toggleSidebar={toggleSidebar} closeChat={closeChat} setIsChatSend={setIsChatSend} resetChatState={resetChatState} value={value} loading={loading} loadMoreLoading={loadMoreLoading} loadMoreChatHistory={loadMoreChatHistory} hasMoreData={hasMoreData} searchText={searchText} setSearchText={setSearchText} totalChatCount={totalChatCount} handleChatItemClick={handleChatItemClick} selectedChatId={selectedChatId} setIndividualChatPage={setIndividualChatPage} setHasMoreIndividualChat={setHasMoreIndividualChat} /> : <SidebarWeb isSidebarCollapsed={isSidebarCollapsed} truncateText={truncateText} toggleSidebar={toggleSidebar} closeChat={closeChat} setIsChatSend={setIsChatSend} resetChatState={resetChatState} value={value} loading={loading} loadMoreLoading={loadMoreLoading} loadMoreChatHistory={loadMoreChatHistory} hasMoreData={hasMoreData} searchText={searchText} setSearchText={setSearchText} totalChatCount={totalChatCount} handleChatItemClick={handleChatItemClick} selectedChatId={selectedChatId} setIndividualChatPage={setIndividualChatPage} setHasMoreIndividualChat={setHasMoreIndividualChat} />
    );
};

export default SidebarIndex;

