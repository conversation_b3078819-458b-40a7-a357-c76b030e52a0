import { <PERSON>, <PERSON>ton, Chip, Divider, IconButton, styled, Paper, TextField, InputAdornment, CircularProgress } from "@mui/material";
import { ImageBaseURL } from "../../../../config";
import CloseIcon from "@mui/icons-material/Close";
import UseAiChatApi from "../../api/aiChatApi";

const StyledOverlay = styled(Box)(({ isSidebarCollapsed }) => ({
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    zIndex: 1100,
    display: !isSidebarCollapsed ? "block" : "none",
}));

const StyledChip = styled(Chip)({
    borderRadius: "10px",
    padding: "2px 4px",
    fontWeight: 600,
    backgroundColor: "#fff",
    color: "#000",
    textTransform: "capitalize",
    border: "2px solid #4891FF",
    fontSize: "12px",
});

const StyledLoadMoreButton = styled(Button)(({ theme }) => ({
    borderRadius: "10px",
    fontWeight: 600,
    color: "#4891FF",
    padding: "10px 16px",
    backgroundColor: "transparent",
    backdropFilter: "blur(4px)",
    WebkitBackdropFilter: "blur(4px)",
    border: "1px solid rgba(255, 255, 255, 0.2)",
    textTransform: "capitalize",
    transition: "all 0.3s ease",
    boxShadow: "none",

    // Responsive font size
    fontSize: "16px",
    [theme.breakpoints.down("sm")]: {
        fontSize: "14px",
        padding: "8px 12px",
    },

    "&:hover": {
        backgroundColor: "transparent",
        color: "#105DC7",
        boxShadow: "none",
    },

    "&.Mui-disabled": {
        backgroundColor: "rgba(72, 145, 255, 0.2)",
        color: "#fff",
        opacity: 0.6,
        cursor: "not-allowed",
    },
}));

const StyledSidebar = styled(Box)(({ theme }) => ({
    width: "280px",
    height: "100%",
    backgroundColor: "#f8f9fa",
    borderRight: "1px solid #e0e0e0",
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
    position: "absolute",
    left: 0,
    top: 0,
    zIndex: 1200,
    boxShadow: "2px 0 8px rgba(0,0,0,0.1)",
    transform: "translateX(-100%)",
}));

const StyledSidebarToggle = styled(IconButton)(({ theme }) => ({
    position: "absolute",
    top: "10px",
    left: "10px",
    zIndex: 1300,
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
    width: '100%', // Make the button take full width
}));

const StyledSidebarContent = styled(Box)(({ theme }) => ({
    padding: "16px",
    height: "100%",
    overflowY: "auto",
}));

const StyledButton = styled(Button)({
    borderRadius: "10px",
    width: "100%",
    fontWeight: "600",
    padding: "10px",
    backgroundColor: "#105DC7",
    textTransform: "capitalize",
    "&:hover": {
        backgroundColor: "#4891FF",
    },
    // Disabled state
    "&.Mui-disabled": {
        backgroundColor: "#4891FF",
        color: "#fff",
        opacity: 0.6,
        cursor: "not-allowed",
    },
});

const Item = styled(Paper)(({ theme }) => ({
    ...theme.typography.body2,
    padding: theme.spacing(1),
    textAlign: "center",
    borderRadius: "none",
    boxShadow: "none",
}));

const labelStyle = { color: "#4891FF", borderRadius: "10px" };

const CustomTextField = styled(TextField)(() => ({
    "& .MuiInputLabel-root": labelStyle,
    "& .MuiOutlinedInput-root": {
        "& fieldset": {
            borderColor: "#BDBCBC",
            borderWidth: "2px",
            borderRadius: "10px"
        },
        "&:hover fieldset": { borderColor: "#000", borderRadius: "10px" },
        "&.Mui-focused fieldset": { borderColor: "#000", borderRadius: "10px" },
        "& input": {
            fontSize: "14px",
            fontWeight: "semibold",
            "&::placeholder": { color: "black", opacity: 1 },
            borderRadius: "10px"
        },
        height: "2.75rem",
        padding: "5px",
        borderRadius: "10px"
    },
}));

const SidebarMobile = ({ isSidebarCollapsed, truncateText, toggleSidebar, setIsChatSend, resetChatState, value, loading, loadMoreLoading, loadMoreChatHistory, hasMoreData, searchText, setSearchText, totalChatCount, handleChatItemClick, selectedChatId, closeChat }) => {

    // Helper function to check if all sections are empty
    const isAllSectionsEmpty = () => {
        if (!value) return true;
        return (!value.today?.message || value.today.message.length === 0) &&
            (!value.yesterday?.message || value.yesterday.message.length === 0) &&
            (!value.this_week?.message || value.this_week.message.length === 0) &&
            (!value.this_month?.message || value.this_month.message.length === 0) &&
            (!value.older?.message || value.older.message.length === 0);
    };

    // Helper function to render chat history section
    const renderChatSection = (sectionName, sectionData, displayName, isLastSection) => {
        if (!sectionData?.message || sectionData.message.length === 0) {
            return null;
        }

        return (
            <>
                <Box sx={{ alignSelf: "center", mt: sectionName !== 'today' ? 2 : 0 }}>
                    <StyledChip label={displayName} variant="outlined" />
                </Box>
                <Box component="ul" className="list-unstyled" sx={{ width: '100%', textAlign: 'left', mb: isLastSection ? 4 : 0 }}>
                    {sectionData.message.map((chat, index) => (
                        <li
                            key={`${sectionName}-${chat._id || index}`}
                            className={`chat-list${selectedChatId === chat._id ? ' selected' : ''}`}
                            onClick={() => { handleChatItemClick(chat._id); setIsChatSend(true); toggleSidebar(); }}
                            style={{ cursor: 'pointer' }}
                        >
                            {truncateText(
                                (chat.title || 'Untitled Chat'),
                                25
                            )}
                        </li>
                    ))}
                </Box>
            </>
        );
    };

    const handleNewChat = () => {
        setIsChatSend(false);
        setHasMoreIndividualChat(false);
        resetChatState();
        toggleSidebar();
    };


    // Dynamically determine the last non-empty section
    const chatSections = [
        { name: 'today', data: value.today, label: 'Today' },
        { name: 'yesterday', data: value.yesterday, label: 'Yesterday' },
        { name: 'this_week', data: value.this_week, label: 'This Week' },
        { name: 'this_month', data: value.this_month, label: 'This Month' },
        { name: 'older', data: value.older, label: 'Older' },
    ];
    const nonEmptySections = chatSections.filter(section => section.data?.message && section.data.message.length > 0);
    const lastNonEmptySectionName = nonEmptySections.length > 0 ? nonEmptySections[nonEmptySections.length - 1].name : null;

    return (
        <>
            {/* Mobile Sidebar Toggle Button (outside sidebar when collapsed) */}
            {isSidebarCollapsed && (
                <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    width: '90%', // or your card's width
                    justifyContent: 'space-between',
                    position: 'absolute',
                    top: 11,
                    left: 16, // or adjust as needed
                    zIndex: 1300,
                    backgroundColor: '#fff',
                }}>
                    {/* Collapse (hamburger) icon on the left */}
                    <StyledSidebarToggle
                        onClick={toggleSidebar}
                        style={{ position: 'static', width: 'auto', marginRight: 'auto' }}
                    >
                        <svg width="40px" height="40px" viewBox="0 -0.5 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g id="SVGRepo_bgCarrier" strokeWidth="0" />
                            <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />
                            <g id="SVGRepo_iconCarrier">
                                <path d="M5.5 11.75C5.08579 11.75 4.75 12.0858 4.75 12.5C4.75 12.9142 5.08579 13.25 5.5 13.25V11.75ZM19.5 13.25C19.9142 13.25 20.25 12.9142 20.25 12.5C20.25 12.0858 19.9142 11.75 19.5 11.75V13.25ZM5.5 7.75C5.08579 7.75 4.75 8.08579 4.75 8.5C4.75 8.91421 5.08579 9.25 5.5 9.25V7.75ZM14.833 9.25C15.2472 9.25 15.583 8.91421 15.583 8.5C15.583 8.08579 15.2472 7.75 14.833 7.75V9.25ZM5.5 15.75C5.08579 15.75 4.75 16.0858 4.75 16.5C4.75 16.9142 5.08579 17.25 5.5 17.25V15.75ZM14.833 17.25C15.2472 17.25 15.583 16.9142 15.583 16.5C15.583 16.0858 15.2472 15.75 14.833 15.75V17.25ZM5.5 13.25H19.5V11.75H5.5V13.25ZM5.5 9.25H14.833V7.75H5.5V9.25ZM5.5 17.25H14.833V15.75H5.5V17.25Z" fill="#4891ff" />
                            </g>
                        </svg>
                    </StyledSidebarToggle>
                    {/* Close icon on the right */}
                    <div
                        className="ai-chat-close-icon"
                        style={{ marginLeft: 'auto', cursor: 'pointer' }}
                        onClick={closeChat}
                    >
                        <CloseIcon sx={{ color: '#fff' }} />
                    </div>
                </Box>
            )}

            {/* Sidebar */}
            <StyledSidebar style={{
                transform: isSidebarCollapsed ? "translateX(-100%)" : "translateX(0)",
                visibility: isSidebarCollapsed ? "hidden" : "visible",
                opacity: isSidebarCollapsed ? 0 : 1,
            }}>
                <StyledSidebarContent>
                    {/* Sidebar Header with Logo and Toggle Icon */}
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2, width: '100%' }}>
                        {/* Logo on the left when sidebar is open */}
                        {!isSidebarCollapsed && (
                            <>
                                <img
                                    src={ImageBaseURL + "logo_fp_blue.png"}
                                    className="ai-header-top-logo-mobile"
                                    alt="header"
                                    style={{ height: 32, width: 'auto', marginRight: 8 }}
                                />
                                {/* Sidebar toggle icon on the right (only when open) */}
                                <svg width="30px" height="30px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" onClick={toggleSidebar}>
                                    <g id="SVGRepo_bgCarrier" strokeWidth="0" />
                                    <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />
                                    <g id="SVGRepo_iconCarrier">
                                        <path fillRule="evenodd" clipRule="evenodd" d="M11.7071 4.29289C12.0976 4.68342 12.0976 5.31658 11.7071 5.70711L6.41421 11H20C20.5523 11 21 11.4477 21 12C21 12.5523 20.5523 13 20 13H6.41421L11.7071 18.2929C12.0976 18.6834 12.0976 19.3166 11.7071 19.7071C11.3166 20.0976 10.6834 20.0976 10.2929 19.7071L3.29289 12.7071C3.10536 12.5196 3 12.2652 3 12C3 11.7348 3.10536 11.4804 3.29289 11.2929L10.2929 4.29289C10.6834 3.90237 11.3166 3.90237 11.7071 4.29289Z" fill="#4891ff" />
                                    </g>
                                </svg>
                            </>
                        )}
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    {/* Chat List UI */}
                    <Box sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 1, // spacing between Chip and List
                    }}>
                        <Item
                            className="page_bg mt-2"
                            sx={{
                                display: "flex",
                                alignItems: "start",
                                justifyContent: "center",
                            }}
                        >
                            <CustomTextField
                                fullWidth
                                id="outlined-basic"
                                placeholder="Search..."
                                variant="outlined"
                                autoComplete="off"
                                value={searchText}
                                sx={{ backgroundColor: "#fff", borderRadius: "10px" }}
                                onChange={(e) => setSearchText(e.target.value)}
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            <Box sx={{ display: "flex", alignItems: "center" }}>
                                                <svg width="35px" height="35px" viewBox="0 -0.5 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">

                                                    <g id="SVGRepo_bgCarrier" strokeWidth="0" />

                                                    <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />

                                                    <g id="SVGRepo_iconCarrier"> <path fillRule="evenodd" clipRule="evenodd" d="M11.132 9.71395C10.139 11.2496 10.3328 13.2665 11.6 14.585C12.8468 15.885 14.8527 16.0883 16.335 15.065C16.6466 14.8505 16.9244 14.5906 17.159 14.294C17.3897 14.0023 17.5773 13.679 17.716 13.334C18.0006 12.6253 18.0742 11.8495 17.928 11.1C17.7841 10.3573 17.4268 9.67277 16.9 9.12995C16.3811 8.59347 15.7128 8.22552 14.982 8.07395C14.2541 7.92522 13.4982 8.00197 12.815 8.29395C12.1254 8.58951 11.5394 9.08388 11.132 9.71395Z" stroke="#000000" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" /> <path d="M17.5986 13.6868C17.2639 13.4428 16.7947 13.5165 16.5508 13.8513C16.3069 14.1861 16.3806 14.6552 16.7154 14.8991L17.5986 13.6868ZM19.0584 16.6061C19.3931 16.85 19.8623 16.7764 20.1062 16.4416C20.3501 16.1068 20.2764 15.6377 19.9416 15.3938L19.0584 16.6061ZM7.5 12.7499C7.91421 12.7499 8.25 12.4142 8.25 11.9999C8.25 11.5857 7.91421 11.2499 7.5 11.2499V12.7499ZM5.5 11.2499C5.08579 11.2499 4.75 11.5857 4.75 11.9999C4.75 12.4142 5.08579 12.7499 5.5 12.7499V11.2499ZM7.5 15.7499C7.91421 15.7499 8.25 15.4142 8.25 14.9999C8.25 14.5857 7.91421 14.2499 7.5 14.2499V15.7499ZM5.5 14.2499C5.08579 14.2499 4.75 14.5857 4.75 14.9999C4.75 15.4142 5.08579 15.7499 5.5 15.7499V14.2499ZM8.5 9.74994C8.91421 9.74994 9.25 9.41415 9.25 8.99994C9.25 8.58573 8.91421 8.24994 8.5 8.24994V9.74994ZM5.5 8.24994C5.08579 8.24994 4.75 8.58573 4.75 8.99994C4.75 9.41415 5.08579 9.74994 5.5 9.74994V8.24994ZM16.7154 14.8991L19.0584 16.6061L19.9416 15.3938L17.5986 13.6868L16.7154 14.8991ZM7.5 11.2499H5.5V12.7499H7.5V11.2499ZM7.5 14.2499H5.5V15.7499H7.5V14.2499ZM8.5 8.24994H5.5V9.74994H8.5V8.24994Z" fill="#000000" /> </g>

                                                </svg>
                                            </Box>
                                        </InputAdornment>
                                    ),
                                    sx: { fontSize: "16px" },
                                }}
                                InputLabelProps={{ sx: { fontSize: "16px" } }}
                            />
                        </Item>
                        <Item className="page_bg">
                            <StyledButton
                                variant="contained"
                                onClick={handleNewChat}
                            >
                                <svg width="20px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">

                                    <g id="SVGRepo_bgCarrier" strokeWidth="0" />

                                    <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />

                                    <g id="SVGRepo_iconCarrier"> <path d="M1 22C1 21.4477 1.44772 21 2 21H22C22.5523 21 23 21.4477 23 22C23 22.5523 22.5523 23 22 23H2C1.44772 23 1 22.5523 1 22Z" fill="#fff" /> <path fillRule="evenodd" clipRule="evenodd" d="M18.3056 1.87868C17.1341 0.707107 15.2346 0.707107 14.063 1.87868L3.38904 12.5526C2.9856 12.9561 2.70557 13.4662 2.5818 14.0232L2.04903 16.4206C1.73147 17.8496 3.00627 19.1244 4.43526 18.8069L6.83272 18.2741C7.38969 18.1503 7.89981 17.8703 8.30325 17.4669L18.9772 6.79289C20.1488 5.62132 20.1488 3.72183 18.9772 2.55025L18.3056 1.87868ZM15.4772 3.29289C15.8677 2.90237 16.5009 2.90237 16.8914 3.29289L17.563 3.96447C17.9535 4.35499 17.9535 4.98816 17.563 5.37868L15.6414 7.30026L13.5556 5.21448L15.4772 3.29289ZM12.1414 6.62869L4.80325 13.9669C4.66877 14.1013 4.57543 14.2714 4.53417 14.457L4.0014 16.8545L6.39886 16.3217C6.58452 16.2805 6.75456 16.1871 6.88904 16.0526L14.2272 8.71448L12.1414 6.62869Z" fill="#fff" /> </g>

                                </svg>&nbsp;
                                New Chat
                            </StyledButton>
                        </Item>
                        {/* Chat History Sections */}
                        {loading ? (
                            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>
                                <CircularProgress size={40} sx={{ color: "#4891FF" }} />
                            </Box>
                        ) : isAllSectionsEmpty() ? (
                            <Box sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                justifyContent: 'center',
                                alignItems: 'center',
                                minHeight: '200px',
                                textAlign: 'center',
                                color: '#666',
                                gap: 1,
                                px: 2
                            }}>
                                <Box sx={{ fontSize: '48px', opacity: 0.3 }}>🔍</Box>
                                <Box sx={{ fontSize: '16px', fontWeight: 500 }}>
                                    {searchText ? 'No results found' : 'No chat history'}
                                </Box>
                                <Box sx={{ fontSize: '14px', opacity: 0.7 }}>
                                    {searchText ? `Try searching for something else` : 'Start a conversation to see your chat history'}
                                </Box>
                            </Box>
                        ) : (
                            <>
                                {/* Only apply mb:4 to the last non-empty section */}
                                {chatSections.map(section =>
                                    renderChatSection(
                                        section.name,
                                        section.data,
                                        section.label,
                                        section.name === lastNonEmptySectionName
                                    )
                                )}
                            </>
                        )}
                        {hasMoreData && totalChatCount >= 10 && (
                            <Box
                                sx={{
                                    position: "absolute",
                                    bottom: 0,
                                    left: "50%",
                                    transform: "translateX(-50%)",
                                    zIndex: 2,
                                    width: "100%",
                                    px: 2, // horizontal padding for mobile spacing
                                }}
                            >
                                <StyledLoadMoreButton
                                    variant="contained"
                                    fullWidth
                                    onClick={loadMoreChatHistory}
                                    disabled={loadMoreLoading}
                                >
                                    {loadMoreLoading ? 'Loading...' : 'Load More Chats'}
                                </StyledLoadMoreButton>
                            </Box>
                        )}
                    </Box>
                </StyledSidebarContent>
            </StyledSidebar>

            {/* Overlay for mobile */}
            <StyledOverlay
                isSidebarCollapsed={isSidebarCollapsed}
                onClick={toggleSidebar}
            />
        </>
    );
};

export default SidebarMobile;

