import { Box, Button, Chip, InputAdornment, Paper, styled, TextField, Tooltip, CircularProgress } from "@mui/material";
import { ImageBaseURL } from "../../../../config";
import Grid2 from "@mui/material/Unstable_Grid2";
import UseAiChatApi from "../../api/aiChatApi";
const Item = styled(Paper)(({ theme }) => ({
    ...theme.typography.body2,
    padding: theme.spacing(1),
    textAlign: "center",
    borderRadius: "none",
    boxShadow: "none",
}));

const labelStyle = { color: "#4891FF", borderRadius: "10px" };

const CustomTextField = styled(TextField)(() => ({
    "& .MuiInputLabel-root": labelStyle,
    "& .MuiOutlinedInput-root": {
        "& fieldset": {
            borderColor: "#BDBCBC",
            borderWidth: "2px",
            borderRadius: "10px"
        },
        "&:hover fieldset": { borderColor: "#000", borderRadius: "10px" },
        "&.Mui-focused fieldset": { borderColor: "#000", borderRadius: "10px" },
        "& input": {
            fontSize: "14px",
            fontWeight: "semibold",
            "&::placeholder": { color: "black", opacity: 1 },
            borderRadius: "10px"
        },
        height: "2.75rem",
        padding: "5px",
        borderRadius: "10px"
    },
}));

const StyledButton = styled(Button)({
    borderRadius: "10px",
    width: "100%",
    fontWeight: "600",
    padding: "10px",
    backgroundColor: "#105DC7",
    textTransform: "capitalize",
    "&:hover": {
        backgroundColor: "#4891FF",
    },
    // Disabled state
    "&.Mui-disabled": {
        backgroundColor: "#4891FF",
        color: "#fff",
        opacity: 0.6,
        cursor: "not-allowed",
    },
});

const StyledChip = styled(Chip)({
    borderRadius: "10px",
    padding: "2px 4px",
    fontWeight: 600,
    backgroundColor: "#fff",
    color: "#000",
    textTransform: "capitalize",
    border: "2px solid #4891FF",
    fontSize: "12px",
});

const StyledLoadMoreButton = styled(Button)({
    borderRadius: "10px",
    width: "100vw",
    fontWeight: "600",
    color: "#4891FF",
    padding: "10px",
    backgroundColor: "transparent", // fully transparent
    backdropFilter: "blur(3px)",   // pure glass blur
    WebkitBackdropFilter: "blur(3px)",
    border: "1px solid rgba(255, 255, 255, 0.2)", // optional faint border
    textTransform: "capitalize",
    transition: "all 0.3s ease", // Smooth hover
    boxShadow: "none",

    "&:hover": {
        backgroundColor: "transparent",
        color: "#105DC7",
        boxShadow: "none",
    },

    // Disabled state
    "&.Mui-disabled": {
        backgroundColor: "rgba(72, 145, 255, 0.2)",
        color: "#fff",
        opacity: 0.6,
        cursor: "not-allowed",
    },
});

const SidebarWeb = ({ isSidebarCollapsed, truncateText, toggleSidebar, setIsChatSend, resetChatState, value, loading, loadMoreLoading, loadMoreChatHistory, hasMoreData, searchText, setSearchText, totalChatCount, handleChatItemClick, selectedChatId }) => {

    // Helper function to check if all sections are empty
    const isAllSectionsEmpty = () => {
        return (!value.today?.message || value.today.message.length === 0) &&
            (!value.yesterday?.message || value.yesterday.message.length === 0) &&
            (!value.this_week?.message || value.this_week.message.length === 0) &&
            (!value.this_month?.message || value.this_month.message.length === 0) &&
            (!value.older?.message || value.older.message.length === 0);
    };

    // Helper function to render chat history section
    const renderChatSection = (sectionName, sectionData, displayName, isLastSection) => {
        if (!sectionData?.message || sectionData.message.length === 0) {
            return null;
        }

        return (
            <>
                <Box sx={{ alignSelf: "center" }}>
                    <StyledChip label={displayName} variant="outlined" />
                </Box>
                <Box component="ul" className="list-unstyled" sx={{ width: '100%', textAlign: 'left', mb: isLastSection ? 4 : 0 }}>
                    {sectionData.message.map((chat, index) => (
                        <li
                            key={`${sectionName}-${chat._id || index}`}
                            className={`chat-list${selectedChatId === chat._id ? ' selected' : ''}`}
                            onClick={() => { handleChatItemClick(chat._id); setIsChatSend(true); }}
                            style={{ cursor: 'pointer' }}
                        >
                            <Tooltip disableFocusListener title={chat?.title || 'Chat'}>
                                {truncateText(
                                    (chat.title || 'Untitled Chat'),
                                    25
                                )}
                            </Tooltip>
                        </li>
                    ))}
                </Box>
            </>
        );
    };
    const handleNewChat = () => {
        setIsChatSend(false);
        setHasMoreIndividualChat(false);
        resetChatState();
    };
    // Dynamically determine the last non-empty section
    const chatSections = [
        { name: 'today', data: value.today, label: 'Today' },
        { name: 'yesterday', data: value.yesterday, label: 'Yesterday' },
        { name: 'this_week', data: value.this_week, label: 'This Week' },
        { name: 'this_month', data: value.this_month, label: 'This Month' },
        { name: 'older', data: value.older, label: 'Older' },
    ];
    const nonEmptySections = chatSections.filter(section => section.data?.message && section.data.message.length > 0);
    const lastNonEmptySectionName = nonEmptySections.length > 0 ? nonEmptySections[nonEmptySections.length - 1].name : null;
    return (
        <div
            className={`sidebar d-flex flex-column mx-2 ${isSidebarCollapsed ? "collapsed" : ""}`}
            style={{ marginTop: 32 }}
        >

            <div className="d-flex align-items-center justify-content-between">
                {!isSidebarCollapsed && (
                    <img
                        src={ImageBaseURL + "fp_logo.png"}
                        className="ai-header-top-logo"
                        alt="header"
                    />
                )}
                <Button
                    variant="light"
                    size="sm"
                    onClick={toggleSidebar}
                    className={`align-self-end px-0 collapse-btn ${isSidebarCollapsed ? "" : "justify-content-end"}`}
                >
                    {isSidebarCollapsed ? (
                        <svg xmlns="http://www.w3.org/2000/svg" width="25px" height="25px" viewBox="0 0 24 24" fill="none" stroke="#000000" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">

                            <g id="SVGRepo_bgCarrier" strokeWidth="0" />

                            <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />

                            <g id="SVGRepo_iconCarrier"> <rect x="3" y="3" width="18" height="18" rx="2" ry="2" /> <line x1="9" y1="3" x2="9" y2="21" /> <path d="M13 8l4 4-4 4" /> </g>

                        </svg>
                    ) : (
                        <svg xmlns="http://www.w3.org/2000/svg" width="25px" height="25px" viewBox="0 0 24 24" fill="none" stroke="#000000" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <g id="SVGRepo_bgCarrier" strokeWidth="0" />
                            <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />
                            <g id="SVGRepo_iconCarrier">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
                                <line x1="9" y1="3" x2="9" y2="21" />
                                <path d="M17 16l-4-4 4-4" />
                            </g>
                        </svg>
                    )}
                </Button>
            </div>
            {!isSidebarCollapsed && (
                <Grid2 container spacing={1} sx={{ height: "calc(100vh - 150px)" }}>
                    <Grid2 item xs={12}>
                        <Item
                            className="page_bg mt-2"
                            sx={{
                                display: "flex",
                                alignItems: "start",
                                justifyContent: "center",
                            }}
                        >
                            <CustomTextField
                                fullWidth
                                id="outlined-basic"
                                placeholder="Search..."
                                variant="outlined"
                                autoComplete="off"
                                value={searchText}
                                sx={{ backgroundColor: "#fff", borderRadius: "10px" }}
                                onChange={(e) => setSearchText(e.target.value)}
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            <Box sx={{ display: "flex", alignItems: "center" }}>
                                                <svg width="35px" height="35px" viewBox="0 -0.5 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">

                                                    <g id="SVGRepo_bgCarrier" strokeWidth="0" />

                                                    <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />

                                                    <g id="SVGRepo_iconCarrier"> <path fillRule="evenodd" clipRule="evenodd" d="M11.132 9.71395C10.139 11.2496 10.3328 13.2665 11.6 14.585C12.8468 15.885 14.8527 16.0883 16.335 15.065C16.6466 14.8505 16.9244 14.5906 17.159 14.294C17.3897 14.0023 17.5773 13.679 17.716 13.334C18.0006 12.6253 18.0742 11.8495 17.928 11.1C17.7841 10.3573 17.4268 9.67277 16.9 9.12995C16.3811 8.59347 15.7128 8.22552 14.982 8.07395C14.2541 7.92522 13.4982 8.00197 12.815 8.29395C12.1254 8.58951 11.5394 9.08388 11.132 9.71395Z" stroke="#000000" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" /> <path d="M17.5986 13.6868C17.2639 13.4428 16.7947 13.5165 16.5508 13.8513C16.3069 14.1861 16.3806 14.6552 16.7154 14.8991L17.5986 13.6868ZM19.0584 16.6061C19.3931 16.85 19.8623 16.7764 20.1062 16.4416C20.3501 16.1068 20.2764 15.6377 19.9416 15.3938L19.0584 16.6061ZM7.5 12.7499C7.91421 12.7499 8.25 12.4142 8.25 11.9999C8.25 11.5857 7.91421 11.2499 7.5 11.2499V12.7499ZM5.5 11.2499C5.08579 11.2499 4.75 11.5857 4.75 11.9999C4.75 12.4142 5.08579 12.7499 5.5 12.7499V11.2499ZM7.5 15.7499C7.91421 15.7499 8.25 15.4142 8.25 14.9999C8.25 14.5857 7.91421 14.2499 7.5 14.2499V15.7499ZM5.5 14.2499C5.08579 14.2499 4.75 14.5857 4.75 14.9999C4.75 15.4142 5.08579 15.7499 5.5 15.7499V14.2499ZM8.5 9.74994C8.91421 9.74994 9.25 9.41415 9.25 8.99994C9.25 8.58573 8.91421 8.24994 8.5 8.24994V9.74994ZM5.5 8.24994C5.08579 8.24994 4.75 8.58573 4.75 8.99994C4.75 9.41415 5.08579 9.74994 5.5 9.74994V8.24994ZM16.7154 14.8991L19.0584 16.6061L19.9416 15.3938L17.5986 13.6868L16.7154 14.8991ZM7.5 11.2499H5.5V12.7499H7.5V11.2499ZM7.5 14.2499H5.5V15.7499H7.5V14.2499ZM8.5 8.24994H5.5V9.74994H8.5V8.24994Z" fill="#000000" /> </g>

                                                </svg>
                                            </Box>
                                        </InputAdornment>
                                    ),
                                    sx: { fontSize: "16px" },
                                }}
                                InputLabelProps={{ sx: { fontSize: "16px" } }}
                            />
                        </Item>
                        <Item className="page_bg">
                            <StyledButton
                                variant="contained"
                                onClick={handleNewChat}
                            >
                                <svg width="20px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">

                                    <g id="SVGRepo_bgCarrier" strokeWidth="0" />

                                    <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />

                                    <g id="SVGRepo_iconCarrier"> <path d="M1 22C1 21.4477 1.44772 21 2 21H22C22.5523 21 23 21.4477 23 22C23 22.5523 22.5523 23 22 23H2C1.44772 23 1 22.5523 1 22Z" fill="#fff" /> <path fillRule="evenodd" clipRule="evenodd" d="M18.3056 1.87868C17.1341 0.707107 15.2346 0.707107 14.063 1.87868L3.38904 12.5526C2.9856 12.9561 2.70557 13.4662 2.5818 14.0232L2.04903 16.4206C1.73147 17.8496 3.00627 19.1244 4.43526 18.8069L6.83272 18.2741C7.38969 18.1503 7.89981 17.8703 8.30325 17.4669L18.9772 6.79289C20.1488 5.62132 20.1488 3.72183 18.9772 2.55025L18.3056 1.87868ZM15.4772 3.29289C15.8677 2.90237 16.5009 2.90237 16.8914 3.29289L17.563 3.96447C17.9535 4.35499 17.9535 4.98816 17.563 5.37868L15.6414 7.30026L13.5556 5.21448L15.4772 3.29289ZM12.1414 6.62869L4.80325 13.9669C4.66877 14.1013 4.57543 14.2714 4.53417 14.457L4.0014 16.8545L6.39886 16.3217C6.58452 16.2805 6.75456 16.1871 6.88904 16.0526L14.2272 8.71448L12.1414 6.62869Z" fill="#fff" /> </g>

                                </svg>&nbsp;
                                New Chat
                            </StyledButton>
                        </Item>
                        <Item
                            className="page_bg mt-2"
                            sx={{
                                position: "relative", // ✅ allow absolute positioning for button
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "flex-start",
                                justifyContent: "flex-start",
                                gap: 2,
                                flex: 1,
                                height: "calc(100vh - 300px)", // ✅ fixed height
                            }}
                        >
                            <Box
                                sx={{
                                    overflowY: "auto",
                                    flex: 1,
                                    width: '100%',
                                    paddingBottom: '20px',
                                }}
                                className="sleek-scrollbar"
                            >
                                <Box
                                    sx={{
                                        display: "flex",
                                        flexDirection: "column",
                                        gap: 2,
                                        marginRight: 0.5
                                    }}
                                >
                                    {loading ? (
                                        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>
                                            <CircularProgress size={40} sx={{ color: "#4891FF" }} />
                                        </Box>
                                    ) : isAllSectionsEmpty() ? (
                                        <Box sx={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            minHeight: '200px',
                                            textAlign: 'center',
                                            color: '#666',
                                            gap: 1
                                        }}>
                                            <Box sx={{ fontSize: '48px', opacity: 0.3 }}>🔍</Box>
                                            <Box sx={{ fontSize: '16px', fontWeight: 500 }}>
                                                {searchText ? 'No results found' : 'No chat history'}
                                            </Box>
                                            <Box sx={{ fontSize: '14px', opacity: 0.7 }}>
                                                {searchText ? `Try searching for something else` : 'Start a conversation to see your chat history'}
                                            </Box>
                                        </Box>
                                    ) : (
                                        <>
                                            {/* Only apply mb:4 to the last non-empty section */}
                                            {chatSections.map(section =>
                                                renderChatSection(
                                                    section.name,
                                                    section.data,
                                                    section.label,
                                                    section.name === lastNonEmptySectionName
                                                )
                                            )}
                                        </>
                                    )}
                                </Box>
                            </Box>

                            {hasMoreData && totalChatCount >= 10 && (
                                <Box
                                    sx={{
                                        position: "absolute",
                                        bottom: 8,
                                        left: "50%",
                                        transform: "translateX(-50%)",
                                        zIndex: 2,
                                    }}
                                >
                                    <StyledLoadMoreButton
                                        variant="contained"
                                        fullWidth
                                        onClick={loadMoreChatHistory}
                                        disabled={loadMoreLoading}
                                    >
                                        {loadMoreLoading ? 'Loading...' : 'Load More Chats'}
                                    </StyledLoadMoreButton>
                                </Box>
                            )}
                        </Item>
                    </Grid2>
                </Grid2>
            )}
        </div>
    );
};

export default SidebarWeb;

