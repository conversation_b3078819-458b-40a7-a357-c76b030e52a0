import { useRef, useState, useEffect, useCallback } from 'react';

/**
 * useSpeechRecognition - Custom hook for continuous speech-to-text with mic button toggle.
 * @param {function} onTranscript - Callback called with each final transcript string.
 * @returns {object} { isListening, toggleSpeechRecognition, isSpeechDialogOpen, dialogText, handleDialogClose }
 */
export default function useSpeechRecognition(onTranscript) {
    const [isListening, setIsListening] = useState(false);
    const [isSpeechDialogOpen, setIsSpeechDialogOpen] = useState(false);
    const [dialogText, setDialogText] = useState("");
    const recognitionRef = useRef(null);

    // Clean up recognition instance on unmount
    useEffect(() => {
        return () => {
            if (recognitionRef.current) {
                recognitionRef.current.onresult = null;
                recognitionRef.current.onend = null;
                recognitionRef.current.onerror = null;
                recognitionRef.current.onstart = null;
                recognitionRef.current.stop();
                recognitionRef.current = null;
            }
        };
    }, []);

    const handleDialogClose = useCallback(() => {
        setIsSpeechDialogOpen(false);
        setDialogText("");
    }, []);

    const toggleSpeechRecognition = useCallback(() => {
        if (!("SpeechRecognition" in window || "webkitSpeechRecognition" in window)) {
            setIsSpeechDialogOpen(true);
            setDialogText("Your current browser does not support speech to text functionality. Please switch to a supported browser like Chrome, Edge, or Safari.");
            return;
        }
        // If already listening, stop and clean up
        if (isListening && recognitionRef.current) {
            recognitionRef.current.stop();
            setIsListening(false);
            return;
        }
        // Always create a new instance for best accuracy
        const recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
        recognition.lang = "en-US";
        recognition.continuous = true;
        recognition.interimResults = false;
        recognition.onstart = () => {
            setIsListening(true);
        };
        recognition.onresult = (event) => {
            let transcript = "";
            for (let i = event.resultIndex; i < event.results.length; i++) {
                if (event.results[i].isFinal) {
                    transcript += event.results[i][0].transcript;
                }
            }
            if (transcript.trim() && typeof onTranscript === 'function') {
                onTranscript(transcript.trim());
            }
        };
        recognition.onerror = (event) => {
            console.error("Speech recognition error:", event.error);
            if (event.error === "service-not-allowed") {
                setIsSpeechDialogOpen(true);
                setDialogText("Your current browser does not support speech to text functionality. Please switch to another browser.");
            }
            setIsListening(false);
        };
        recognition.onend = () => {
            // If still supposed to be listening, restart (for continuous mode)
            if (isListening) {
                recognition.start();
            } else {
                setIsListening(false);
            }
        };
        recognitionRef.current = recognition;
        recognition.start();
    }, [isListening, onTranscript]);

    return {
        isListening,
        toggleSpeechRecognition,
        isSpeechDialogOpen,
        dialogText,
        handleDialogClose,
    };
} 