// FloatingAiBot.jsx

import { IconButton, Tooltip } from "@mui/material";
import { ImageBaseURL } from "../config";
import PropTypes from "prop-types";
import { useEffect, useState } from "react";
import CloseIcon from "@mui/icons-material/Close";
import UseAiChatApi from "./AiChatContexts/api/aiChatApi";

const FloatingAiBot = ({ onClick }) => {
    const { userFirstName } = UseAiChatApi();

    const animationURL = `${ImageBaseURL}ai_bot_4.gif`;
    const [openTooltip, setOpenTooltip] = useState(false);

    useEffect(() => {
        setOpenTooltip(true);
    }, []);


    const handleTooltipClose = (e) => {
        e.stopPropagation(); // prevent bot click trigger
        setOpenTooltip(false);
    };

    const tooltipContent = (
        <div style={{ maxWidth: "250px", position: "relative" }}>
            <IconButton
                size="small"
                onClick={handleTooltipClose}
                style={{
                    position: "absolute",
                    top: 0,
                    right: 0,
                    padding: "2px",
                    color: "#fff",
                }}
            >
                <CloseIcon fontSize="inherit" style={{ fontSize: "16px" }} />
            </IconButton>
            <div style={{ paddingRight: "15px", fontSize: "12px", color: "#fff" }}>
                Hi {userFirstName ? userFirstName.charAt(0).toUpperCase() + userFirstName.slice(1).toLowerCase() : "User"}👋 I'm your FastPass AI Assistant!
            </div>
        </div>
    );

    return (
        <div
            onClick={onClick}
            className='ai-bot-container'
        >
            <Tooltip
                title={tooltipContent}
                open={openTooltip}
                arrow
                placement="top-end"
                PopperProps={{
                    modifiers: [
                        {
                            name: 'offset',
                            options: {
                                offset: [0, -15],
                            },
                        },
                    ],
                }}
            >
                <img
                    src={animationURL}
                    alt="AI Bot"
                    className='ai-bot-img'
                />
            </Tooltip>
        </div>
    );
};

FloatingAiBot.propTypes = {
    onClick: PropTypes.func.isRequired,
};

export default FloatingAiBot;

