import React, { createContext, useContext, useState, useEffect } from 'react';

const ModalContext = createContext();

export const ModalProvider = ({ children }) => {
  const [showChatSelectionModal, setShowChatSelectionModal] = useState(false);

    // Handle scroll lock when modal opens or closes
    useEffect(() => {
      if (showChatSelectionModal) {
        document.body.style.overflow = "hidden"; // Prevent scrolling when modal is open
      } else {
        document.body.style.overflow = "auto"; // Restore scrolling when modal is closed
      }
  
      return () => {
        document.body.style.overflow = "auto"; // Ensure cleanup on unmount
      };
    }, [showChatSelectionModal]);

  return (
    <ModalContext.Provider value={{ showChatSelectionModal, setShowChatSelectionModal }}>
      {children}
    </ModalContext.Provider>
  );
};

export const useModal = () => useContext(ModalContext); 