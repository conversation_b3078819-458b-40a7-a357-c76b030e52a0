import { createContext, useState, useContext } from "react";
import PropTypes from 'prop-types';

// Create the Notification Context
const NotificationContext = createContext();

// Custom Hook for consuming the context
export const useNotification = () => useContext(NotificationContext);

// Provider Component
export const NotificationProvider = ({ children }) => {
  const [hasNewMessage, setHasNewMessage] = useState(false); // State to track new messages
  const [pusherData, setPusherData] = useState(null); // State to store Pusher data (like chatId)
  const [unreadMessages, setUnreadMessages] = useState([]);

  const addUnreadMessage = (msgType) => {
    setUnreadMessages((prev) =>
      prev.includes(msgType) ? prev : [...prev, msgType]
    );
  };

  // Remove a message type from the unread list when the chat is opened
  const clearUnreadMessage = (msgType) => {
    setUnreadMessages((prev) => prev.filter((type) => type !== msgType));
  };
  
  return (
    <NotificationContext.Provider value={{ hasNewMessage, setHasNewMessage, pusherData, setPusherData, unreadMessages, setUnreadMessages, addUnreadMessage, clearUnreadMessage }}>
      {children}
    </NotificationContext.Provider>
  );
};

NotificationProvider.propTypes = {
  children: PropTypes.node.isRequired,
};
