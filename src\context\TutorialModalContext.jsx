// TutorialModalContext.js
import { createContext, useContext, useEffect, useMemo, useState } from "react";
import { Modal } from "react-bootstrap";
import PropTypes from "prop-types";
import { Skeleton, Typography } from "@mui/material";
import { ImageBaseURL } from "../config";
import { useMediaQuery } from "react-responsive";
import PlayCircleIcon from '@mui/icons-material/PlayCircle';
import StopCircleIcon from '@mui/icons-material/StopCircle';
import { useLocation } from "react-router-dom";
import useImageLoaderContext from "./UseImageLoaderContext";
import { setTutorialFlagWithDB, updateTutorialStatus } from "./TutorialStorage";
import { useDispatch } from "react-redux";

const TutorialModalContext = createContext();

export const useGlobalTutorialModal = () => useContext(TutorialModalContext);

export const TutorialModalProvider = ({ children }) => {
    const [imageSrc, setImageSrc] = useState(null);
    const [additionalText, setAdditionalText] = useState(null);
    const [headingText, setHeadingText] = useState(null);
    const [subText, setSubText] = useState(null);
    const [show, setShow] = useState(false);
    const [isDisabled, setIsDisabled] = useState(true);
    const [countdown, setCountdown] = useState(4);
    const [isPlaying, setIsPlaying] = useState(false);
    const [audio, setAudio] = useState(null); // holds the audio object
    const imageLoadStatus = useImageLoaderContext(imageSrc ? [imageSrc] : []);
    const isImageLoaded = imageLoadStatus[imageSrc];
    const [dontShowLoading, setDontShowLoading] = useState(false);

    let token = localStorage.getItem("token");
    const dispatch = useDispatch();

    /**
     * Open the tutorial modal and set the text and image.
     * @param {{
     *   additionalTopText: string,
     *   mainText: string,
     *   subText: string,
     *   bodyImageSrc: string,
     *   audioSrc: string | null
     * }} options The options to use for the modal.
     */
    const openTutorialModal = ({ additionalTopText, mainText, subText, bodyImageSrc, audioSrc }) => {
        setAdditionalText(additionalTopText);
        setHeadingText(mainText);
        setSubText(subText);
        setImageSrc(bodyImageSrc);

        // Reset audio state
        if (audio) {
            audio.pause();
            setIsPlaying(false);
        }

        if (audioSrc) {
            // Create a new audio object and set it as the current audio.
            // This is done so that we can control the audio playback.
            const newAudio = new Audio(audioSrc);
            setAudio(newAudio);
        }

        // Show the modal
        setShow(true);
    };

    /**
     * Toggle the audio playback for the tutorial modal.
     */
    const handleAudioToggle = () => {
        if (!audio) return;

        if (isPlaying) {
            // Pause the audio and reset the current time to 0.
            audio.pause();
            audio.currentTime = 0;
            // Set the "isPlaying" state to false.
            setIsPlaying(false);
        } else {
            // Play the audio.
            audio.play();
            // Set the "isPlaying" state to true.
            setIsPlaying(true);

            // Add an event listener to reset the "isPlaying" state when the audio finishes.
            audio.onended = () => {
                setIsPlaying(false); // reset when audio finishes
            };
        }
    };

    /**
     * Close the tutorial modal.
     */
    const closeTutorialModal = () => {
        if (audio) {
            // Pause the audio and reset the current time to 0.
            audio.pause();
            audio.currentTime = 0;
        }
        // Reset the "isPlaying" state to false.
        setIsPlaying(false);
        // Hide the modal.
        setShow(false);
    };

    const location = useLocation();

    // Close modal on route change
    useEffect(() => {
        if (show) {
            closeTutorialModal();
        }
    }, [location.pathname]);


    // Memoize context value to prevent unnecessary re-renders
    const contextValue = useMemo(
        () => ({
            openTutorialModal,
            closeTutorialModal,
            dontShowLoading,
            setDontShowLoading
        }),
        [dontShowLoading]
    );

    const isTabletOrMobile = useMediaQuery({ query: '(max-width: 992px)' });
    const isXsDevice = useMediaQuery({ query: '(min-width: 320px) and (max-width: 475px)' });
    const isSmDevice = useMediaQuery({ query: '(min-width: 475px) and (max-width: 575px)' });
    const isMobile = useMediaQuery({ maxWidth: 767 });
    let logoClassName;
    if (isTabletOrMobile) {
        if (isMobile) {
            logoClassName = "header-top-logo-mobile";
        } else {
            logoClassName = "header-top-logo-tab";
        }
    } else {
        logoClassName = "header-top-logo";
    }

    const getFontSizeTopText = () => {
        if (isXsDevice) return "12px";
        if (isSmDevice && isTabletOrMobile) return "18px";
        if (isMobile) return "20px";
        return "20px"; // default
    };

    const getFontSizeHeading = () => {
        if (isXsDevice) return "16px";
        if (isSmDevice && isTabletOrMobile) return "25px";
        if (isMobile) return "28px";
        return "30px"; // default
    };

    const getIconSize = () => {
        if (isXsDevice) return "20px";
        if (isSmDevice && isTabletOrMobile) return "24px";
        if (isMobile) return "28px";
        return "32px"; // default
    };

    const getFontSizeBottomText = () => {
        if (isXsDevice) return "11px";
        if (isSmDevice && isTabletOrMobile) return "16px";
        if (isMobile) return "20px";
        return "18px"; // default
    };

    useEffect(() => {
        let countdownTimer;
        let enableTimer;

        if (show) {
            setIsDisabled(true);
            setCountdown(4); // reset to 4 every time show becomes true

            // Countdown interval
            countdownTimer = setInterval(() => {
                setCountdown((prev) => prev - 1);
            }, 1000);

            // Enable after 4s
            enableTimer = setTimeout(() => {
                setIsDisabled(false);
                clearInterval(countdownTimer);
            }, 4000);
        }

        return () => {
            clearTimeout(enableTimer);
            clearInterval(countdownTimer);
        };
    }, [show]);

    const handleGotItClick = () => {
        const currentPath = location.pathname;

        if (currentPath === "/my-garage") {
            updateTutorialStatus("garage_testDrive");
            closeTutorialModal();
        } else if (currentPath === "/specific-search") {
            updateTutorialStatus("search_vehicles");
            closeTutorialModal();
        } else if (currentPath === "/command-center") {
            updateTutorialStatus("commandCenter_shopInventory");
            closeTutorialModal();
        } else if (currentPath === "/swipe-cards") {
            updateTutorialStatus("swipeCards_vehicles");
            closeTutorialModal();
        } else if (currentPath === "/landing") {
            updateTutorialStatus("landing_commandCenter");
            closeTutorialModal();
        } else {
            closeTutorialModal(); // fallback
        }
    };

    const getTutorialKey = (pathname) => {
        if (pathname === "/my-garage") {
            return "garage_testDrive";
        } else if (pathname === "/specific-search") {
            return "search_vehicles";
        } else if (pathname === "/command-center") {
            return "commandCenter_shopInventory";
        } else if (pathname === "/swipe-cards") {
            return "swipeCards_vehicles";
        } else if (pathname === "/landing") {
            return "landing_commandCenter";
        }
        return null; // fallback
    };

    const handleDontShowAgain = async () => {
        setDontShowLoading(true);
        const tutorialKey = getTutorialKey(location.pathname);

        if (!tutorialKey) return;

        try {
            await setTutorialFlagWithDB({ tutorialKey, dispatch, token });
            setDontShowLoading(false);
        } catch (err) {
            console.error("Error setting tutorial flag:", err);
            setDontShowLoading(false);
        } finally {
            closeTutorialModal();
            setDontShowLoading(false);
        }
    };

    return (
        <TutorialModalContext.Provider value={contextValue}>
            {children}
            <Modal show={show} onHide={closeTutorialModal} centered backdrop="static" size="lg" dialogClassName={(isSmDevice || isXsDevice) && "tutorial-modal-dialog"}>
                <Modal.Body
                    className="tutorial-bg-img p-0"
                    style={{
                        backgroundImage: `url(${ImageBaseURL + "tutorials/tutorial-bg-pattern.png"})`,
                        position: 'relative',
                    }}
                >
                    <div className="col-12" style={{ marginTop: isSmDevice || isSmDevice || isMobile && "20%" }}>
                        <Typography variant={isMobile ? "body2" : "subtitle1"} className="text-center" style={{ fontWeight: '600', margin: '15px' }}>
                            EXCLUSIVE MEMBERS' ONLY
                        </Typography>
                    </div>
                    <div className="col-12 d-flex justify-content-center align-items-center mb-2">
                        <img
                            src={ImageBaseURL + "fp_logo.png"}
                            className={logoClassName}
                            alt="FastPass"
                        />
                    </div>
                    <div className="col-12 text-center">
                        <Typography variant={isMobile ? "caption" : "body2"} className="text-center tutorial-font-color" style={{ fontWeight: '500' }}>
                            AUTO BUYER PROGRAM
                        </Typography>
                    </div>
                    {isMobile || isTabletOrMobile ? (
                        <div className="row" style={{ margin: "20px" }}>
                            {!isImageLoaded ? (
                                <div className="col-12 d-flex justify-content-center align-item-center mb-1">
                                    <Skeleton
                                        variant="rectangular"
                                        width="70%"
                                        height={100}
                                        animation="wave"
                                        sx={{ borderRadius: 2 }}
                                    />
                                </div>
                            ) : (
                                <div className="col-12 d-flex justify-content-center align-item-center mb-1">
                                    <img
                                        src={imageSrc}
                                        className="tutorial-body-img"
                                        alt="FastPass"
                                    />
                                </div>
                            )}
                            <div className="col-12 d-grid justify-content-center align-item-center">
                                <div className="row col-12">
                                    <div className="col-8 align-content-end">
                                        <Typography variant={isMobile ? "subtitle1" : "h6"} className="fw-bold" style={{ fontSize: getFontSizeTopText() }}>
                                            {additionalText}
                                        </Typography>
                                    </div>
                                    <div className="text-end align-content-center col-4">
                                        <span onClick={handleAudioToggle} style={{ cursor: "pointer" }}>
                                            {isPlaying ? (
                                                <StopCircleIcon style={{ fontSize: getIconSize(), color: "#ff3037" }} />
                                            ) : (
                                                <PlayCircleIcon style={{ fontSize: getIconSize(), color: "#ff3037" }} />
                                            )}
                                        </span>
                                    </div>
                                    {!isMobile || !isTabletOrMobile && <div className="col-2"></div>}
                                </div>
                                <div className="col-12">
                                    <Typography
                                        variant="h4"
                                        style={{ fontSize: getFontSizeHeading() }}
                                        className="fw-bold tutorial-font-color"
                                    >
                                        {headingText}
                                    </Typography>
                                </div>
                                <div className="col-12">
                                    <Typography variant="body2" style={{ fontWeight: "600", fontSize: getFontSizeBottomText() }}>
                                        {subText}
                                    </Typography>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="col-12">
                            <div className="row" style={{ margin: "20px" }}>
                                {!isImageLoaded ? (
                                    <div className="col-6 d-flex justify-content-center align-item-center">
                                        <Skeleton
                                            variant="rectangular"
                                            width="65%"
                                            height={250}
                                            animation="wave"
                                            sx={{ borderRadius: 2 }}
                                        />
                                    </div>
                                ) : (
                                    <div className="col-6 align-content-center">
                                        <img
                                            src={imageSrc}
                                            className="tutorial-body-img"
                                            alt="FastPass"
                                        />
                                    </div>
                                )}
                                <div className="col-6 align-content-center">
                                    <div className="row col-12">
                                        <div className="col-8 align-content-center">
                                            <Typography variant={isMobile ? "subtitle1" : "h6"} className="fw-bold">
                                                {additionalText}
                                            </Typography>
                                        </div>
                                        <div className="text-end align-content-center col-2">
                                            <span onClick={handleAudioToggle} style={{ cursor: "pointer" }}>
                                                {isPlaying ? (
                                                    <StopCircleIcon style={{ fontSize: "32px", color: "#ff3037" }} />
                                                ) : (
                                                    <PlayCircleIcon style={{ fontSize: "32px", color: "#ff3037" }} />
                                                )}
                                            </span>
                                        </div>
                                        {!isMobile || !isTabletOrMobile && <div className="col-2"></div>}
                                    </div>

                                    <Typography variant="h4" style={{ fontSize: "31px" }} className="fw-bold tutorial-font-color">
                                        {headingText}
                                    </Typography>
                                    <Typography variant="body2" style={{ fontWeight: '600' }}>
                                        {subText}
                                    </Typography>
                                </div>
                            </div>
                        </div>
                    )}

                    <div className="col-12 row text-center m-0">
                        <div className="col-6 tutorial-modal-btn" onClick={handleDontShowAgain}>
                            <span>{dontShowLoading ? "Processing..." : "Don't Show Again"}</span>
                        </div>
                        <div
                            className="col-6 tutorial-modal-btn"
                            style={{
                                opacity: isDisabled ? 0.5 : 1,
                                pointerEvents: isDisabled ? "none" : "auto",
                            }}
                            onClick={handleGotItClick}
                        >
                            <span>Got It</span>
                            {isDisabled && <span>(0{countdown})</span>}
                        </div>
                    </div>
                </Modal.Body>
                <div
                    className="tutorial-close-icon"
                    onClick={closeTutorialModal}
                >
                    &#10005;
                </div>
            </Modal >
        </TutorialModalContext.Provider >
    )
}

TutorialModalProvider.propTypes = {
    children: PropTypes.node.isRequired
};