// utils/TutorialStorage.js
import { getloggedInUserDetails, updateUserLoanDetails } from "../store/apps/user";

const TUTORIAL_KEY = "tutorial_completion_status";

/**
 * Updates the tutorial status for the given key in localStorage.
 *
 * @param {string} key - The key representing the tutorial status to update.
 */
export const updateTutorialStatus = (key) => {
    try {
        // Retrieve existing tutorial status from localStorage
        const existing = JSON.parse(localStorage.getItem(TUTORIAL_KEY)) || {};

        // Update the status for the specified key
        existing[key] = true;

        // Save the updated status back to localStorage
        localStorage.setItem(TUTORIAL_KEY, JSON.stringify(existing));
    } catch (error) {
        // Log any errors that occur during the update process
        console.error("Error updating tutorial status:", error);
    }
};

/**
 * Checks if a tutorial should be shown based on the given key and the user's
 * existing tutorial flags.
 *
 * This function will return false if the user has already completed the tutorial
 * for the given key, or if the user object does not have the tutorial_completion_status
 * property. Otherwise, it will return true.
 *
 * @param {string} tutorialKey - The key representing the tutorial status to check.
 * @param {function} dispatch - The dispatch function from the Redux store.
 * @returns {Promise<boolean>} - A promise that resolves with a boolean indicating
 *   whether the tutorial should be shown.
 */
export const shouldShowTutorial = async ({ tutorialKey, dispatch }) => {
    const token = localStorage.getItem("token"); // or whatever key you're using
    try {
        const userResponse = await dispatch(getloggedInUserDetails(token));

        // Wait until userResponse is valid
        if (!userResponse || !userResponse.meta || userResponse.meta.requestStatus !== "fulfilled") {
            console.warn("User response not fulfilled yet, delaying tutorial check");
            return false; // Don't show tutorial until confirmed
        }

        const user = userResponse.payload?.data?.[0];
        if (user?.tutorial_completion_status) {
            const flags = JSON.parse(user.tutorial_completion_status || "{}");
            return !flags[tutorialKey]; // Show only if key is NOT true
        }

        return true; // No flags found; show tutorial
    } catch (err) {
        console.warn("Error checking tutorial flag:", err);
        return true; // Fallback to showing tutorial if error occurs
    }
};

/**
 * Updates the tutorial status for the given key in the database.
 *
 * @param {object} options - Options object containing:
 *   - {string} tutorialKey - The key representing the tutorial status to update.
 *   - {function} dispatch - The dispatch function from the Redux store.
 *   - {string} token - The token to pass to the API call.
 *   - {boolean} [value=true] - The value to set the tutorial flag to. Defaults to true.
 * @returns {Promise<boolean>} - A promise that resolves with a boolean indicating
 *   whether the update was successful.
 */
export const setTutorialFlagWithDB = async ({ tutorialKey, dispatch, token, value = true }) => {
    try {
        // Immediately update localStorage
        const existingFlags = getTutorialStatus();
        const updatedFlags = { ...existingFlags, [tutorialKey]: value };
        localStorage.setItem(TUTORIAL_KEY, JSON.stringify(updatedFlags));

        // Fetch existing DB flags
        const userDataResponse = await dispatch(getloggedInUserDetails(token));
        let existingDbFlags = {};

        if (userDataResponse.meta?.requestStatus === "fulfilled") {
            const userData = userDataResponse.payload?.data?.[0];
            try {
                // Parse the existing flags from the DB response
                existingDbFlags = JSON.parse(userData?.tutorial_completion_status || "{}");
            } catch {
                // If parsing fails, start from scratch
                existingDbFlags = {};
            }
        }

        // Merge and sync with DB
        const finalFlags = { ...existingDbFlags, [tutorialKey]: value };
        const userLoanDetails = {
            // Stringify the final flags and pass them to the API call
            tutorial_completion_status: JSON.stringify(finalFlags),
        };

        const dbUpdateResponse = await dispatch(updateUserLoanDetails({ token, userLoanDetails }));

        if (dbUpdateResponse.meta?.requestStatus === "fulfilled") {
            return true;
        } else {
            // Log an error if the DB update fails
            console.warn("DB update failed:", dbUpdateResponse.error);
            return false;
        }
    } catch (error) {
        // Log any errors that occur during the update process
        console.error("Error setting tutorial flag:", error);
        return false;
    }
};

export const resetTutorialProgress = async (dispatch, setLoader) => {
    const token = localStorage.getItem('token');
    try {
        setLoader(true); // show loader

        // 1. Clear localStorage
        localStorage.removeItem("tutorial_completion_status");

        // 2. Update DB
        const userLoanDetails = {
            tutorial_completion_status: null
        };

        const response = await dispatch(updateUserLoanDetails({ token, userLoanDetails }));

        if (response.meta?.requestStatus === "fulfilled") {
            console.log("Tutorial flags reset successfully.");
        } else {
            console.error("Failed to reset tutorial flags in DB:", response.error);
        }
    } catch (err) {
        console.error("Error resetting tutorial:", err);
    } finally {
        setLoader(false); // hide loader
    }
};

/**
 * Retrieves the tutorial status flags from local storage.
 * @returns {Object} An object with flag names as keys and boolean values
 * indicating whether the tutorial has been seen or not.
 */
export const getTutorialStatus = () => {
    try {
        // Retrieve the stored tutorial status flags from local storage
        const storedFlags = localStorage.getItem(TUTORIAL_KEY);

        // If the stored flags exist, parse them into an object
        if (storedFlags) {
            return JSON.parse(storedFlags);
        }
    } catch {
        // Return an empty object if parsing fails
        console.warn("Error parsing tutorial status flags:", error);
    }

    // Return an empty object if no flags are stored
    return {};
};

/**
 * Checks if the user has seen the tutorial for the given key.
 * @param {string} key - The key representing the tutorial status to check.
 * @returns {boolean} - True if the user has seen the tutorial, false otherwise.
 */
export const hasSeenTutorial = (key) => {
    // Retrieve the stored tutorial status flags from local storage
    const status = getTutorialStatus();
    // Return true if the key exists in the status object with value true
    return status[key] === true;
};
