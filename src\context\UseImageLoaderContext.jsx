import { useState, useEffect } from "react";

const useImageLoaderContext = (imageUrls) => {
    const [imageLoadStatus, setImageLoadStatus] = useState({});

    useEffect(() => {
        if (!imageUrls || imageUrls.length === 0) return;

        let isMounted = true; // To avoid setting state on an unmounted component
        /**
        * Load images in parallel using Promise.all() and update the
        * imageLoadStatus state with the result.
        */
        const loadImages = async () => {
            const status = {};

            await Promise.all(
                imageUrls.map(
                    (url) =>
                        /**
                         * Create a new Image object and set its src attribute
                         * to the current URL. If the image loads successfully,
                         * set the status of that URL to true and resolve the
                         * promise. If the image fails to load, set the status
                         * of that URL to false and reject the promise.
                         */
                        new Promise((resolve) => {
                            const img = new Image();
                            img.src = url;
                            img.onload = () => {
                                status[url] = true;
                                resolve();
                            };
                            img.onerror = () => {
                                status[url] = false;
                                resolve();
                            };
                        })
                )
            );

            if (isMounted) {
                setImageLoadStatus((prev) => ({ ...prev, ...status }));
            }
        };

        loadImages();

        return () => {
            isMounted = false; // Cleanup function to prevent memory leaks
        };
    }, [JSON.stringify(imageUrls)]); // ✅ Ensure dependency doesn't trigger unnecessary re-renders

    return imageLoadStatus;
};

export default useImageLoaderContext;
