/* eslint-disable no-undef */
import { DefaultAzureCredential } from "@azure/identity";
import { SecretClient } from "@azure/keyvault-secrets";
import 'dotenv/config'; // Importing dotenv in an ES module

// Load environment variables
const {
  VITE_AZURE_KEY_VAULT_NAME,
} = process.env;

if (!VITE_AZURE_KEY_VAULT_NAME) {
  console.error("Please make sure all environment variables are set.");
  process.exit(1);
}

const credential = new DefaultAzureCredential();
const vaultUrl = `https://${VITE_AZURE_KEY_VAULT_NAME}.vault.azure.net`;

const client = new SecretClient(vaultUrl, credential);

async function getAllSecrets() {
  try {
    const secrets = {};
    const secretPropertiesIterator = client.listPropertiesOfSecrets().byPage();

    for await (const page of secretPropertiesIterator) {
      const promises = page.map(async (secretProperty) => {
        const secret = await client.getSecret(secretProperty.name);
        secrets[secret.name] = secret.value;
      });
      await Promise.all(promises);
    }

    return secrets;
  } catch (error) {
    console.error("Error fetching secrets:", error);
    throw error;
  }
}

async function loadSecretsIntoEnv() {
  const secrets = await getAllSecrets();
  const processedSecrets = {};

  // Prefix keys with VITE_ and replace hyphens with underscores
  Object.entries(secrets).forEach(([key, value]) => {
    const processedKey = `VITE_${key.replace(/-/g, "_")}`;
    processedSecrets[processedKey] = value;
    process.env[processedKey] = value; // Store in process.env
  });
  
  return processedSecrets; // Return the processed secrets
}

// Export the function for use in CommonJS
export { loadSecretsIntoEnv };

if (import.meta.url === `file://${process.argv[1]}`) {
  loadSecretsIntoEnv().then(() => {
    console.log('Secrets loaded into process.env');
  }).catch(error => {
    console.error('Failed to load secrets:', error);
    process.exit(1);
  });
}
