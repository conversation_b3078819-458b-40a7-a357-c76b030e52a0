import React from "react";
import ReactDOM from "react-dom/client";
import { Provider } from "react-redux";
import { ThemeProvider } from "@mui/material/styles";
import App from "./App.jsx";
import "./index.css";
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap/dist/js/bootstrap.min.js';
import 'bootstrap-icons/font/bootstrap-icons.css';
import theme from "./theme";
import store from "./store"; // Ensure the path to your store is correct
import UpdateButton from "./components/updateButton.jsx";
import { registerSW } from 'virtual:pwa-register';

ReactDOM.createRoot(document.getElementById("root")).render(
  <React.StrictMode>
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <App />
        <UpdateButton />
      </ThemeProvider>
    </Provider>
  </React.StrictMode>
);

// Register the service worker
registerSW({
  immediate: true,
  onRegistered(r) {
    r && console.log('Service Worker registered with scope:', r.scope);
  },
  onRegisterError(error) {
    console.log('Service Worker registration error:', error);
  }
});
