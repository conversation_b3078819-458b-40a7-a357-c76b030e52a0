/* eslint-disable react-refresh/only-export-components */
import { createContext, useContext, useState } from 'react';
import { Toast, ToastContainer } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import PropTypes from "prop-types";

// Create ToastContext
const ToastContext = createContext();

// Provider component
export const ToastProvider = ({ children }) => {
  const [toast, setToast] = useState({ show: false, message: '', type: '' });

  const showToast = (message, type) => {
    setToast({ show: true, message, type });
    setTimeout(() => setToast({ show: false, message: '', type }), 15000); // Auto-hide after 5 seconds
  };

  return (
    <ToastContext.Provider value={{ showToast }}>
      {children}
      <ToastContainer position="top-end" className="p-3">
        {toast.show && (
          <Toast
            onClose={() => setToast({ show: false, message: '', type: '' })}
            bg={toast.type}
            className="text-white"
          >
            <Toast.Body>{toast.message}</Toast.Body>
          </Toast>
        )}
      </ToastContainer>
    </ToastContext.Provider>
  );
};

// Custom hook to use toast
export const useToast = () => useContext(ToastContext);

// Fix the missing 'children' prop validation
ToastProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

