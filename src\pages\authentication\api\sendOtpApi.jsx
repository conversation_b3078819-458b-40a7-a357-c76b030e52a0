import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { sendOTP } from "../../../store/apps/user";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "bootstrap/dist/css/bootstrap.min.css";
import "bootstrap-icons/font/bootstrap-icons.css";

function useSendOtpApi() {
  const [phoneNumber, setPhoneNumber] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  
  const navigate = useNavigate();
  const dispatch = useDispatch();

  useEffect(() => {
    const token = localStorage.getItem("token");
    if (token) {
      navigate("/landing");
    }
    localStorage.setItem("isSendOtp", false);
  }, [navigate]);

  const clearPhoneNumber = () => {
    setPhoneNumber("");
  };

  const handlePhoneNumberChange = (e) => {
    const input = e.target.value;
    const cleanedInput = input.replace(/\D/g, "");
    const truncatedInput = cleanedInput.slice(0, 12);
    setPhoneNumber(truncatedInput);
  };

  const handleGetOTPClick = () => {
    setIsLoading(true);

    const phoneNumberDigits = phoneNumber.slice(-10);
    const countryCodeDigits =
      phoneNumber.length > 10 ? phoneNumber.slice(0, -10) : "";

    if (!phoneNumber) {
      toast.warning("Please enter your mobile number", {
        className: "bg-warning text-white fw-800",
        bodyClassName: "text-white",
        icon: <i className="bi bi-exclamation-circle"></i>,
      });
      setIsLoading(false);
    } else {
      setError("");

      dispatch(
        sendOTP({
          countryCode: countryCodeDigits,
          phoneNumber: phoneNumberDigits,
        })
      )
        .then((response) => {
          if (response.meta.requestStatus === "fulfilled") {
            toast.success("OTP sent successfully", {
              className: "bg-success text-white fw-800",
              bodyClassName: "text-white",
              icon: <i className="bi bi-check-circle"></i>,
            });
            const params = {
              phoneNumber: phoneNumber,
            };
            navigate("/verify-otp", { state: { params } });
            localStorage.setItem("isSendOtp", true);
          } else if(response.payload.is_expiry === 1 && response.payload.statusCode === 400) {
            toast.warning(response.payload.message, {
              className: "bg-warning text-white fw-800",
              bodyClassName: "text-white",
              icon: <i className="bi bi-exclamation-circle"></i>,
            });
            localStorage.clear();
          }else {
            toast.error(response.payload.message || "Invalid Number. Please enter correct number", {
              className: "bg-danger text-white fw-800",
              bodyClassName: "text-white",
              icon: <i className="bi bi-exclamation-circle"></i>,
            });
            localStorage.clear();
          }
          setIsLoading(false);
        })
        .catch((error) => {
          toast.error("Invalid Number. Please enter correct number" + error, {
            className: "bg-danger text-white fw-800",
            bodyClassName: "text-white",
            icon: <i className="bi bi-exclamation-circle"></i>,
          });
          setIsLoading(false);
        });
    }
  };

  return {
    phoneNumber,
    error,
    isLoading,
    handlePhoneNumberChange,
    handleGetOTPClick,
    clearPhoneNumber,
  };
}

export default useSendOtpApi;
