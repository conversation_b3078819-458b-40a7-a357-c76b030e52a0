import { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import {
  sendOTP,
  verifyOTP,
  getUser,
  setToken,
} from "../../../store/apps/user";
import { useLocation } from "react-router-dom";
import { pageMetaData } from "../../../../data/pageMetaData";
import { pageComponentData } from "../../../../data/componentMetaData";
import { callComponentActionApi } from "../../../util/callComponenetActionApi";

function useOtpLogic() {
  const [OTP, setOTP] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showResend, setShowResend] = useState(false);
  const [timer, setTimer] = useState(
    parseInt(import.meta.env.VITE_OTP_TIMER_VALUE)
  );
  const [isResendLoading, setResendLoading] = useState(false);

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const getphoneNumber = location.state?.params;
  const phoneNumber = getphoneNumber?.phoneNumber;
  const [pageId, setPageId] = useState(null); // State to store the page_id
  const [componentMetaDataVal, setComponentMetaData] = useState(null); // State to store the componentMetaData

  useEffect(() => {
    const token = localStorage.getItem("token");
    if (token) {
      navigate("/landing");
    }

    const getSendOtp = localStorage.getItem("isSendOtp");
    if (getSendOtp == "false") {
      navigate("/send-otp");
    }

    const interval = setInterval(() => {
      setTimer((prevTimer) => {
        if (prevTimer === 0) {
          setShowResend(true);
          clearInterval(interval);
        }
        return prevTimer > 0 ? prevTimer - 1 : 0;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [navigate]);

  // Store verify otp button click //
  useEffect(() => {
    // Get the page_id from pageMetaData based on slug
    const slug = "login"; // Replace this with the desired slug
    const pageData = pageMetaData.find((page) => page.slug === slug);
    if (pageData) {
      setPageId(pageData.id);
    }

    // Get Page Componenet Meta Data
    const matchingComponents = pageComponentData.filter(
      (pageComponent) => pageComponent.page_id === pageId
    );

    if (matchingComponents.length > 0) {
      setComponentMetaData(matchingComponents);
    }
  }, [pageId]);
  // End //
  const handleResendOTPClick = () => {
    setResendLoading(true);
    //const phoneNumber = localStorage.getItem("ph_number");
    const phoneNumberDigits = phoneNumber.slice(-10);
    const countryCodeDigits =
      phoneNumber.length > 10 ? phoneNumber.slice(0, -10) : "";
    setOTP("");
    dispatch(
      sendOTP({
        countryCode: countryCodeDigits,
        phoneNumber: phoneNumberDigits,
      })
    )
      .then((response) => {
        if (response.meta.requestStatus === "fulfilled") {
          toast.success("OTP resent successfully", {
            className: "bg-success text-white fw-600 fw-bolder",
            bodyClassName: "text-white",
            icon: <i className="bi bi-check-circle"></i>,
          });
          setTimer(60); // Reset timer to 60 seconds
          setShowResend(false); // Hide resend button
          setResendLoading(false);
          const interval = setInterval(() => {
            setTimer((prevTimer) => {
              if (prevTimer === 0) {
                clearInterval(interval);
                setShowResend(true); // Show resend button when timer reaches 0
              }
              return prevTimer > 0 ? prevTimer - 1 : 0;
            });
          }, 1000);
        }
        setIsLoading(false);
        setResendLoading(false);
      })
      .catch((error) => {
        console.error(error);
        toast.error("Failed to resend OTP. Please try again.", {
          className: "bg-danger text-white fw-600 fw-bolder",
          bodyClassName: "text-white",
          icon: <i className="bi bi-exclamation-circle"></i>,
        });
        setIsLoading(false);
        setResendLoading(false);
      });
  };

  const handleVerifyOTPClick = async () => {
    // Keep your original function name
    setIsLoading(true);
    const phoneNumberDigits = phoneNumber.slice(-10);
    const countryCodeDigits =
      phoneNumber.length > 10 ? phoneNumber.slice(0, -10) : "";

    if (!OTP) {
      toast.warning("Please enter the OTP", {
        className: "bg-warning text-white fw-600 fw-bolder",
        bodyClassName: "text-white",
        icon: <i className="bi bi-exclamation-circle"></i>,
      });
      setIsLoading(false);
      return;
    }

    setError("");

    try {
      const response = await dispatch(
        verifyOTP({
          countryCode: countryCodeDigits,
          phoneNumber: phoneNumberDigits,
          otp: OTP,
        })
      );

      if (response.meta.requestStatus === "fulfilled") {
        const validToken = response.payload.token;

        const userResponse = await dispatch(
          getUser({ phone_number: phoneNumberDigits, authToken: validToken })
        );

        if (userResponse.meta.requestStatus === "fulfilled") {
          if (userResponse.payload.error !== "Number not found") {
            dispatch(setToken(validToken));
            localStorage.setItem("token", validToken);
            localStorage.setItem("verified", true);
            localStorage.setItem("ph_number", phoneNumberDigits);
            navigate("/landing");

            try {
              let slug = "verify-otp";
              // Find the component by slug
              const component = componentMetaDataVal.find(
                (comp) => comp.slug === slug
              );
              if (!component) {
                console.error(`Component with slug "${slug}" not found.`);
                return;
              }

              // Call the API with the component ID
              await callComponentActionApi(dispatch, component.id);
            } catch (error) {
              console.error("Error handling button click:", error);
            }
          } else {
            toast.warning(
              "Your FastPass is currently being created and will be ready shortly. You will be sent a text when it's activated. Please call support at (************* if you have any questions.",
              {
                className: "bg-warning text-white fw-600 fw-bolder",
                bodyClassName: "text-white",
                icon: <i className="bi bi-exclamation-circle"></i>,
                autoClose: 15000,
              }
            );
            setOTP("");
            setTimer(0);
          }
        } else {
          toast.error("Error fetching user details.", {
            className: "bg-danger text-white fw-600 fw-bolder",
            bodyClassName: "text-white",
            icon: <i className="bi bi-exclamation-circle"></i>,
          });
          setOTP("");
          setTimer(0);
        }
        setIsLoading(false);
      } else {
        if (response.payload.statusCode === 400) {
          if (response.payload.message === "Number not found") {
            toast.warning(
              "Your FastPass is currently being created and will be ready shortly. You will be sent a text when it's activated. Please call support at (************* if you have any questions.",
              {
                className: "bg-warning text-white fw-600 fw-bolder",
                bodyClassName: "text-white",
                icon: <i className="bi bi-exclamation-circle"></i>,
                autoClose: 15000,
              }
            );
          } else {
            toast.error("OTP verification failed", {
              className: "bg-danger text-white fw-600 fw-bolder",
              bodyClassName: "text-white",
              icon: <i className="bi bi-exclamation-circle"></i>,
            });
          }
        } else {
          toast.warning(
            "Your FastPass is currently being created and will be ready shortly. You will be sent a text when it's activated. Please call support at (************* if you have any questions.",
            {
              className: "bg-warning text-white fw-600 fw-bolder",
              bodyClassName: "text-white",
              icon: <i className="bi bi-exclamation-circle"></i>,
              autoClose: 15000,
            }
          );
        }
        setOTP("");
        setTimer(0);
        setIsLoading(false);
      }
    } catch (error) {
      console.error(error);
      toast.error("An error occurred. Please try again.");
      setOTP("");
      setTimer(0);
      setIsLoading(false);
    }
  };

  return {
    OTP,
    setOTP,
    error,
    isLoading,
    showResend,
    timer,
    handleResendOTPClick,
    handleVerifyOTPClick,
    isResendLoading,
  };
}

export default useOtpLogic;
