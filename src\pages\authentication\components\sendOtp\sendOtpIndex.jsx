import { useMediaQuery } from 'react-responsive'
import SendOtpMobile from './sendOtpMobile';
import SendOtpWeb from './sendOtpWeb';
import MasterLayoutWithoutFrames from '../../../../components/layouts/masterLayoutWithoutFrames';

function SendOtpIndex() {
  const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' })
  return (
    <MasterLayoutWithoutFrames>
      {isTabletOrMobile ? <SendOtpMobile /> : <SendOtpWeb />}
    </MasterLayoutWithoutFrames>
  );
}

export default SendOtpIndex;