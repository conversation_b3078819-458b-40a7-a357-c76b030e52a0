import { styled } from "@mui/material/styles";
import { useMediaQuery } from 'react-responsive';
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import InputAdornment from "@mui/material/InputAdornment";
import Divider from "@mui/material/Divider";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import CircularProgress from "@mui/material/CircularProgress";
import Icon from "../../../../icon";
import useSendOtpApi from "../../api/sendOtpApi";
import CloseIcon from "@mui/icons-material/Close";
import { ImageBaseURL } from '../../../../config';

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const CustomTextField = styled(TextField)({
  "& .MuiInputLabel-root": {
    fontWeight: "semibold",
  },
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
  },
});
const StyledButton = styled(({ ...other }) => <Button {...other} />)(({ isMobile }) => ({
  borderRadius: "10px",
  width: isMobile ? "50%" : "25%",
  fontSize: "15px",
  fontWeight: "600",
  padding: "15px",
  backgroundColor: "#4891FF",
  // Hover state
  "&:hover": {
    backgroundColor: "#357AE8",
  },

  // Disabled state
  "&.Mui-disabled": {
    backgroundColor: "#4891FF",
    color: "#fff",
    opacity: 0.6,
    cursor: 'not-allowed',
  },
}));

const SendOtpMobile = () => {
  const isMobile = useMediaQuery({ maxWidth: 767 });
  const { phoneNumber, isLoading, handlePhoneNumberChange, handleGetOTPClick, clearPhoneNumber } =
    useSendOtpApi();
  return (
    <Box sx={{ flexGrow: 1 }}>
      <Grid container spacing={2} sx={{ padding: "15px 10px" }}>
        <Grid item xs={12}>
          <Item className='page_bg'>
            <img
              src={ImageBaseURL + "fp_logo.png"}
              alt="Fastpass"
              style={isMobile ? { maxWidth: "75%", maxHeight: "75%" } : { maxWidth: "55%", maxHeight: "55%" }}
            />
          </Item>
        </Grid>
        <Grid item xs={12} style={{ paddingTop: "0px" }}>
          <Item className='page_bg'>
            <img
              src={ImageBaseURL + "login.png"}
              alt="loginImage"
              style={isMobile ? { maxWidth: "75%", maxHeight: "75%" } : { maxWidth: "55%", maxHeight: "55%" }}
            />
          </Item>
        </Grid>
        <Grid item xs={12}>
          <Item>
            <CustomTextField
              id="outlined-basic"
              label="Enter Your Mobile Number"
              variant="outlined"
              sx={{ width: isMobile ? "95%" : "55%" }}
              autoComplete="off"
              value={phoneNumber}
              onChange={handlePhoneNumberChange}
              type="tel"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                      <Icon icon="hand-holding-a-phone-otp" size={30} />
                      <Divider
                        sx={{ height: 30, m: 1, borderColor: "#4891FF" }}
                        orientation="vertical"
                      />
                    </Box>
                  </InputAdornment>
                ),
                endAdornment: phoneNumber && (
                  <InputAdornment position="end">
                    <CloseIcon
                      style={{ cursor: "pointer" }}
                      onClick={clearPhoneNumber}
                    />
                  </InputAdornment>
                ),
              }}
            />
          </Item>
        </Grid>
        <Grid item xs={12} style={{ paddingTop: "0px" }}>
          <Item className='page_bg'>
            <StyledButton
              variant="contained"
              isMobile={isMobile}
              onClick={handleGetOTPClick}
              disabled={isLoading}
              startIcon={
                isLoading ? (
                  <CircularProgress size={20} color="inherit" />
                ) : null
              }
            >
              {isLoading ? "Sending OTP..." : "GET OTP"}
            </StyledButton>
          </Item>
        </Grid>
        <Grid item xs={12} style={{ paddingTop: "0px" }}>
          <Item className='page_bg'>
            <Typography
              variant="subtitle2"
              gutterBottom
              mx={1}
              sx={{ fontWeight: "800", fontSize: "14px", ...(!isMobile && { mx: 20 }) }}
            >
              By entering in your phone number, you agree to allow CU FastPass
              to text, call or email on matters related to your credit union
              auto financing or other related questions.
            </Typography>
          </Item>
        </Grid>
      </Grid>
    </Box>
  );
};
export default SendOtpMobile;
