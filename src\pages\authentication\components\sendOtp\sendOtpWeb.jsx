import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import InputAdornment from "@mui/material/InputAdornment";
import Divider from "@mui/material/Divider";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import useSendOtpApi from "../../api/sendOtpApi";
import CircularProgress from "@mui/material/CircularProgress";
import Icon from "../../../../icon";
import CloseIcon from "@mui/icons-material/Close";
import Tooltip from '@mui/material/Tooltip';
import Zoom from '@mui/material/Zoom';
import { ImageBaseURL } from '../../../../config';
import { useMediaQuery } from "react-responsive";

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const CustomTextField = styled(TextField)({
  "& .MuiInputLabel-root": {
    fontWeight: "semibold",
  },
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
  },
});
const StyledButton = styled(({ ...other }) => <Button {...other} />)(({ isBigScreen }) => ({
  borderRadius: "10px",
  width: isBigScreen ? "23%" : "30%",
  fontWeight: "600",
  fontSize: "16px",
  padding: "15px",
  backgroundColor: "#4891FF",
  color: "#fff",

  // Hover state
  "&:hover": {
    backgroundColor: "#357AE8",
  },

  // Disabled state
  "&.Mui-disabled": {
    backgroundColor: "#4891FF",
    color: "#fff",
    opacity: 0.6,
    cursor: 'not-allowed',
  },
}));

const SendOtpWeb = () => {
  const { phoneNumber, isLoading, handlePhoneNumberChange, handleGetOTPClick, clearPhoneNumber } =
    useSendOtpApi();
    const isBigScreen = useMediaQuery({ query: '(min-width: 1824px)' });

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Grid container spacing={1} sx={{ padding: "20px 0px" }}>
        <Grid item sm={12}>
          <Item className='page_bg'>
            <img
              src={ImageBaseURL + "fp_logo.png"}
              alt="Fastpass"
              style={{ maxWidth: "30%", maxHeight: "30%" }}
            />
          </Item>
        </Grid>
        <Grid item xs={6}>
          <Item className='page_bg'>
            <img
              src={ImageBaseURL + "login.png"}
              alt="loginImage"
              style={{ maxWidth: "70%", maxHeight: "70%" }}
            />
          </Item>
        </Grid>
        <Grid item xs={6} sx={{ alignSelf: "center" }}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Item>
                <CustomTextField
                  id="outlined-basic"
                  label="Enter Your Mobile Number"
                  variant="outlined"
                  sx={{ width: isBigScreen ? '50%' : '75%' }}
                  autoComplete="off"
                  value={phoneNumber}
                  onChange={handlePhoneNumberChange}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <Icon icon="hand-holding-a-phone-otp" size={30} />
                          <Divider
                            sx={{ height: 30, m: 1, borderColor: "#4891FF" }}
                            orientation="vertical"
                          />
                        </Box>
                      </InputAdornment>
                    ),
                    endAdornment: phoneNumber && (
                      <Tooltip TransitionComponent={Zoom} title="Clear Mobile Number" arrow>
                        <InputAdornment position="end">
                          <CloseIcon
                            style={{ cursor: "pointer" }}
                            onClick={clearPhoneNumber}
                          />
                        </InputAdornment>
                      </Tooltip>
                    ),
                    sx: {
                      fontSize: !isBigScreen ? '16px' : '20px', // Adjust the font size for mobile and larger screens
                    },
                  }}
                  InputLabelProps={{
                    sx: {
                      fontSize: !isBigScreen ? '16px' : '20px',
                    },
                  }}
                />
              </Item>
            </Grid>
            <Grid item xs={12}>
              <Item className='page_bg'>
                <Tooltip TransitionComponent={Zoom} title="Get OTP" arrow>
                  <StyledButton
                    variant="contained"
                    onClick={handleGetOTPClick}
                    disabled={isLoading}
                    isBigScreen={isBigScreen}
                    startIcon={
                      isLoading ? (
                        <CircularProgress size={20} color="inherit" />
                      ) : null
                    }
                  >
                    {isLoading ? "Sending OTP..." : "GET OTP"}
                  </StyledButton>
                </Tooltip>
              </Item>
            </Grid>
            <Grid item xs={12}>
              <Item sx={{ textAlign: "center" }} className='page_bg'>
                <Typography
                  variant="subtitle2"
                  mx={12}
                  sx={{ fontWeight: "800", fontSize: "16px" }}
                >
                  By entering in your phone number, you agree to allow CU FastPass
                  to text, call or email on matters related to your credit union
                  auto financing or other related questions.
                </Typography>
              </Item>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};
export default SendOtpWeb;
