import { useMediaQuery } from 'react-responsive'
import VerifyOtpMobile from '../verifyOtp/verifyOtpMobile';
import VerifyOtpWeb from '../verifyOtp/verifyOtpWeb';
import MasterLayoutWithoutFrames from '../../../../components/layouts/masterLayoutWithoutFrames';

function VerifyOtpIndex() {
  const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' })
  return (
    <MasterLayoutWithoutFrames>
      {isTabletOrMobile ? <VerifyOtpMobile /> : <VerifyOtpWeb />}
    </MasterLayoutWithoutFrames>
  );
}

export default VerifyOtpIndex;