import { styled } from "@mui/material/styles";
import { useMediaQuery } from "react-responsive";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import OTPInput from "otp-input-react";
import useOtpLogic from "../../api/verifyOtpApi";
import CircularProgress from "@mui/material/CircularProgress";
import { ImageBaseURL } from "../../../../config";
import { useEffect, useState } from "react";

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const StyledButton = styled(({ ...other }) => <Button {...other} />)(
  ({ isMobile }) => ({
    borderRadius: "10px",
    width: isMobile ? "40%" : "25%",
    fontWeight: "600",
    fontSize: "14px",
    padding: "14px",
    backgroundColor: "#4891FF",
    "&:hover": {
      backgroundColor: "#4891FF",
    },

    // Disabled state
    "&.Mui-disabled": {
      backgroundColor: "#4891FF",
      color: "#fff",
      opacity: 0.6,
      cursor: "not-allowed",
    },
  })
);

const VerifyOtpMobile = () => {
  const {
    OTP,
    setOTP,
    isLoading,
    timer,
    showResend,
    handleResendOTPClick,
    handleVerifyOTPClick,
    isResendLoading,
  } = useOtpLogic();

  const inputStyle = {
    width: 50,
    height: 50,
    border: "2px solid #4891FF",
    fontSize: "18px",
  };

  const isMobile = useMediaQuery({ maxWidth: 767 });
  const [isButtonDisabled, setIsButtonDisabled] = useState(false);

  useEffect(() => {
    // Disabled the button when the OTP length is less than 4
    if (OTP.length < 4) {
      setIsButtonDisabled(true);
    } else {
      setIsButtonDisabled(false);
    }
  }, [OTP.length]);

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Grid container spacing={2} sx={{ padding: "15px 10px" }}>
        <Grid item xs={12}>
          <Item className="page_bg" style={{ paddingBottom: "0px" }}>
            <img
              src={ImageBaseURL + "fp_logo.png"}
              alt="Fastpass"
              style={
                isMobile
                  ? { maxWidth: "75%", maxHeight: "75%" }
                  : { maxWidth: "55%", maxHeight: "55%" }
              }
            />
          </Item>
        </Grid>
        <Grid item xs={12} style={{ paddingTop: "0px" }}>
          <Item className="page_bg">
            <img
              src={ImageBaseURL + "verify_otp.png"}
              alt="verify otp"
              style={
                isMobile
                  ? { maxWidth: "75%", maxHeight: "75%" }
                  : { maxWidth: "55%", maxHeight: "55%" }
              }
            />
          </Item>
        </Grid>
        <Grid item xs={12} style={{ paddingTop: "0px" }}>
          <Typography
            variant="subtitle1"
            gutterBottom
            className="text-center font_color fw-bold"
          >
            Enter Your OTP
          </Typography>
          <Box display="flex" justifyContent="center">
            <OTPInput
              value={OTP}
              onChange={setOTP}
              autoFocus
              OTPLength={4}
              otpType="number"
              disabled={isLoading}
              inputStyles={inputStyle}
              inputClassName="border-2 input-group overflow-hidden w-60 form-control bg-transparent m-2 text-center"
              inputProps={{ inputMode: "numeric" }}
            />
          </Box>
        </Grid>
        <Grid item xs={12} style={{ paddingTop: "0px" }}>
          <Item className="page_bg">
            <StyledButton
              variant="contained"
              isMobile={isMobile}
              onClick={handleVerifyOTPClick}
              disabled={isLoading || isButtonDisabled}
              startIcon={
                isLoading ? (
                  <CircularProgress size={20} color="inherit" />
                ) : null
              }
            >
              {isLoading ? "Verifying..." : "VERIFY OTP"}
            </StyledButton>
          </Item>
        </Grid>
        <Grid item xs={12} style={{ paddingTop: "0px" }}>
          <Item className="page_bg">
            {showResend ? (
              <Typography
                variant="subtitle2"
                gutterBottom
                sx={{ fontWeight: "800" }}
              >
                <span>Didn&apos;t Receive the OTP?</span>{" "}
                <Button
                  onClick={handleResendOTPClick}
                  sx={{
                    textTransform: "none",
                    color: "#4891FF",
                    fontWeight: "bold",
                    padding: 0,
                    textDecoration: "underline",
                    paddingBottom: "5px",
                  }}
                  disabled={isResendLoading}
                >
                  {isResendLoading ? (
                    // Show loader/spinner when isResendLoading is true
                    <CircularProgress size={20} sx={{ color: "#4891FF" }} />
                  ) : (
                    "Resend OTP"
                  )}
                </Button>
              </Typography>
            ) : (
              <Typography
                variant="subtitle2"
                gutterBottom
                sx={{ fontWeight: "800" }}
              >
                <span style={{ fontWeight: "bold" }}>
                  Time Remaining: {timer} Seconds
                </span>{" "}
              </Typography>
            )}
            <Typography
              variant="subtitle2"
              gutterBottom
              sx={{ fontWeight: "800" }}
            >
              OR
            </Typography>
            <Typography
              variant="subtitle2"
              gutterBottom
              sx={{ fontWeight: "800" }}
            >
              Let us quickly verify your phone number in our system.
              <br />
              Call:{" "}
              <a href="tel:+18884179680" style={{ color: "#4891FF" }}>
                (*************
              </a>
            </Typography>
          </Item>
        </Grid>
      </Grid>
    </Box>
  );
};

export default VerifyOtpMobile;
