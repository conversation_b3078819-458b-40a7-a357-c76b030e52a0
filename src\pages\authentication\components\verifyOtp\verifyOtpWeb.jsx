import { useEffect, useRef, useState } from "react";
import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import OTPInput from "otp-input-react";
import useOtpLogic from "../../api/verifyOtpApi";
import CircularProgress from "@mui/material/CircularProgress";
import Tooltip from "@mui/material/Tooltip";
import Zoom from "@mui/material/Zoom";
import { ImageBaseURL } from "../../../../config";

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const StyledButton = styled(Button)({
  borderRadius: "10px",
  width: "25%",
  fontWeight: "600",
  padding: "15px",
  backgroundColor: "#4891FF",
  "&:hover": {
    backgroundColor: "#4891FF",
  },
  // Disabled state
  "&.Mui-disabled": {
    backgroundColor: "#4891FF",
    color: "#fff",
    opacity: 0.6,
    cursor: "not-allowed",
  },
});

const SendOtpMobile = () => {
  const {
    OTP,
    setOTP,
    isLoading,
    timer,
    showResend,
    handleResendOTPClick,
    handleVerifyOTPClick,
    isResendLoading,
  } = useOtpLogic();
  const inputStyle = {
    width: 50,
    height: 50,
    border: "2px solid #4891FF",
    fontSize: "18px",
  };

  const buttonRef = useRef(null);
  const [isButtonDisabled, setIsButtonDisabled] = useState(false);

  useEffect(() => {
    // Enable or disable button based on OTP length
    if (OTP.length === 4) {
      setIsButtonDisabled(false);
    } else {
      setIsButtonDisabled(true);
    }
  }, [OTP.length]);

  useEffect(() => {
    // Focus the button when it is enabled
    if (!isButtonDisabled && buttonRef.current) {
      buttonRef.current.focus();
    }
  }, [isButtonDisabled]);

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Grid container spacing={1} sx={{ padding: "20px 0px" }}>
        <Grid item sm={12}>
          <Item className="page_bg">
            <img
              src={ImageBaseURL + "fp_logo.png"}
              alt="Fastpass"
              style={{ maxWidth: "27%", maxHeight: "27%" }}
            />
          </Item>
        </Grid>
        <Grid item xs={6}>
          <Item className="page_bg">
            <img
              src={ImageBaseURL + "verify_otp.png"}
              alt="Verify OTP"
              style={{ maxWidth: "70%", maxHeight: "70%" }}
            />
          </Item>
        </Grid>

        <Grid item xs={6} sx={{ alignSelf: "center" }}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Item>
                <Typography
                  variant="h6"
                  gutterBottom
                  className="text-center font_color fw-bold page_bg"
                >
                  Enter Your OTP
                </Typography>
                <Box display="flex" justifyContent="center">
                  <OTPInput
                    value={OTP}
                    onChange={setOTP}
                    autoFocus
                    OTPLength={4}
                    otpType="number"
                    disabled={isLoading}
                    inputStyles={inputStyle}
                    inputClassName="border-2 input-group overflow-hidden w-60 form-control bg-transparent m-2 text-center"
                    inputProps={{ inputMode: "numeric" }}
                  />
                </Box>
              </Item>
            </Grid>
            <Grid item xs={12} style={{ paddingTop: "0px" }}>
              <Item className="page_bg">
                <StyledButton
                  variant="contained"
                  onClick={handleVerifyOTPClick}
                  disabled={isLoading || isButtonDisabled}
                  ref={buttonRef}
                  startIcon={
                    isLoading ? (
                      <CircularProgress size={20} color="inherit" />
                    ) : null
                  }
                >
                  {isLoading ? "Verifying..." : "VERIFY OTP"}
                </StyledButton>
              </Item>
            </Grid>
            <Grid item xs={12}>
              <Item className="page_bg">
                {showResend ? (
                  <Typography
                    variant="subtitle2"
                    gutterBottom
                    sx={{ fontWeight: "800" }}
                  >
                    <span className="fw-bold">
                      Didn&apos;t Receive the OTP?
                    </span>{" "}
                    <Tooltip
                      TransitionComponent={Zoom}
                      title="Resend OTP to your mobile"
                      arrow
                    >
                      <Button
                        className="fw-bold btn btn-link"
                        sx={{
                          textTransform: "none",
                          textDecoration: "underline",
                          color: "#4891FF",
                          fontSize: "14px",
                          padding: 0,
                          paddingBottom: "3px",
                          "&:hover": {
                            textDecoration: "underline",
                            backgroundColor: "transparent", // Prevent background change on hover
                            color: "#4891FF", // Maintain the same color on hover
                          },
                          "&:focus": {
                            textDecoration: "underline",
                            backgroundColor: "transparent", // Prevent background change on focus
                            color: "#4891FF", // Maintain the same color on focus
                          },
                        }}
                        onClick={handleResendOTPClick}
                        disabled={isResendLoading} // Disable the button while loading
                      >
                        {isResendLoading ? (
                          // Show loader/spinner when isResendLoading is true
                          <CircularProgress
                            size={20}
                            sx={{ color: "#4891FF" }}
                          />
                        ) : (
                          "Resend OTP"
                        )}
                      </Button>
                    </Tooltip>
                  </Typography>
                ) : (
                  <Typography
                    variant="subtitle2"
                    gutterBottom
                    sx={{ fontWeight: "800" }}
                  >
                    <span className="fw-bold">
                      Time Remaining: {timer} Seconds
                    </span>{" "}
                  </Typography>
                )}
                <Typography
                  variant="subtitle2"
                  gutterBottom
                  sx={{ fontWeight: "800" }}
                >
                  OR
                </Typography>
                <Typography
                  variant="subtitle2"
                  gutterBottom
                  sx={{ fontWeight: "800" }}
                >
                  Let us quickly verify your phone number in our system.
                  <br />
                  <Tooltip
                    TransitionComponent={Zoom}
                    title="Call us to verify"
                    arrow
                  >
                    <span>
                      Call:{" "}
                      <a href="tel:+18884179680" style={{ color: "#4891FF" }}>
                        (*************
                      </a>
                    </span>
                  </Tooltip>
                </Typography>
              </Item>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};
export default SendOtpMobile;
