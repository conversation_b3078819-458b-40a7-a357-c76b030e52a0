import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  CircularProgress,
  Box,
} from "@mui/material";
import PropTypes from "prop-types";

const ConfirmLogOutDialog = ({ open, onClose, onConfirm, logoutLoader }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <DialogTitle id="alert-dialog-title">
        {logoutLoader ? "Confirming Log Out" : "Confirm Log Out"}
      </DialogTitle>
      <DialogContent>
        {logoutLoader ? (
          // Centered Loader
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            height="100px"
          >
            <CircularProgress size={40} />
          </Box>
        ) : (
          <DialogContentText id="alert-dialog-description">
            Are you sure you want to log out?
          </DialogContentText>
        )}
      </DialogContent>
      {!logoutLoader && (
        <DialogActions>
          <Button onClick={onClose} sx={{ color: "red" }}>
            Cancel
          </Button>
          <Button onClick={onConfirm} autoFocus>
            Log Out
          </Button>
        </DialogActions>
      )}
    </Dialog>
  );
};

ConfirmLogOutDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  logoutLoader: PropTypes.bool.isRequired,
};

export default ConfirmLogOutDialog;
