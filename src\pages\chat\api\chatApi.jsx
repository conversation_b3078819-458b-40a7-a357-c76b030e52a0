import { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { getUserChatList, getVehicleDetailsByChatId } from "../../../store/apps/chats/index";
import { fetchDealerResponse } from "../../../store/apps/car/index";
import { debounce } from 'lodash';
import { pageMetaData } from "../../../../data/pageMetaData";
import { pageComponentData } from "../../../../data/componentMetaData";
import { useNotification } from "../../../context/NotificationContext"; // Import the custom hook

const ChatApi = () => {
  const [pageSizeVal, setPageSizeVal] = useState(Number(import.meta.env.VITE_CONVERSATION_LIST_PAGE_SIZE)); // To store page size
  let initialPageSize = Number(import.meta.env.VITE_CONVERSATION_LIST_PAGE_SIZE); // Initial page size
  const [page] = useState(1);
  const dispatch = useDispatch();
  const [listItems, setListItems] = useState([]);
  const [searchText, setSearchText] = useState(""); // To store search text
  const [debouncedSearchText, setDebouncedSearchText] = useState(""); // To store search text
  const [currentAction, setCurrentAction] = useState(""); // To store current action of functions
  const [anchorEl, setAnchorEl] = useState(null); // To handle dropdown menu open/close
  const isMenuOpen = Boolean(anchorEl); // To handle dropdown menu open/close
  const [loading] = useState(true);
  const [listLoading, setListLoading] = useState(false);
  const [listLoadOnFilter, setListLoadOnFilter] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [allCarsFetched, setAllCarsFetched] = useState(false);
  const [showChatComponent, setShowChatComponent] = useState(false); // Initially, show NoConversationSelectionWeb
  const sortTypeRef = useRef(""); // Ref to store the current sort type
  const orderTypeRef = useRef("newest"); // Ref to store the current order type
  let token = localStorage.getItem("token");
  const [selectedItem, setSelectedItem] = useState(null); // To store selected item
  const [noVehiclesFound, setNoVehiclesFound] = useState(false); // To show NoConversationSelectionWeb
  const [showNoConvFound, setShowNoConvFound] = useState(false); // To show NoConversationSelectionWeb
  const initialConvData = useRef(false); // To store initial conversation data
  const conversationCount = useRef(0); // Ref to store the conversation count
  const [pageId, setPageId] = useState(null); // State to store the page_id
  const [componentMetaData, setComponentMetaData] = useState(null); // State to store the componentMetaData
  useEffect(() => {
    // Get the page_id from pageMetaData based on slug
    const slug = "conversations"; // Replace this with the desired slug
    const pageData = pageMetaData.find((page) => page.slug === slug);
    if (pageData) {
      setPageId(pageData.id);
    }

    // Get Page Componenet Meta Data
    const matchingComponents = pageComponentData.filter(
      (pageComponent) => pageComponent.page_id === pageId
    );
    if (matchingComponents.length > 0) {
      setComponentMetaData(matchingComponents);
    }
  }, [pageId]);
  const { hasNewMessage, setHasNewMessage, pusherData} = useNotification();
  // ***all the searching, sorting or pagination related functions have been triggered depending on currentAction useState's value & depending upon that the conversation fetch API will be called, onclearing search the api will be called as it was called in initial state*** //

  // Handle search
  useEffect(() => {
    // Only make the API call if searchText changes
    const fetchData = debounce(async () => {
      setCurrentAction("search");
      setDebouncedSearchText(searchText);
    }, 300); // 300ms debounce time
    fetchData();
    // Cleanup function to cancel debounce on unmount
    return () => fetchData.cancel();
  }, [searchText]);

  // Handle sort alphabetically
  const handleSortAlphabetically = async () => {
    handleMenuClose();
    setCurrentAction("alphabetical_sort");
    sortTypeRef.current = "alphabetical"; // Update the ref directly
    orderTypeRef.current = ""; // Reset orderType as it's not needed for alphabetical
    setSelectedItem(null);
  };

  // Handle sort by date
  const handleSortByDate = async () => {
    handleMenuClose();
    setCurrentAction("date_sort");
    sortTypeRef.current = "timestamp"; // Update the ref directly
    orderTypeRef.current = orderTypeRef.current === "newest" ? "oldest" : "newest"; // Toggle orderType
    setSelectedItem(null);
  };

  // calling api to get chat list
  useEffect(() => {
    const fetchInitialChats = async () => {
      // switch case to handle different loading actions
      switch (currentAction) {
        case "search":
        case "alphabetical_sort":
        case "date_sort":
          setListLoadOnFilter(true);
          setListLoading(false);
          initialConvData.current = false;
          setShowChatComponent(false);
          break;
        case "load_more":
          setLoadingMore(true);
          setListLoading(false);
          initialConvData.current = false;
          break;
        default:
          setListLoading(true);
          initialConvData.current = true;
      }
      // connecting store
      await dispatch(
        getUserChatList({
          page: page,
          page_size: pageSizeVal,
          token: token,
          sort_type: sortTypeRef.current, // Use the ref value
          order_type: orderTypeRef.current, // Use the ref value
          search_text: debouncedSearchText,
        })
      )
        .then((response) => {
          setListItems([response.payload.data.conversations]);
          conversationCount.current = response.payload.data.total_conversations;
          // Check if the data is empty INITIALLY and set the `showNoConvFound` state
          if (initialConvData.current && conversationCount.current === 0 && currentAction !== "search") {
            setShowNoConvFound(true);
          } else {
            setShowNoConvFound(false);
          }

          // Check if the number of new cars is less than the page size, which means we might be at the end
          if (conversationCount.current <= pageSizeVal) {
            setAllCarsFetched(true); // Set this to true if fewer cars are returned
          } else {
            setAllCarsFetched(false);
          }
          setListLoading(false);
          setListLoadOnFilter(false);
          setLoadingMore(false);
        })
        .catch((error) => {
          console.error("Error while Fetching Cars:", error);
          setListLoading(false);
          setListLoadOnFilter(false);
          setLoadingMore(false);
          setAllCarsFetched(false);
        });
    };

    fetchInitialChats();
  }, [dispatch, pageSizeVal, debouncedSearchText, token, sortTypeRef.current, orderTypeRef.current]); // Ensure the dependency array only contains the necessary items

  // trigger loading on clear search
  useEffect(() => {
    // Check if the data is empty and set the `noVehiclesFound` state for SEARCH
    if (searchText === '') {
      setNoVehiclesFound(false);
      setListLoadOnFilter(true);
    }
  }, [searchText]);

  // Asynchronously loads more cars for the user's garage
  const loadMoreCars = async () => {
    setCurrentAction("load_more");
    if (loadingMore || allCarsFetched) return; // Check if loading more or all cars have been fetched
    setPageSizeVal((prev) => prev + 10);
  };

  // handle clear search
  const handleClearSearch = () => {
    setPageSizeVal(initialPageSize); // Reset to the initial page size
    setSearchText(""); // Clear search text first
  };

  // handle tune icon click
  const handleTuneIconClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  // Handle menu close
  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  /**
   * Fetches the dealer response data for a given car.
   *
   * This function dispatches an asynchronous action to retrieve the dealer
   * response data using the provided car object. It obtains the user's
   * authentication token from local storage to authorize the request.
   *
   * @param {Object} car - The car object containing necessary details.
   * @returns {Object|null} - Returns the data payload from the response
   *                          if successful, otherwise returns null in
   *                          case of an error.
   */
  const fetchDealerResponseData = async (car) => {
    const token = localStorage.getItem("token");
    try {
      // Dispatch the async action to fetch the vehicle details
      const response = await dispatch(fetchDealerResponse({ car, token }));
      // Return the payload of the response
      return response.payload.data;
    } catch (error) {
      // Log the error and return null
      console.error("Error while fetching dealer response:", error);
      return null;
    }
  };

  /**
   * Fetches user chat details by chat ID.
   *
   * This function dispatches an async action to retrieve the details
   * of a chat conversation using the provided car object. 
   * It retrieves the user's authentication token from local storage 
   * to authorize the request.
   *
   * @param {Object} car - The car object containing necessary details 
   *                       like `vin`, `fp_dealer_id`, and `mc_row_id`.
   * @returns {Object|null} - Returns the data payload from the response
   *                          if successful, otherwise returns null in case
   *                          of an error.
   */
  const fetchVehicleDetailsByChatId = async (conversation_id) => {
    const token = localStorage.getItem("token");
    try {
      // Dispatch the async action to fetch the vehicle details
      const response = await dispatch(getVehicleDetailsByChatId({ conversation_id: conversation_id, token: token }));
      // Return the payload of the response
      return response.payload.data;
    } catch (error) {
      // Log the error and return null
      console.error("Error while fetching dealer response:", error);
      return null;
    }
  };

  const formatTimestamp = (timestamp, timeZone = '') => {
    if (!timestamp) return '';

    return new Date(timestamp).toLocaleString('en-US', {
      timeZone: timeZone || Intl.DateTimeFormat().resolvedOptions().timeZone,
      year: 'numeric',
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
      timeZoneName: "short", // Display timezone abbreviation (e.g., CST)
    });
  };


  return {
    listItems,
    searchText,
    setSearchText,
    anchorEl,
    setAnchorEl,
    isMenuOpen,
    handleClearSearch,
    handleTuneIconClick,
    handleMenuClose,
    loading,
    fetchDealerResponseData,
    formatTimestamp,
    fetchVehicleDetailsByChatId,
    listLoading,
    loadMoreCars,
    allCarsFetched,
    loadingMore,
    handleSortAlphabetically,
    handleSortByDate,
    listLoadOnFilter,
    showChatComponent,
    setShowChatComponent,
    setListItems,
    selectedItem,
    setSelectedItem,
    noVehiclesFound,
    conversationCount,
    showNoConvFound,
    componentMetaData,
    hasNewMessage, 
    setHasNewMessage, 
    pusherData
  };
};

export default ChatApi;
