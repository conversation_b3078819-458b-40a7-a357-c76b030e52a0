import { Box, Paper, Typography, <PERSON><PERSON><PERSON>, Button } from "@mui/material";
import { styled } from "@mui/material/styles";
import React, { useEffect, useState, useRef, useCallback } from "react";
import CircularProgress from "@mui/material/CircularProgress";
import { toast } from "react-toastify";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { MobileDateTimePicker } from "@mui/x-date-pickers/MobileDateTimePicker";
import dayjs from "dayjs";
import CloseIcon from "@mui/icons-material/Close";
import SendIcon from "@mui/icons-material/Send";
import { useParams } from "react-router-dom";
import ChatApi from "../api/chatApi";
import MasterLayout from "../../../components/layouts/masterLayout";
import useGarageListApi from "../../myGarage/api/fetchGarageApi";
import { ImageBaseURL } from "../../../config";
import { pageMetaData } from "../../../../data/pageMetaData";
import { pageComponentData } from "../../../../data/componentMetaData";
import { executeComponentAction } from "../../../util/componentActionUtil";
import { useDispatch } from "react-redux";

const ChatMessage = styled(Paper)(({ theme, alignRight }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  maxWidth: "70%",
  marginBottom: theme.spacing(2),
  backgroundColor: alignRight ? "#4891FF" : "#61C454",
  color: "#fff",
  textAlign: "left",
  alignSelf: alignRight ? "flex-end" : "flex-start",
  borderRadius: alignRight ? "10px 0px 10px 10px" : "0px 10px 10px 10px",
  wordBreak: "break-word",
}));

const Timestamp = styled(Typography)(({ theme }) => ({
  fontSize: "0.75rem",
  color: "#ccc",
  marginTop: theme.spacing(0.5),
}));

const StyledButtonBlue = styled(Button)({
  margin: "0 10px",
  borderRadius: "50%",
  width: "40px",
  height: "40px",
  minWidth: "auto",
  backgroundColor: "#4891FF",
  "&:hover": { backgroundColor: "#1976d2" },
});

const CustomTextField = styled(TextField)({
  "& .MuiInputLabel-root": { color: "#4891FF" },
  "& .MuiOutlinedInput-root": {
    "& fieldset": { borderColor: "#4891FF", borderWidth: "2px" },
    "&:hover fieldset": { borderColor: "#4891FF" },
    "&.Mui-focused fieldset": { borderColor: "#4891FF" },
  },
});

const ChatComponentMobile = () => {
  const [messages, setMessages] = useState([]);
  const [userRequestMsg, setUserRequestMsg] = useState("");
  const [userMsgSendLoader, setUserMsgSendLoader] = useState(false);
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [dateTimeValue, setDateTimeValue] = useState(dayjs(""));
  const [convLoading, setConvLoading] = useState(false);
  const { conversation_id } = useParams();
  const conversationId = conversation_id;
  const { fetchVehicleDetailsByChatId, fetchDealerResponseData, hasNewMessage, setHasNewMessage } = ChatApi();
  const messageEndRef = useRef(null);
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
  const [fetchSelectedVehicleDetails, setFetchSelectedVehicleDetails] =
    useState({});
  const [pageId, setPageId] = useState(null);
  const [componentMetaData, setComponentMetaData] = useState(null); // State to store the componentMetaData
  const dispatch = useDispatch(); // Redux dispatch
  // Code For capture send message button click //

  useEffect(() => {
    // Get the page_id from pageMetaData based on slug
    const slug = "conversations"; // Replace this with the desired slug
    const pageData = pageMetaData.find((page) => page.slug === slug);
    if (pageData) {
      setPageId(pageData.id);
    }

    // Get Page Componenet Meta Data
    const matchingComponents = pageComponentData.filter(
      (pageComponent) => pageComponent.page_id === pageId
    );

    if (matchingComponents.length > 0) {
      setComponentMetaData(matchingComponents);
    }
  }, [pageId]);

  // End //
  let selVehicleDet = {};
  const fetchChatMessages = useCallback(async () => {
    setConvLoading(true);
    const getVehicleDetails = await fetchVehicleDetailsByChatId(conversationId);

    // Store the car details in the state
    if (getVehicleDetails.length > 0) {
      const vehicleDetail = getVehicleDetails[0]; // Assuming you need only the first vehicle details
      selVehicleDet = {
        vin: vehicleDetail.vin,
        fp_dealer_id: vehicleDetail.fp_dealer_id,
        mc_row_id: vehicleDetail.mc_row_id,
        year: vehicleDetail.year,
        conversation_id: conversationId,
        heading: vehicleDetail.heading,
        is_delete_mc: vehicleDetail.is_delete_mc,
      };
      setFetchSelectedVehicleDetails({
        vin: vehicleDetail.vin,
        fp_dealer_id: vehicleDetail.fp_dealer_id,
        mc_row_id: vehicleDetail.mc_row_id,
        year: vehicleDetail.year,
        conversation_id: conversationId,
        heading: vehicleDetail.heading,
        seller_name: vehicleDetail.seller_name,
        photo_url: vehicleDetail.photo_url,
        deleted_at: vehicleDetail.deleted_at,
        conversation_details_url:
          window.location.href.split("/conversation")[0] + "/conversation",
        is_delete_mc: vehicleDetail.is_delete_mc,
      });
    }

    const resp = await fetchDealerResponseData(selVehicleDet); // Use carDetails here
    try {
      if (resp && resp.SalesForceResponse) {
        const parsedResponse = JSON.parse(resp.SalesForceResponse);

        if (parsedResponse[0]?.error?.Message) {
          toast.error(parsedResponse[0].error.Message);
          return;
        }
        const defaultMessage = {
          id: 1,
          text: "Hello! How can I assist you today?",
          fromDealer: true,
        };
        if (parsedResponse[0]?.success?.data) {
          const chatMessages = parsedResponse[0].success.data
            .map((msg, index) => ({
              id: index + 1,
              text: msg.EmailMessage,
              fromDealer: msg.Sender === "Dealer",
              timestamp: new Date(msg.CreatedDate),
            }))
            .sort((a, b) => a.timestamp - b.timestamp)
            .map((msg) => ({
              ...msg,
              timestamp: msg.timestamp.toLocaleString(),
            }));

          setMessages([defaultMessage, ...chatMessages]);
        }
      }
      setConvLoading(false);
    } catch (error) {
      toast.error("Failed to load conversation. Please try again.");
      setConvLoading(false);
    }
  });

  useEffect(() => {
    fetchChatMessages();
  }, []);

  const scrollToBottom = () => {
    messageEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendAskDealerMessage = async (userMsg) => {
    const formattedDateTime =
      dateTimeValue && dateTimeValue.isValid()
        ? dateTimeValue.format("YYYY-MM-DD HH:mm:ss")
        : null;
    // Proceed only if userMsg has a value or formattedDateTime is valid
    if (
      !userMsg &&
      (!formattedDateTime ||
        formattedDateTime === null ||
        formattedDateTime === "Invalid date")
    ) {
      return;
    }
    setUserMsgSendLoader(true);
    try {
      await sendDealerRequestMsg(
        fetchSelectedVehicleDetails,
        userMsg,
        formattedDateTime,
        testDriveMessage
      );
      setUserRequestMsg(""); // Clear the text field if needed
      setDateTimeValue(dayjs(""));
      setShowDateTimePicker(false);
      toast.success(
        `Message Sent Successfully. Waiting for dealer response...`
      );

      await fetchChatMessages();
    } catch (error) {
      console.error("Failed to send message:", error);
    } finally {
      setUserMsgSendLoader(false);
    }
    const component = componentMetaData.find(
      (comp) => comp.slug === "send-message"
    );
    if (component) {
      executeComponentAction(
        dispatch,
        component.id,
        fetchSelectedVehicleDetails.mc_row_id
      );
    }
  };

  useEffect(() => {
    if (hasNewMessage) {
      fetchChatMessages();
      setHasNewMessage(false); // Reset the flag after fetching messages
    }
  }, [hasNewMessage, fetchChatMessages, setHasNewMessage]);

  const handleToggleDateTimePicker = () => {
    setShowDateTimePicker((prevState) => !prevState);
    setDateTimeValue(dayjs(""));
  };
  const { sendDealerRequestMsg, memberName } = useGarageListApi();
  const testDriveMessage = `
  I'm interested in scheduling a test drive for <b>${
    fetchSelectedVehicleDetails.length > 0
      ? fetchSelectedVehicleDetails[0].heading
      : "the selected vehicle"
  }</b>.<br />
  Are you available on`;
  const truncateText = (text, maxLength) => {
    if (text.length > maxLength) {
      return text.slice(0, maxLength) + "...";
    }
    return text;
  }; // Truncate text if necessary
  const fallbackImg = `${ImageBaseURL}fallback_image.png`;
  if (convLoading) {
    // Show only the spinner while loading
    return (
      <MasterLayout>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "100vh", // Center the spinner on the screen
          }}
        >
          <CircularProgress size={50} />
        </Box>
      </MasterLayout>
    );
  }
  return (
    <MasterLayout>
      <Box
        sx={{
          padding: { xs: 1, sm: 2 },
          mb: 4,
          mt: 8,
          position: "relative",
          display: "flex",
          flexDirection: "column",
        }}
      >
        {/* Header Section */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            borderBottom: "1px solid #4981ff",
            padding: 1,
            backgroundColor: "#ffffff",
            position: "fixed",
            top: 55,
            left: 0,
            right: 0,
            zIndex: 1,
          }}
        >
          {/* Image */}
          <img
            src={fetchSelectedVehicleDetails?.photo_url || fallbackImg}
            alt="vehicle"
            style={{
              height: "auto",
              maxWidth: "100px",
              objectFit: "contain",
              // Responsive width
              width: { xs: "20%", sm: "15%", md: "10%" },
            }}
            onError={(e) => {
              e.target.src = fallbackImg;
            }}
          />
          {/* Seller Name */}
          {fetchSelectedVehicleDetails.seller_name && (
            <Typography
              variant="body1"
              gutterBottom
              sx={{
                fontWeight: "bold",
                flexGrow: 1,
                textAlign: "center",
              }}
            >
              {truncateText(
                fetchSelectedVehicleDetails.seller_name.toUpperCase(),
                30
              )}
            </Typography>
          )}
        </Box>

        {/* Main Content (Chat) */}
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            padding: { xs: 1, sm: 2 },
            position: "relative",
            flexGrow: 1,
            marginTop: "85px", // Space for the fixed header
            marginBottom: "85px", // Space for the fixed footer
            overflowY: "auto",
          }}
        >
          {messages.map((msg) => (
            <ChatMessage key={msg.id} alignRight={!msg.fromDealer}>
              {msg.text
                .replace(/\\n/g, "\n") // Convert escaped new lines to actual new lines
                .split("\n") // Split by new lines
                .map((line, index) => (
                  <React.Fragment key={index}>
                    {line.trim() === "" ? (
                      <br style={{ lineHeight: "2em" }} />
                    ) : (
                      <span dangerouslySetInnerHTML={{ __html: line }} /> // Render HTML safely
                    )}
                    <br />
                  </React.Fragment>
                ))}

              <Timestamp
                alignRight={!msg.fromDealer}
                style={{ color: "white" }}
              >
                {msg.timestamp}
              </Timestamp>
            </ChatMessage>
          ))}
          <div ref={messageEndRef} />
        </Box>

        {/* Footer Section */}

        <Box
          sx={{
            position: "fixed",
            bottom: 80,
            left: 0,
            right: 0,
            backgroundColor: "#ffffff",
            borderTop: "1px solid #4981ff",
            padding: 2,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            zIndex: 1,
          }}
        >
          {fetchSelectedVehicleDetails ? (
            fetchSelectedVehicleDetails.deleted_at !== null ? (
              <Typography
                variant="body1"
                sx={{ color: "red", textAlign: "center", width: "100%" }}
              >
                <b>Vehicle is no longer available in your garage</b>
              </Typography>
            ) : fetchSelectedVehicleDetails.is_delete_mc === true ? (
              <Typography
                variant="body1"
                sx={{ color: "red", textAlign: "center", width: "100%" }}
              >
                <b>Vehicle is sold out</b>
              </Typography>
            ) : (
              <>
                {showDateTimePicker && (
                  <Typography
                    variant="body1"
                    sx={{ marginBottom: 2, textAlign: "center" }}
                    dangerouslySetInnerHTML={{ __html: testDriveMessage }}
                  />
                )}

                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    width: "100%",
                  }}
                >
                  <StyledButtonBlue onClick={handleToggleDateTimePicker}>
                    {showDateTimePicker ? (
                      <CloseIcon sx={{ color: "white" }} />
                    ) : (
                      <svg
                        width="23"
                        height="23"
                        viewBox="0 0 48 48"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M47.6388 23.8481C47.6388 36.9301 37.0338 47.5351 23.9519 47.5351C10.8699 47.5351 0.264893 36.9301 0.264893 23.8481C0.264893 10.7662 10.8699 0.161133 23.9519 0.161133C37.0338 0.161133 47.6388 10.7662 47.6388 23.8481ZM21.021 42.5724C19.4605 38.7423 15 28.365 12.1084 27.4012C10.0859 26.727 7.29597 26.8203 5.26757 27.0258C6.61858 35.0274 12.9848 41.3247 21.021 42.5724ZM5.78265 18.4486C8.10752 10.6136 15.3623 4.89853 23.9519 4.89853C32.5415 4.89853 39.7962 10.6136 42.1211 18.4486C38.3814 17.7479 31.6695 16.742 23.9519 16.742C16.2342 16.742 9.52236 17.7479 5.78265 18.4486ZM42.6362 27.0258C40.6078 26.8203 37.8178 26.727 35.7954 27.4012C32.9037 28.365 28.4432 38.7423 26.8827 42.5724C34.9189 41.3247 41.2852 35.0274 42.6362 27.0258Z"
                          fill="white"
                        />
                      </svg>
                    )}
                  </StyledButtonBlue>

                  <Box sx={{ flexGrow: 1, marginX: 2 }}>
                    {showDateTimePicker ? (
                      <LocalizationProvider dateAdapter={AdapterDayjs}>
                        <Box sx={{ width: "100%" }}>
                          {isIOS ? (
                            <TextField
                              type="datetime-local"
                              label="Select Date Time Slot"
                              value={
                                dateTimeValue
                                  ? dayjs(dateTimeValue).format(
                                      "YYYY-MM-DDTHH:mm"
                                    )
                                  : ""
                              }
                              onChange={(e) =>
                                setDateTimeValue(dayjs(e.target.value))
                              }
                              placeholder="Tap to select date/time"
                              InputProps={{
                                style: {
                                  cursor: "pointer",
                                },
                              }}
                              fullWidth
                              sx={{
                                minWidth: { xs: "100%", sm: "200px" },
                                marginTop: { xs: 2, sm: 0 },
                              }}
                            />
                          ) : (
                            <MobileDateTimePicker
                              label="Select Date Time Slot"
                              value={dateTimeValue}
                              onChange={(newValue) =>
                                setDateTimeValue(newValue)
                              }
                              minDateTime={dayjs()}
                              sx={{
                                minWidth: { xs: "100%", sm: "200px" },
                                marginTop: { xs: 2, sm: 0 },
                              }}
                            />
                          )}
                        </Box>
                      </LocalizationProvider>
                    ) : (
                      <CustomTextField
                        id="outlined-multiline-flexible"
                        label="Type Your Query Here..."
                        variant="outlined"
                        multiline
                        fullWidth
                        sx={{
                          width: "100%",
                          background: "#ffffff",
                        }}
                        autoComplete="off"
                        InputProps={{
                          sx: {
                            fontSize: "16px",
                          },
                        }}
                        value={userRequestMsg}
                        onChange={(e) => setUserRequestMsg(e.target.value)}
                      />
                    )}
                  </Box>

                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    {userMsgSendLoader ? (
                      <CircularProgress size={40} sx={{ color: "#4891FF" }} />
                    ) : (
                      <SendIcon
                        sx={{
                          color: "#4891FF",
                          cursor: "pointer",
                          fontSize: "40px",
                        }}
                        onClick={() => sendAskDealerMessage(userRequestMsg)}
                      />
                    )}
                  </Box>
                </Box>
              </>
            )
          ) : (
            <Typography
              variant="body1"
              sx={{ color: "red", textAlign: "center", width: "100%" }}
            >
              <b>No vehicle data available</b>
            </Typography>
          )}
        </Box>
      </Box>
    </MasterLayout>
  );
};

export default ChatComponentMobile;
