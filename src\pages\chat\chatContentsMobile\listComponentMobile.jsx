import React, { useEffect, useState } from "react";
import {
  List,
  ListItem,
  Divider,
  Grid,
  Box,
  Typography,
  CircularProgress,
  Button,
} from "@mui/material";
import { ImageBaseURL } from "../../../config";
import PropTypes from "prop-types";
import ChatApi from "../api/chatApi";

const ListComponentMobile = ({
  listItems,
  onListItemClick,
  listLoading,
  listLoadOnFilter,
  loadMoreCars,
  loadingMore,
  allCarsFetched,
  conversationCount
}) => {

const [updatedListItems, setUpdatedListItems] = useState(listItems);
const fallbackImg = `${ImageBaseURL}fallback_image.png`;
  
const { hasNewMessage, pusherData } = ChatApi();

  // Sync updatedListItems with incoming listItems when listItems prop changes
  useEffect(() => {
    setUpdatedListItems(listItems);
  }, [listItems]);

  // Update msg_count when new message arrives
  useEffect(() => {
    if (hasNewMessage && pusherData?.chatId) {
      setUpdatedListItems((prevListItems) => {
        const newList = prevListItems.map((subArray) =>
          subArray.map((item) => {
            if (item.conversation_id === pusherData.chatId) {
              return {
                ...item,
                msg_count: (item.msg_count || 0) + pusherData.user_msg_count + 1,
              };
            }
            return item;
          })
        );
        return newList;
      });
    }
  }, [hasNewMessage, pusherData]);

  const handleLoadMore = () => {
    if (!loadingMore && !listLoading) {
      loadMoreCars();
    }
  };

  // Debug: Log the content variable's state
  let content;
  if (loadingMore && !allCarsFetched) {
    content = (
      <Box
        sx={{ display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <CircularProgress size={30} />
        <Typography
          variant="subtitle1"
          color="#4891FF"
          sx={{ marginLeft: 2, fontWeight: "600" }}
        >
          Loading more vehicles...
        </Typography>
      </Box>
    );
  } else if (
    !loadingMore &&
    !allCarsFetched &&
    !listLoading &&
    !listLoadOnFilter &&
    conversationCount.current > 10
  ) {
    content = (
      <Button
        variant="outlined"
        onClick={handleLoadMore}
        className="map-text"
        style={{
          textDecoration: "underline",
          backgroundColor: "#fff",
          border: "none",
          color: "#4891FF",
          fontWeight: "800",
        }}
      >
        Click to Load More Conversations
      </Button>
    );
  }

  return (
    <List>
      {listLoading || listLoadOnFilter ? (
        <Typography variant="body2" color="textSecondary" align="center">
          Your conversations are loading...
        </Typography>
      ) : (
        Array.isArray(listItems) && conversationCount.current > 0 ? (
          <>
            {/* Loop over each array in listItems */}
            {listItems.map((subArray, arrayIndex) =>
              // Map over each item in the current subArray
              subArray.map((item, index) => (
                <React.Fragment key={`${arrayIndex}-${index}`}>
                  <ListItem
                    sx={{ pr: 1 }}
                    button // Makes the ListItem clickable
                    onClick={() => onListItemClick(item)} // Calls the click handler with the item
                  >
                    <Grid
                      container
                      spacing={2}
                      className="chat_list_shadow"
                      sx={{
                        my: 0.5,
                        padding: "10px", // Adjusted padding for mobile
                        backgroundColor: item.msg_count > 0 ? "aliceblue" : "#ffffff", // Set background to aliceblue if count > 0
                        position: "relative", // To position timestamp
                        transition: "background-color 0.3s ease",
                      }}
                    >
                      {/* Image Container */}
                      <Grid
                        item
                        xs={4} // Adjusted for better fit on mobile
                        container
                        alignItems="center"
                        justifyContent="flex-end"
                      >
                        <img
                          src={item.photo_url || fallbackImg}
                          alt="Item"
                          style={{
                            height: 80, // Reduced height for mobile
                            width: 100, // Reduced width for mobile
                            objectFit: "contain",
                          }}
                          onError={(e) => {
                            e.target.src = fallbackImg;
                          }}
                        />
                      </Grid>

                      {/* Text Container */}
                      <Grid
                        item
                        xs={item.msg_count > 0 ? 6 : 8} // Adjusted for better fit on mobile
                        container
                        direction="column"
                        sx={{
                          justifyContent: "center",
                          paddingLeft: "10px", // Adjusted padding for mobile
                        }}
                      >
                        <Box>
                          <Typography variant="body1" fontWeight="bold">
                            {item.heading || ""}
                          </Typography>
                          <Typography variant="body2" mt={1}>
                            {item.seller_name || ""}
                          </Typography>
                        </Box>
                      </Grid>

                      {/* Count Badge */}
                      {item.msg_count > 0 && (
                        <Grid
                          item
                          xs={2} // Adjusted for better fit on mobile
                          container
                          alignItems="center"
                          justifyContent="flex-end"
                        >
                          <Box
                            sx={{
                              backgroundColor: "#4891FF",
                              borderRadius: "50%",
                              width: 30,
                              height: 30,
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              color: "white",
                              fontWeight: "600",
                              fontSize: "14px",
                            }}
                          >
                            {item.msg_count > 9 ? "9+" : item.msg_count}
                          </Box>
                        </Grid>
                      )}

                      {/* Timestamp */}
                      <Grid
                        item
                        xs={12} // Adjusted for better fit on mobile
                        container
                        direction="column"
                        sx={{
                          justifyContent: "center",
                          paddingLeft: "10px", // Adjusted padding for mobile
                          textAlign: "right", // Aligns text to the right
                        }}
                      >
                        <Typography variant="caption">
                          {item.last_seen_at
                            ? new Date(item.last_seen_at).toLocaleString()
                            : "No timestamp"}{" "}
                          {/* Format the timestamp */}
                        </Typography>
                      </Grid>
                    </Grid>
                  </ListItem>

                  {index < conversationCount.current - 1 && (
                    <Divider sx={{ borderWidth: 1, borderColor: "#808080" }} />
                  )}
                </React.Fragment>
              ))
            )}
            <Grid item xs={12} sx={{ textAlign: "center", padding: "10px" }}>
              {content}
            </Grid>
          </>
        ) : (
          <Box sx={{ textAlign: "center", padding: 2 }}>
            <Typography
              variant="body1"
              gutterBottom
              sx={{ fontWeight: '500' }}
            >
              No conversation found against your search criteria
            </Typography>
          </Box>
        )
      )}
    </List>
  );
};

ListComponentMobile.propTypes = {
  listItems: PropTypes.array.isRequired,
  onListItemClick: PropTypes.func.isRequired,
  listLoading: PropTypes.bool.isRequired,
  listLoadOnFilter: PropTypes.bool.isRequired,
  loadMoreCars: PropTypes.func.isRequired,
  loadingMore: PropTypes.bool.isRequired,
  allCarsFetched: PropTypes.bool.isRequired,
  conversationCount: PropTypes.number.isRequired,
};

export default ListComponentMobile;