import { useNavigate } from "react-router-dom";
import { useMediaQuery } from "react-responsive";
import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Icon from "../../../icon";
import { ImageBaseURL } from "../../../config";

function NoDataFound() {
  const navigate = useNavigate();
  const Item = styled(Paper)(({ theme }) => ({
    ...theme.typography.body2,
    padding: theme.spacing(1),
    textAlign: "center",
    borderRadius: "none",
    boxShadow: "none",
  }));

  const StyledButton = styled(({ ...other }) => <Button {...other} />)(
    ({ isMobile }) => ({
      borderRadius: "10px",
      width: isMobile ? "50%" : "30%",
      fontSize: isMobile ? "14px" : "18px",
      fontWeight: "600",
      padding: "15px",
      backgroundColor: "#4891FF",
      "&:hover": {
        backgroundColor: "primary",
      },
    })
  );

  const token = localStorage.getItem("token");
  const handleReload = () => {
    if (token) {
      navigate("/my-garage");
    } else {
      navigate("/");
    }
  };
  const isMobile = useMediaQuery({ maxWidth: 767 });

  return (
    <Box
      sx={{
        flexGrow: 1,
        height: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Grid container spacing={2} sx={{ padding: "15px 0px" }}>
        <Grid item xs={12}>
          <Item className="page_bg">
            <img
              src={`${ImageBaseURL}no_conversation.png`}
              alt="Not Found"
              style={
                isMobile
                  ? { maxWidth: "60%", maxHeight: "60%" }
                  : { maxWidth: "50%", maxHeight: "50%" }
              }
            />
          </Item>
        </Grid>
        <Grid item xs={12}>
          <Item className="page_bg">
            <Typography
              variant="subtitle2"
              gutterBottom
              sx={{ fontWeight: "800" }}
              style={isMobile ? { fontSize: "14px" } : { fontSize: "18px" }}
            >
             <b>No Conversation Found</b>
            </Typography>
            <Typography
              variant="subtitle2"
              gutterBottom
              sx={{ fontWeight: "800" }}
              style={isMobile ? { fontSize: "14px" } : { fontSize: "18px" }}
            >
             Start Conversation From Garage
            </Typography>
          </Item>
        </Grid>
        <Grid item xs={12}>
          <Item className="page_bg">
            <StyledButton
              isMobile={isMobile}
              variant="contained"
              onClick={handleReload}
            >
            <Icon icon="home-garage-white" size={30} />
              <span className="btn_class_landing_mobile">
              Go To My Garage
              </span>
            </StyledButton>
          </Item>
        </Grid>
      </Grid>
    </Box>
  );
}

export default NoDataFound;
