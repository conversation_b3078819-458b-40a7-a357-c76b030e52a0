import { Box, Grid, Paper, Typography, Text<PERSON>ield, Button } from "@mui/material";
import { styled } from "@mui/material/styles";
import React, { useEffect, useState, useRef } from "react";
import CircularProgress from "@mui/material/CircularProgress";
import useGarageListApi from "../../myGarage/api/fetchGarageApi";
import ChatApi from "../api/chatApi";
import { toast } from "react-toastify";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DesktopDateTimePicker } from "@mui/x-date-pickers/DesktopDateTimePicker";
import dayjs from "dayjs";
import CloseIcon from "@mui/icons-material/Close";
import SendIcon from "@mui/icons-material/Send";
import { ImageBaseURL } from "../../../config";
import { pageMetaData } from "../../../../data/pageMetaData";
import { pageComponentData } from "../../../../data/componentMetaData";
import { executeComponentAction } from "../../../util/componentActionUtil";
import { useDispatch } from "react-redux";

const ChatMessage = styled(Paper)(({ theme, alignRight }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  maxWidth: "70%",
  marginBottom: theme.spacing(2),
  backgroundColor: alignRight ? "#4891FF" : "#61C454",
  color: "#fff",
  textAlign: "left",
  alignSelf: alignRight ? "flex-end" : "flex-start",
  borderRadius: alignRight ? "10px 0px 10px 10px" : "0px 10px 10px 10px",
  position: "relative",
  display: "flex",
  flexDirection: "column",
  alignItems: alignRight ? "flex-end" : "flex-start",
  wordBreak: "break-word",
}));

const Timestamp = styled(Typography)(({ theme }) => ({
  fontSize: "0.75rem",
  color: "#ccc",
  marginTop: theme.spacing(0.5),
}));

const StyledButtonBlue = styled(Button)({
  margin: "0 10px",
  borderRadius: "50%",
  width: "40px",
  height: "40px",
  minWidth: "auto",
  fontWeight: "600",
  padding: "5px",
  backgroundColor: "#4891FF",
  boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.2)",
  "&:hover": {
    backgroundColor: "#1976d2",
  },
});

const labelStyle = {
  color: "#4891FF",
};

const CustomTextField = styled(TextField)({
  "& .MuiInputLabel-root": labelStyle, // Apply label style
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "bold", // Bold the input field value
    },
  },
});

const ChatComponentWeb = (selectedCar) => {
  const [userRequestMsg, setUserRequestMsg] = useState("");
  const [userMsgSendLoader, setUserMsgSendLoader] = useState(null);
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hello! How can I assist you today?",
      fromDealer: true,
    },
  ]);
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [dateTimeValue, setDateTimeValue] = useState(dayjs(""));
  const [pageId, setPageId] = useState(null);
  const [componentMetaData, setComponentMetaData] = useState(null); // State to store the componentMetaData
  const dispatch = useDispatch(); // Redux dispatch
  useEffect(() => {
    const defaultMessage = {
      id: 1,
      text: "Hello! How can I assist you today?",
      fromDealer: true,
    };
    if (selectedCar.sfmessages && selectedCar.sfmessages.length > 0) {
      // Process API data
      const formattedMessages = selectedCar.sfmessages.map((msg) => ({
        id: msg.id,
        text: msg.text,
        fromDealer: msg.fromDealer,
        timestamp: msg.timestamp,
      }));

      // Combine default message with API messages and sort by timestamp
      const allMessages = [defaultMessage, ...formattedMessages].sort(
        (a, b) => new Date(a.timestamp) - new Date(b.timestamp)
      );

      setMessages(allMessages);
    } else {
      // Reset messages if no data is present
      setMessages([]);
    }
  }, [selectedCar.sfmessages]);
  // Code For capture send message button click //

  useEffect(() => {
    // Get the page_id from pageMetaData based on slug
    const slug = "conversations"; // Replace this with the desired slug
    const pageData = pageMetaData.find((page) => page.slug === slug);
    if (pageData) {
      setPageId(pageData.id);
    }

    // Get Page Componenet Meta Data
    const matchingComponents = pageComponentData.filter(
      (pageComponent) => pageComponent.page_id === pageId
    );

    if (matchingComponents.length > 0) {
      setComponentMetaData(matchingComponents);
    }
  }, [pageId]);

  // End //
  const messageEndRef = useRef(null);
  const { sendDealerRequestMsg, memberName } = useGarageListApi();
  const { hasNewMessage, setHasNewMessage } = ChatApi();
  const sendAskDealerMessage = async (selectedCar, userMsg) => {
    const formattedDateTime =
      dateTimeValue && dateTimeValue.isValid()
        ? dateTimeValue.format("YYYY-MM-DD HH:mm:ss")
        : null;
    // Proceed only if userMsg has a value or formattedDateTime is valid
    if (
      !userMsg &&
      (!formattedDateTime ||
        formattedDateTime === null ||
        formattedDateTime === "Invalid date")
    ) {
      return;
    }
    setUserMsgSendLoader(true);
    try {
      await sendDealerRequestMsg(
        selectedCar.selectedCar,
        userMsg,
        formattedDateTime,
        testDriveMessage
      );
      setUserRequestMsg(""); // Clear the text field if needed
      setDateTimeValue(dayjs(""));
      setShowDateTimePicker(false);
      toast.success(
        `Message Sent Successfully. Waiting for dealer response...`
      );
      // Fetch and update messages after sending
      const updatedMessages = await selectedCar.refreshMessages(
        selectedCar.selectedCar
      );

      if (updatedMessages) {
        setMessages(updatedMessages);
      }
    } catch (error) {
      console.error("Failed to send message:", error);
    } finally {
      setUserMsgSendLoader(false);
    }
    const component = componentMetaData.find(
      (comp) => comp.slug === "send-message"
    );
    if (component) {
      executeComponentAction(
        dispatch,
        component.id,
        selectedCar.selectedCar.mc_row_id
      );
    }
  };
  let testDriveMessage = "";
  if (selectedCar) {
    testDriveMessage = `I'm interested in scheduling a test drive for <b>${selectedCar.selectedCar.heading}</b>.<br />Are you available on`;
  } else {
    testDriveMessage = "";
  }

  const scrollToBottom = () => {
    messageEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (hasNewMessage && selectedCar) {
      const fetchUpdatedMessages = async () => {
        try {
          const updatedMessages = await selectedCar.refreshMessages(selectedCar.selectedCar);
          if (updatedMessages) {
            setMessages(updatedMessages);
  
            // Reset the message count for the currently selected chat
            selectedCar.setListItems((prevListItems) =>
              prevListItems.map((group) =>
                group.map((item) =>
                  item.conversation_id === selectedCar.conversation_id
                    ? { ...item, msg_count: 0 } // Set count to 0
                    : item
                )
              )
            );
          }
        } catch (error) {
          console.error("Failed to fetch updated messages:", error);
        } finally {
          setHasNewMessage(false); // Reset the flag after processing
        }
      };
  
      fetchUpdatedMessages();
    }
  }, [hasNewMessage, selectedCar, setHasNewMessage, selectedCar.setListItems]);
  
  const handleToggleDateTimePicker = () => {
    setShowDateTimePicker((prevState) => !prevState);
    setDateTimeValue(dayjs(""));
  };
  const truncateText = (text, maxLength) => {
    if (text.length > maxLength) {
      return text.slice(0, maxLength) + "...";
    }
    return text;
  }; // Truncate text if necessary
  const fallbackImg = `${ImageBaseURL}fallback_image.png`;
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
        width: "100%",
      }}
    >
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          borderBottom: "1px solid #4981ff",
          ml: 2,
        }}
      >
        {/* Image */}
        <img
          src={selectedCar.selectedCar?.photo_url || fallbackImg}
          alt="vehicle"
          style={{
            height: "auto",
            maxWidth: "100px",
            marginRight: 2, // Corresponds to theme.spacing(2)
            marginBottom: 1, // Corresponds to theme.spacing(1)
            objectFit: "contain",
            // Responsive width
            width: { xs: "20%", sm: "15%", md: "10%" },
          }}
          onError={(e) => {
            e.target.src = fallbackImg;
          }}
        />

        {/* Seller Name */}
        {selectedCar.selectedCar && (
          <Typography
            variant="h6"
            gutterBottom
            sx={{
              fontWeight: "bold",
              flexGrow: 1,
              textAlign: "center",
            }}
          >
            {truncateText(
              selectedCar.selectedCar.seller_name.toUpperCase(),
              30
            )}
          </Typography>
        )}
      </Box>

      {/* Chat Window */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          padding: 2,
          position: "relative",
          overflowY: "auto",
          flexGrow: 1,
        }}
      >
        {selectedCar.loading ? (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 1,
            }}
          >
            <CircularProgress />
          </Box>
        ) : (
          selectedCar.selectedCar &&
          messages.length > 0 &&
          messages.map((msg) => (
            <ChatMessage key={msg.id} alignRight={!msg.fromDealer}>
              {msg.text
                .replace(/\\n/g, "\n") // Convert escaped new lines to actual new lines
                .split("\n") // Split by new lines
                .map((line, index) => (
                  <React.Fragment key={index}>
                    {line.trim() === "" ? (
                      <br style={{ lineHeight: "2em" }} />
                    ) : (
                      <span dangerouslySetInnerHTML={{ __html: line }} /> // Render HTML safely
                    )}
                    <br />
                  </React.Fragment>
                ))}

              <Timestamp
                alignRight={!msg.fromDealer}
                style={{ color: "white" }}
              >
                {msg.timestamp}
              </Timestamp>
            </ChatMessage>
          ))
        )}
        <div ref={messageEndRef} />
      </Box>

      {/* Footer */}
      <Box
        sx={{
          pt: 2,
          borderTop: "1px solid #4981ff",
          textAlign: "center",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
          width: "100%", // Ensures it spans the full width of the footer
          flexShrink: 0, // Prevents shrinking
        }}
      >
        {/* Grid for chat input and DateTimePicker */}
        <Grid
          container
          spacing={1}
          justifyContent="center"
          alignItems="center"
          direction="row"
        >
          {selectedCar?.selectedCar ? (
            selectedCar?.selectedCar?.deleted_at !== null ? (
              <Typography
                variant="body1"
                sx={{ color: "red", textAlign: "center", width: "100%" }}
              >
                <b>Vehicle is no longer available in your garage</b>
              </Typography>
            ) : selectedCar?.selectedCar?.is_delete_mc === true ? (
              <Typography
                variant="body1"
                sx={{ color: "red", textAlign: "center", width: "100%" }}
              >
                <b>Vehicle is sold out</b>
              </Typography>
            ) : (
              <>
                <Grid
                  item
                  md={12}
                  xs={12}
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  {showDateTimePicker ? (
                    <Typography
                      variant="body1"
                      sx={{ marginBottom: 2, textAlign: "center" }}
                      dangerouslySetInnerHTML={{ __html: testDriveMessage }}
                    />
                  ) : (
                    ""
                  )}
                </Grid>
                <Grid
                  item
                  md={2}
                  xs={2}
                  sx={{ display: "flex", justifyContent: "center" }}
                >
                  <StyledButtonBlue onClick={handleToggleDateTimePicker}>
                    {showDateTimePicker ? (
                      <CloseIcon sx={{ color: "white" }} />
                    ) : (
                      <svg
                        width="23"
                        height="23"
                        viewBox="0 0 48 48"
                        fill="none"
                      >
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M47.6388 23.8481C47.6388 36.9301 37.0338 47.5351 23.9519 47.5351C10.8699 47.5351 0.264893 36.9301 0.264893 23.8481C0.264893 10.7662 10.8699 0.161133 23.9519 0.161133C37.0338 0.161133 47.6388 10.7662 47.6388 23.8481ZM21.021 42.5724C19.4605 38.7423 15 28.365 12.1084 27.4012C10.0859 26.727 7.29597 26.8203 5.26757 27.0258C6.61858 35.0274 12.9848 41.3247 21.021 42.5724ZM5.78265 18.4486C8.10752 10.6136 15.3623 4.89853 23.9519 4.89853C32.5415 4.89853 39.7962 10.6136 42.1211 18.4486C38.3814 17.7479 31.6695 16.742 23.9519 16.742C16.2342 16.742 9.52236 17.7479 5.78265 18.4486ZM42.6362 27.0258C40.6078 26.8203 37.8178 26.727 35.7954 27.4012C32.9037 28.365 28.4432 38.7423 26.8827 42.5724C34.9189 41.3247 41.2852 35.0274 42.6362 27.0258Z"
                          fill="white"
                        />
                      </svg>
                    )}
                  </StyledButtonBlue>
                </Grid>
                <Grid
                  item
                  md={8}
                  xs={8}
                  sx={{ display: "flex", justifyContent: "center" }}
                >
                  {showDateTimePicker ? (
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <Box sx={{ width: { xs: "100%", sm: "auto" } }}>
                        <DesktopDateTimePicker
                          label="Select Date Time Slot"
                          value={dateTimeValue}
                          onChange={(newValue) => setDateTimeValue(newValue)}
                          sx={{
                            minWidth: { xs: "100%", sm: "200px" },
                            marginTop: { xs: 2, sm: 0 },
                          }}
                          minDateTime={dayjs()}
                        />
                      </Box>
                    </LocalizationProvider>
                  ) : (
                    <CustomTextField
                      id="outlined-multiline-flexible"
                      label="Type Your Query Here..."
                      variant="outlined"
                      multiline
                      fullWidth
                      sx={{
                        width: "100%",
                        background: "#ffffff",
                      }}
                      autoComplete="off"
                      InputProps={{
                        sx: { fontSize: "16px" },
                      }}
                      value={userRequestMsg}
                      onChange={(e) => setUserRequestMsg(e.target.value)}
                    />
                  )}
                </Grid>
                <Grid
                  item
                  md={2}
                  xs={2}
                  sx={{ display: "flex", justifyContent: "center" }}
                >
                  {userMsgSendLoader ? (
                    <CircularProgress size={40} sx={{ color: "#4891FF" }} />
                  ) : (
                    <SendIcon
                      sx={{
                        color: "#4891FF",
                        cursor: "pointer",
                        fontSize: "40px",
                      }}
                      onClick={() =>
                        sendAskDealerMessage(selectedCar, userRequestMsg)
                      }
                    />
                  )}
                </Grid>
              </>
            )
          ) : (
            <Typography
              variant="body1"
              sx={{ color: "red", textAlign: "center", width: "100%" }}
            >
              <b>No vehicle selected</b>
            </Typography>
          )}
        </Grid>
      </Box>
    </Box>
  );
};

export default ChatComponentWeb;
