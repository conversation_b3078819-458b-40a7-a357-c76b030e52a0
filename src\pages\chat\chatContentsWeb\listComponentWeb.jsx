import React, { useEffect, useState } from "react";
import {
  List,
  ListItem,
  Divider,
  Grid,
  Box,
  Typography,
  CircularProgress,
  Button,
} from "@mui/material";
import { ImageBaseURL } from "../../../config";
import PropTypes from "prop-types";
import { useMediaQuery } from "react-responsive";
import ChatApi from "../api/chatApi";
const ListComponentWeb = ({
  listItems,
  onListItemClick,
  listLoading,
  listLoadOnFilter,
  selectedItem,
  setSelectedItem,
  loadMoreCars,
  loadingMore,
  allCarsFetched,
  conversationCount
}) => {
  const fallbackImg = `${ImageBaseURL}fallback_image.png`;
  const isBigScreen = useMediaQuery({ query: '(min-width: 1824px)' });
  const { hasNewMessage, pusherData } = ChatApi();
  // State to store updated list of items 
  const [updatedListItems, setUpdatedListItems] = useState(listItems);

  const handleLoadMore = () => {
    if (!loadingMore && !listLoading) {
      loadMoreCars();
    }
  };

  // Effect to listen to pusher data and update message count
  useEffect(() => {
    if (hasNewMessage && pusherData) {
      const { chatId } = pusherData;
  
      setUpdatedListItems((prevItems) =>
        prevItems.map((subArray) =>
          Array.isArray(subArray)
            ? subArray.map((item) => {
                // If the current item matches the chatId
                if (item.conversation_id === chatId) {
                  // Check if selectedItem exists and matches the chatId
                  if (selectedItem && selectedItem.conversation_id === chatId) {
                    return { ...item, msg_count: 0 }; // Reset msg_count to 0
                  }
                  return { ...item, msg_count: (item.msg_count || 0) + 1 }; // Increment msg_count
                }
                return item; // Leave other items unchanged
              })
            : subArray // If it's not an array, return as is (though this shouldn't occur based on your structure)
        )
      );
    }
  }, [hasNewMessage, pusherData, selectedItem]);

  // Define content to be rendered based on conditions
  let content;

  if (loadingMore && !allCarsFetched) {
    content = (
      <Box
        sx={{ display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <CircularProgress size={30} />
        <Typography
          variant="subtitle1"
          color="#4891FF"
          sx={{ marginLeft: 2, fontWeight: "600" }}
        >
          Loading more vehicles...
        </Typography>
      </Box>
    );
  } else if (
    !loadingMore &&
    !allCarsFetched &&
    !listLoading &&
    !listLoadOnFilter &&
    conversationCount.current > 10
  ) {
    content = (
      <Button
        variant="outlined"
        onClick={handleLoadMore}
        className="map-text"
        sx={{ mb: 4 }}
        style={{
          textDecoration: "underline",
          backgroundColor: "#fff",
          border: "none",
          color: "#4891FF",
          fontWeight: "800",
        }}
      >
        Click to Load More Conversations
      </Button>
    );
  }
  
  const handleItemClick = (item) => {
    setUpdatedListItems((prevItems) =>
      prevItems.map((subArray) =>
        subArray.map((chat) => {
          if (chat.conversation_id === item.conversation_id) {
            return { ...chat, msg_count: 0 }; // Reset msg_count
          }
          return chat;
        })
      )
    );
    setSelectedItem(item);
    onListItemClick(item);
  };

  return (
    <List>
      {/* Render loading message if data is still loading */}
      {listLoading || listLoadOnFilter ? (
        <Typography variant="body2" color="textSecondary" align="center">
          Your conversations are loading...
        </Typography>
      ) : Array.isArray(listItems) && conversationCount.current > 0 ? (
        listItems.map((subArray, arrayIndex) =>
          Array.isArray(subArray) ? (
            subArray.map((item, index) => (
              <React.Fragment key={`${arrayIndex}-${index}`}>
                <ListItem button onClick={() => handleItemClick(item)}>
                  <Grid
                    container
                    spacing={2}
                    className="chat_list_shadow"
                    sx={{
                      my: 0.5,
                      padding: "15px",
                      backgroundColor:
                        selectedItem && selectedItem.conversation_id === item.conversation_id
                          ? "lightblue" // Selected item background color
                          : item.msg_count > 0
                            ? "aliceblue" // Background color for items with msg_count > 0
                            : "#ffffff", // Default background color
                      position: "relative",
                      transition: "background-color 0.3s ease",
                      alignItems: "center",
                    }}
                  >
                    {/* Image Container */}
                    <Grid item xs={4} container justifyContent="left">
                      <img
                        src={item.photo_url || fallbackImg}
                        alt="Item"
                        style={{
                          height: isBigScreen ? 100 : 80,
                          width: isBigScreen ? 120 : 100,
                          objectFit: "contain",
                        }}
                        onError={(e) => {
                          e.target.src = fallbackImg;
                        }}
                      />
                    </Grid>

                    {/* Text Container */}
                    <Grid item xs={item.msg_count > 0 ? 6 : 8} container direction="column" justifyContent="left">
                      <Typography variant="body2" fontWeight="bold">
                        {item.heading || ""}
                      </Typography>
                      <Typography variant="subtitle2" mt={1}>
                        {item.seller_name || ""}
                      </Typography>
                    </Grid>

                    {/* Count Badge */}
                    {item.msg_count > 0 && (
                      <Grid item xs={2} container alignItems="center" justifyContent="center">
                        <Box
                          sx={{
                            backgroundColor: "#4891FF",
                            borderRadius: "50%",
                            width: 30,
                            height: 30,
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            color: "white",
                            fontWeight: "600",
                          }}
                          title={item.msg_count}
                        >
                          {item.msg_count > 9 ? "9+" : item.msg_count}
                        </Box>
                      </Grid>
                    )}

                    {/* Timestamp */}
                    <Grid
                      item
                      xs={12}
                      container
                      justifyContent="flex-end"
                      sx={{
                        paddingTop: "10px",
                        textAlign: "right",
                      }}
                    >
                      <Typography variant="caption">
                        {item.last_seen_at
                          ? new Date(item.last_seen_at).toLocaleString()
                          : "No timestamp"}
                      </Typography>
                    </Grid>
                  </Grid>
                </ListItem>

                {index < subArray.length - 1 && (
                  <Divider sx={{ borderWidth: 1, borderColor: "#808080" }} />
                )}
              </React.Fragment>
            ))
          ) : (
            console.error("Expected subArray to be an array but received:", subArray)
          )
        )
      ) : (
        <Box sx={{ textAlign: "center", padding: 2 }}>
          <Typography
            variant="body1"
            gutterBottom
            sx={{ fontWeight: '500' }}
          >
            No conversation found against your search criteria
          </Typography>
        </Box>
      )}
      <Grid item xs={12} sx={{ textAlign: "center", padding: "10px" }}>
        {content}
      </Grid>
    </List>
  );
};

ListComponentWeb.propTypes = {
  listLoading: PropTypes.bool.isRequired,
  onListItemClick: PropTypes.func.isRequired,
  listItems: PropTypes.array.isRequired,
  listLoadOnFilter: PropTypes.bool.isRequired,
  selectedItem: PropTypes.object.isRequired,
  setSelectedItem: PropTypes.func.isRequired,
  loadMoreCars: PropTypes.func.isRequired,
  loadingMore: PropTypes.bool.isRequired,
  allCarsFetched: PropTypes.bool.isRequired,
  conversationCount: PropTypes.number.isRequired,
};

export default ListComponentWeb;