import { Box, Typography, CircularProgress } from '@mui/material';
import { ImageBaseURL } from '../../../config';
import PropTypes from "prop-types";

const NoConversationSelectionWeb = ({ loading }) => {
    return (
        <Box
            sx={{
                padding: 2,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                position: 'relative',
                minHeight: 300, // Ensures box height even without content
            }}
        >
            {loading ? (
                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(255, 255, 255, 0.8)', // Optional background overlay
                        zIndex: 1,
                    }}
                >
                    <CircularProgress />
                </Box>
            ) : (
                <Box
                    className="page_bg"
                    sx={{
                        textAlign: 'center',
                        width: '100%',
                    }}
                >
                    <img
                        src={`${ImageBaseURL}no_conversation.png`}
                        alt="Not Found"
                        style={{ width: '20%', height: '20%' }}
                    />
                    <Typography
                        variant="body1"
                        gutterBottom
                        sx={{ fontWeight: '500' }}
                    >
                        Click any conversation to start chat...
                    </Typography>
                </Box>
            )}
        </Box>
    );
};

NoConversationSelectionWeb.propTypes = {
  loading: PropTypes.bool.isRequired,
};
export default NoConversationSelectionWeb;
