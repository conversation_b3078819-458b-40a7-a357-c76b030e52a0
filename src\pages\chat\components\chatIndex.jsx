import { useMediaQuery } from 'react-responsive'
import ChatMobile from './chatMobile';
import ChatWeb from './chatWeb';
import MasterLayout from '../../../components/layouts/masterLayout';

function ChatIndex() {
    const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' })
    return (
        <MasterLayout>
            {isTabletOrMobile ? <ChatMobile /> : <ChatWeb />}
        </MasterLayout>
    );
}

export default ChatIndex;