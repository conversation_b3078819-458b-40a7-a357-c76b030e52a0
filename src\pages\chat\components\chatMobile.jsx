// src/pages/chat/components/chatMobile.jsx
import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import {
  Menu,
  MenuItem,
  InputAdornment,
  TextField,
  Divider,
  Stack,
  CircularProgress,
  Typography,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import CloseIcon from "@mui/icons-material/Close";
import IconButton from "@mui/material/IconButton";
import TuneIcon from "@mui/icons-material/Tune";
import SortByAlphaIcon from "@mui/icons-material/SortByAlpha";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import ListComponentMobile from "../chatContentsMobile/listComponentMobile"; // Use mobile-specific list component
import ChatApi from "../api/chatApi";
import { useNavigate } from "react-router-dom";
import NoConvFound from "../chatContentsMobile/noConversationFound";
import loaderGif from "../../../assets/gifs/loader.gif";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { callComponentActionApi } from "../../../util/callComponenetActionApi";

const labelStyle = {
  color: "#4891FF",
};

const CustomTextField = styled(TextField)(() => ({
  "& .MuiInputLabel-root": labelStyle,
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "semibold",
      "&::placeholder": {
        color: "black",
        opacity: 1,
      },
    },
    height: "3rem",
    padding: "8px 14px",
  },
}));

const ChatMobile = () => {
  const {
    listItems,
    searchText,
    setSearchText,
    anchorEl,
    isMenuOpen,
    handleClearSearch,
    handleTuneIconClick,
    handleMenuClose,
    listLoading,
    handleSortAlphabetically,
    handleSortByDate,
    listLoadOnFilter,
    noVehiclesFound,
    loadingMore,
    allCarsFetched,
    loadMoreCars,
    conversationCount,
    showNoConvFound,
    componentMetaData
  } = ChatApi();

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const handleListItemClick = async (selectedVehicleDetails) => {
    const conversationId = selectedVehicleDetails.conversation_id;

    // Local validation for `conversation_id` (e.g., check if it's defined and matches expected format)
    if (
      !conversationId ||
      typeof conversationId !== "string" ||
      !/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(
        conversationId
      )
    ) {
      console.error("Invalid conversation ID: not a valid UUID");
      return;
    }

    // Navigate to the chat details route with `conversation_id` as a URL parameter
    navigate(`/conversation/${conversationId}`);
  };

  useEffect(() => {
        let isMounted = true; // Add a flag to check if the component is still mounted
      
        if (!listLoading && componentMetaData && componentMetaData.length > 0 && isMounted) {
          try {
            const component = componentMetaData.find((comp) => comp.slug === "conversations" && comp.type === "url");
            if (!component) {
              console.error(`Component with slug "conversations" not found.`);
              return;
            }
            // Call the API with the component ID
            callComponentActionApi(dispatch, component.id);
          } catch (error) {
            console.error("Error handling button click:", error);
          }
        }
      
        // Cleanup function to reset the flag when the component is unmounted
        return () => {
          isMounted = false;
        };
      }, [listLoading, componentMetaData]);

  if (listLoading) {
    return (
      <Stack
        alignItems="center"
        justifyContent="center"
        sx={{ height: "100vh" }}
      >
        <img
          className="page_bg"
          src={loaderGif}
          alt="Loading..."
          style={{ width: "200px", height: "200px" }}
        />
        <span style={{ fontWeight: "600" }}>
          Loading Your Conversations...
        </span>
      </Stack>
    );
  }

  return showNoConvFound ? (
    <NoConvFound />
  ) : (
    <Box
      sx={{
        flexGrow: 1,
        mt: 8,
        display: "flex",
        height: "100%",
        flexDirection: "column",
        padding: "10px",
      }}
    >
      {/* Search bar and Tune Icon */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          mb: 1,
          backgroundColor: "#fff",
        }}
      >
        {/* Search bar */}
        <CustomTextField
          fullWidth
          id="outlined-basic"
          placeholder="Search..."
          variant="outlined"
          autoComplete="off"
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <SearchIcon sx={{ color: "#4891FF" }} />
                  <Divider
                    sx={{ height: 25, m: 1, borderColor: "#4891FF" }}
                    orientation="vertical"
                  />
                </Box>
              </InputAdornment>
            ),
            endAdornment: (
              <>
                {searchText && (
                  <InputAdornment position="end">
                    <IconButton onClick={handleClearSearch}>
                      <CloseIcon sx={{ color: "#4891FF", cursor: "pointer" }} />
                    </IconButton>
                  </InputAdornment>
                )}
              </>
            ),
            sx: {
              fontSize: "16px",
            },
          }}
          InputLabelProps={{
            sx: {
              fontSize: "16px",
            },
          }}
        />

        {/* Tune Icon beside the search bar */}
        <IconButton onClick={handleTuneIconClick} sx={{ ml: 1 }}>
          <TuneIcon sx={{ color: "#4891FF" }} />
        </IconButton>
      </Box>

      {/* Dropdown Menu triggered by TuneIcon */}
      <Menu
        anchorEl={anchorEl}
        open={isMenuOpen}
        onClose={handleMenuClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
        transformOrigin={{ vertical: "top", horizontal: "left" }}
      >
        <MenuItem onClick={handleSortAlphabetically}>
          <SortByAlphaIcon sx={{ mr: 1, color: "#4891FF" }} />
          Sort alphabetically
        </MenuItem>
        <MenuItem onClick={handleSortByDate}>
          <CalendarMonthIcon sx={{ mr: 1, color: "#4891FF" }} />
          Sort by oldest or newest
        </MenuItem>
      </Menu>

      {/* List with scrollable behavior */}
      <Box
        sx={{
          maxHeight: "calc(100vh - 250px)", // Adjust the height as needed
          overflowY: "auto", // Enables vertical scrolling
          scrollbarWidth: "thin", // For Firefox
          padding: "0 10px",
          "&::-webkit-scrollbar": {
            width: "8px", // Scrollbar width
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "#4891FF", // Scrollbar color
            borderRadius: "4px",
          },
        }}
      >
        {!listLoading ? (
          <>
            {/* Render No vehicles found if no results are available */}
            {noVehiclesFound ? (
              <Box sx={{ textAlign: "center", padding: 2 }}>
                <Typography
                  variant="body1"
                  gutterBottom
                  sx={{ fontWeight: '500' }}
                >
                  No conversation found against your search criteria
                </Typography>
              </Box>
            ) : (
              <ListComponentMobile
                listItems={listItems}
                onListItemClick={handleListItemClick}
                listLoading={listLoading}
                listLoadOnFilter={listLoadOnFilter}
                loadingMore={loadingMore}
                allCarsFetched={allCarsFetched}
                loadMoreCars={loadMoreCars} // Pass the loadMoreCars function
                conversationCount={conversationCount}
              />
            )}
          </>
        ) : (
          <CircularProgress size={60} sx={{ color: "#4891FF" }} />
        )}
      </Box>
    </Box>
  );
};

export default ChatMobile;