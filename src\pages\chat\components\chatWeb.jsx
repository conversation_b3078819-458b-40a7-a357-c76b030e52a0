import { useEffect, useState } from "react";
import {
  Box,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  Grid,
  Paper,
  TextField,
  InputAdornment,
  Stack,
  Typography,
  CircularProgress
} from "@mui/material";
import loaderGif from "../../../assets/gifs/loader.gif";
import { styled } from "@mui/material/styles";
import SearchIcon from "@mui/icons-material/Search";
import CloseIcon from "@mui/icons-material/Close";
import TuneIcon from "@mui/icons-material/Tune";
import SortByAlphaIcon from "@mui/icons-material/SortByAlpha";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import ListComponentWeb from "../chatContentsWeb/listComponentWeb";
import ChatComponentWeb from "../chatContentsWeb/chatComponentWeb";
import NoConversationSelectionWeb from "../chatContentsWeb/noConversationSelectionWeb";
import ChatApi from "../api/chatApi";
import { toast } from "react-toastify";
import NoConvFound from "../chatContentsWeb/noConversationFound";
import { useDispatch } from "react-redux";
import { callComponentActionApi } from "../../../util/callComponenetActionApi";
// Styled components
const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const labelStyle = { color: "#4891FF" };

const CustomTextField = styled(TextField)(() => ({
  "& .MuiInputLabel-root": labelStyle,
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": { borderColor: "#4891FF" },
    "&.Mui-focused fieldset": { borderColor: "#4891FF" },
    "& input": {
      fontWeight: "semibold",
      "&::placeholder": { color: "black", opacity: 1 },
    },
    height: "3rem",
    padding: "8px 14px",
  },
}));

const ChatWeb = () => {
  const {
    listItems,
    searchText,
    setSearchText,
    anchorEl,
    isMenuOpen,
    handleClearSearch,
    handleTuneIconClick,
    handleMenuClose,
    fetchDealerResponseData,
    formatTimestamp,
    listLoading,
    loadingMore,
    allCarsFetched,
    handleSortAlphabetically,
    handleSortByDate,
    listLoadOnFilter,
    showChatComponent,
    setShowChatComponent,
    setListItems,
    selectedItem,
    setSelectedItem,
    noVehiclesFound,
    loadMoreCars,
    conversationCount,
    showNoConvFound,
    componentMetaData
  } = ChatApi();

  const [selectedVehicleDetails, setSelectedVehicleDetails] = useState(null); // Holds the selected vehicle details
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(false); // Initialize loading state
  const dispatch = useDispatch();

  // Function to fetch and parse chat messages from the dealer response
  const fetchChatMessages = async (selectedVehicleDetails) => {
    try {
      // Fetch the response
      const resp = await fetchDealerResponseData(selectedVehicleDetails);
  
      if (resp && resp.SalesForceResponse) {
        try {
          // Attempt to parse the JSON
          const parsedResponse = JSON.parse(resp.SalesForceResponse);
          // Check for errors in the response
          if (
            parsedResponse[0] &&
            parsedResponse[0].error &&
            parsedResponse[0].error.Message
          ) {
            // Show the error message in a toast and stop the loader
            toast.error(parsedResponse[0].error.Message);
            return null; // Return null to indicate an error
          }
  
          if (parsedResponse[0] && parsedResponse[0].success.data) {
            const chatMessages = parsedResponse[0].success.data.map(
              (msg, index) => ({
                id: index + 1,
                text: msg.EmailMessage,
                fromDealer: msg.Sender === "Dealer",
                timestamp: new Date(msg.CreatedDate), // Keep as Date object for sorting
              })
            );
  
            // Sort messages by timestamp (includes both date and time)
            const sortedMessages = chatMessages.sort(
              (a, b) => a.timestamp - b.timestamp
            );
  
            // Format timestamps for display as strings
            const formattedMessages = sortedMessages.map((msg) => ({
              ...msg,
              timestamp: msg.timestamp.toLocaleString(), // Format date to a readable string
            }));
  
            return formattedMessages; // Return the formatted messages
          }
        } catch (parseError) {
          // Handle JSON parsing error
          console.error(`JSON Parsing Error: ${parseError}. Response: ${resp.SalesForceResponse}`);
          toast.error("Failed to load conversation. Please try again.");
          return null; // Return null to indicate parsing failure
        }
      } else {
        console.error("Empty or invalid response from server:", resp.SalesForceResponse);
        toast.error("No data received from the server. Please try again.");
      }
  
      return null; // Return null if no valid messages
    } catch (error) {
      // Catch AxiosError or other errors
      if (error.response && error.response.status === 401) {
        // Handle 401 Unauthorized
        console.error("Unauthorized request:", error.message);
        toast.error("Your session has expired. Please log in again.");
      } else if (error.response) {
        // Handle other HTTP errors
        console.error("HTTP Error:", error.message);
        toast.error(`Error: ${error.response.statusText} (${error.response.status})`);
      } else {
        // Handle other errors (e.g., network issues)
        console.error("Error:", error.message);
        toast.error("An unexpected error occurred. Please try again later.");
      }
      return null; // Return null to indicate an error occurred
    }
  };

  // Updated handleListItemClick function to use fetchChatMessages
  const handleListItemClick = async (selectedVehicleDetails) => {
    setSelectedVehicleDetails(selectedVehicleDetails);
    setLoading(true);

    const formattedMessages = await fetchChatMessages(selectedVehicleDetails);

    if (formattedMessages) {
      setMessages(formattedMessages);
    }

    setLoading(false); // Stop the loading spinner
    setShowChatComponent(true); // Set to true to show ChatComponentWeb
  };

  const updateMessageCount = (item) => {
    setListItems((prevListItems) =>
      prevListItems.map((subArray) =>
        subArray.map((listItem) =>
          listItem.conversation_id === item.conversation_id
            ? { ...listItem, msg_count: 0 }
            : listItem
        )
      )
    );
  };

  useEffect(() => {
      let isMounted = true; // Add a flag to check if the component is still mounted
    
      if (!listLoading && componentMetaData && componentMetaData.length > 0 && isMounted) {
        try {
          const component = componentMetaData.find((comp) => comp.slug === "conversations" && comp.type === "url");
          if (!component) {
            console.error(`Component with slug "conversations" not found.`);
            return;
          }
          // Call the API with the component ID
          callComponentActionApi(dispatch, component.id);
        } catch (error) {
          console.error("Error handling button click:", error);
        }
      }
    
      // Cleanup function to reset the flag when the component is unmounted
      return () => {
        isMounted = false;
      };
    }, [listLoading, componentMetaData]);

  if (listLoading) {
    return (
      <Stack
        alignItems="center"
        justifyContent="center"
        sx={{ height: "100vh" }}
      >
        <img
          className="page_bg"
          src={loaderGif}
          alt="Loading..."
          style={{ width: "200px", height: "200px" }}
        />
        <span style={{ fontWeight: "600" }}>
          Loading Your Conversations...
        </span>
      </Stack>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, mt: 12 }}>
      {listLoading && <CircularProgress size={60} sx={{ color: "#4891FF" }} />}
      {showNoConvFound ? (
        <NoConvFound /> // Show NoConvFound only after the delay
      ) : (
        <Grid container spacing={2} sx={{ height: "calc(100vh - 150px)" }}>
          <Grid item md={4} sx={{ borderRight: "1px solid #4891FF" }}>
            <Item
              className="page_bg"
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              {/* Search bar */}
              <CustomTextField
                fullWidth
                id="outlined-basic"
                placeholder="Search using vehicle or dealer name..."
                variant="outlined"
                autoComplete="off"
                value={searchText}
                sx={{ backgroundColor: "#fff" }}
                onChange={(e) => setSearchText(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <SearchIcon sx={{ color: "#4891FF" }} />
                        <Divider sx={{ height: 25, m: 1, borderColor: "#4891FF" }} orientation="vertical" />
                      </Box>
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <>
                      {searchText && (
                        <InputAdornment position="end">
                          <IconButton onClick={handleClearSearch}>
                            <CloseIcon sx={{ color: "#4891FF", cursor: "pointer" }} />
                          </IconButton>
                        </InputAdornment>
                      )}
                    </>
                  ),
                  sx: { fontSize: "16px" },
                }}
                InputLabelProps={{ sx: { fontSize: "16px" } }}
              />

              {/* Tune Icon beside the search bar */}
              <IconButton onClick={handleTuneIconClick} sx={{ ml: 1 }}>
                <TuneIcon sx={{ color: "#4891FF" }} />
              </IconButton>

              {/* Dropdown Menu triggered by TuneIcon */}
              <Menu
                anchorEl={anchorEl}
                open={isMenuOpen}
                onClose={handleMenuClose}
                anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
                transformOrigin={{ vertical: "top", horizontal: "left" }}
              >
                <MenuItem onClick={handleSortAlphabetically}>
                  <SortByAlphaIcon sx={{ mr: 1, color: "#4891FF" }} />
                  Sort alphabetically
                </MenuItem>
                <MenuItem onClick={handleSortByDate}>
                  <CalendarMonthIcon sx={{ mr: 1, color: "#4891FF" }} />
                  Sort by oldest or newest
                </MenuItem>
              </Menu>
            </Item>
            <Item className="page_bg">
              {/* List with scrollable behavior */}
              <Box
                sx={{
                  maxHeight: "calc(100vh - 250px)", // Adjust the height as needed
                  overflowY: "auto", // Enables vertical scrolling
                  scrollbarWidth: "thin", // For Firefox
                  padding: "0 10px",
                  "&::-webkit-scrollbar": {
                    width: "8px", // Scrollbar width
                  },
                  "&::-webkit-scrollbar-thumb": {
                    backgroundColor: "#4891FF", // Scrollbar color
                    borderRadius: "4px",
                  },
                }}
              >
                {!listLoading ? (
                  <>
                    {/* Render No vehicles found if no results are available */}
                    {noVehiclesFound ? (
                      <Box sx={{ textAlign: "center", padding: 2 }}>
                        <Typography
                          variant="body1"
                          gutterBottom
                          sx={{ fontWeight: '500' }}
                        >
                          No conversation found against your search criteria
                        </Typography>
                      </Box>
                    ) : (
                      <ListComponentWeb
                        onListItemClick={(item) => {
                          handleListItemClick(item);
                          updateMessageCount(item); // Reset count when an item is clicked
                        }}
                        loadingMore={loadingMore}
                        allCarsFetched={allCarsFetched}
                        listItems={listItems} // Pass the listItems as a prop if needed
                        listLoadOnFilter={listLoadOnFilter}
                        selectedItem={selectedItem}
                        setSelectedItem={setSelectedItem}
                        loadMoreCars={loadMoreCars} // Pass the loadMoreCars function
                        conversationCount={conversationCount}
                      />
                    )}
                  </>
                ) : (
                  <CircularProgress size={60} sx={{ color: "#4891FF" }} />
                )}
              </Box>
            </Item>
          </Grid>

          {/* right side */}
          <Grid item md={8}>
            <Item className="page_bg" sx={{ height: "calc(100vh - 200px)" }}>
              {showChatComponent ? (
                <ChatComponentWeb
                  selectedCar={selectedVehicleDetails}
                  sfmessages={messages}
                  formatTimestamp={formatTimestamp}
                  loading={loading}
                  refreshMessages={fetchChatMessages} // Pass the function to refresh messages
                  setListItems={setListItems} // Pass the function here
                />
              ) : (
                <NoConversationSelectionWeb loading={loading} />
              )}
            </Item>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default ChatWeb;