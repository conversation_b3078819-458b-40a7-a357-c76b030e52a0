import { useMediaQuery } from 'react-responsive';
import AccessDeniedMobile from './accessDeniedMobile';
import AccessDeniedWeb from './accessDeniedWeb';
import MasterLayoutWithoutFrames from '../../../components/layouts/masterLayoutWithoutFrames';


const AccessDeniedIndex = () => {
    const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' });

    return (
        <MasterLayoutWithoutFrames>
            {isTabletOrMobile ? <AccessDeniedMobile /> : <AccessDeniedWeb />}
        </MasterLayoutWithoutFrames>
    );
}

export default AccessDeniedIndex;