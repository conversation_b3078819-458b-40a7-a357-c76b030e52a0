import { useNavigate } from 'react-router-dom';
import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import HomeIcon from '@mui/icons-material/Home';
import { ImageBaseURL } from '../../../config'; // Adjust the import based on your config



function AccessDeniedWeb() {
    const navigate = useNavigate();

    const Item = styled(Paper)(({ theme }) => ({
        ...theme.typography.body2,
        padding: theme.spacing(1),
        textAlign: "center",
        borderRadius: "none",
        boxShadow: "none",
    }));

    const StyledButton = styled(Button)({
        borderRadius: "10px",
        width: "15%",
        fontWeight: "600",
        padding: "15px",
        backgroundColor: "#4891FF",
        "&:hover": {
            backgroundColor: "primary",
        },
    });

/**
 * Handles the "Confirm Logout" dialog confirmation button click event.
 *
 * Tries to call the logout API using the component ID found in componentMetaDataVal.
 * If the API call is successful, clears localStorage and redirects to the login page.
 * If the API call fails, does not clear localStorage or redirect.
 *
 * @param {void} Nothing.
 * @return {Promise<void>} Nothing.
 */
  const handleGoHome = async () => {
    try {

      localStorage.clear();
      navigate("/send-otp", { replace: true });
      location.reload();
    } catch (error) {
      console.error("Error handling button click:", error);
      // If API call fails, do NOT clear localStorage or redirect
    }
  };

    return (
        <Box sx={{
            flexGrow: 1,
            height: "100vh",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
        }}>
            <Grid container spacing={2} sx={{ padding: "30px 0px" }}>
                <Grid item xs={12}>
                    <Item className='page_bg'>
                        <img
                            src={ImageBaseURL + "access_denied.png"} // Ensure you have an appropriate image
                            alt="Access Denied"
                            style={{ maxWidth: "25%", maxHeight: "25%" }}
                        />
                    </Item>
                </Grid>
                <Grid item xs={12}>
                    <Item className='page_bg'>
                        <Typography
                            variant="subtitle2"
                            gutterBottom
                            sx={{ fontWeight: "800" }}
                        >
                            Access Denied
                        </Typography>
                    </Item>
                </Grid>
                <Grid item xs={12}>
                    <Item className='page_bg'>
                        <StyledButton
                            variant="contained"
                            onClick={handleGoHome}
                        >
                            <HomeIcon sx={{ marginRight: "10px" }} /> GO HOME
                        </StyledButton>
                    </Item>
                </Grid>
            </Grid>
        </Box>
    );
}

export default AccessDeniedWeb; 