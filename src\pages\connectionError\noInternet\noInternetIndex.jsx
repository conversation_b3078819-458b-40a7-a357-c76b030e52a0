import { useMediaQuery } from 'react-responsive'
import NoInternetMobile from './noInternetMobile.';
import NoInternetWeb from './noInternetWeb';
import MasterLayoutWithoutFrames from '../../../components/layouts/masterLayoutWithoutFrames';

function NoInternet() {
  const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' })
  return (
    <MasterLayoutWithoutFrames>
      {isTabletOrMobile ? <NoInternetMobile /> : <NoInternetWeb />}
    </MasterLayoutWithoutFrames>
  );
}

export default NoInternet;