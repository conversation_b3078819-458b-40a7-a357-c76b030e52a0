import { useState, useEffect } from 'react';
import { useMediaQuery } from 'react-responsive'
import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import RefreshIcon from '@mui/icons-material/Refresh';
import Tooltip from '@mui/material/Tooltip';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Zoom from '@mui/material/Zoom';
import NoInternetImgBase64 from '../../../../base64NoInternerImage';

function NoInternetMobile() {
    const [isOnline, setIsOnline] = useState(navigator.onLine);
    const [tooltipOpen, setTooltipOpen] = useState(false);
    const handleTooltipClose = () => {
        setTooltipOpen(false);
    };

    useEffect(() => {
        const handleOnline = () => {
            setIsOnline(true);
        };

        const handleOffline = () => {
            setIsOnline(false);
        };

        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);

        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        };
    }, []);

    const Item = styled(Paper)(({ theme }) => ({
        ...theme.typography.body2,
        padding: theme.spacing(1),
        textAlign: "center",
        borderRadius: "none",
        boxShadow: "none",
    }));

    const StyledButton = styled(({ ...other }) => <Button {...other} />)(({ isMobile }) => ({
        borderRadius: "10px",
        width: isMobile ? "50%" : "30%",
        fontSize: isMobile ? "14px" : "18px",
        fontWeight: "600",
        padding: "15px",
        backgroundColor: "#4891FF",
        "&:hover": {
            backgroundColor: "primary",
        },
    }));

    const handleReload = () => {
        if (!isOnline) {
            setTooltipOpen(true);
            setTimeout(() => {
                setTooltipOpen(false);
            }, 3000); // Hide tooltip after 3 seconds
        } else {
            window.location.reload();
        }
    };
    const isMobile = useMediaQuery({ maxWidth: 767 });
    return (
        <Box sx={{
            flexGrow: 1,
            height: "100vh",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
        }} >
            <Grid container spacing={2} sx={{ padding: "15px 0px" }}>
                <Grid item xs={12}>
                    <Item className='page_bg'>
                        <img
                            src={NoInternetImgBase64}
                            alt="no internet"
                            style={isMobile ? { maxWidth: "60%", maxHeight: "60%" } : { maxWidth: "50%", maxHeight: "50%" }}
                        />
                    </Item>
                </Grid>
                <Grid item xs={12}>
                    <Item className='page_bg'>
                        <Typography
                            variant="subtitle2"
                            gutterBottom
                            sx={{ fontWeight: "800" }}
                            style={isMobile ? { fontSize: "14px" } : { fontSize: "18px" }}
                        >
                            No Internet Connection Found
                        </Typography>
                        <Typography
                            variant="caption"
                            gutterBottom
                            sx={{ fontWeight: "800" }}
                            style={isMobile ? { fontSize: "14px" } : { fontSize: "18px" }}
                        >
                            Please Check Your Internet
                        </Typography>
                    </Item>
                </Grid>
                <Grid item xs={12}>
                    <Item className='page_bg'>
                        <ClickAwayListener onClickAway={handleTooltipClose}>
                            <div>
                                <Tooltip
                                    PopperProps={{
                                        disablePortal: true,
                                    }}
                                    open={tooltipOpen}
                                    disableFocusListener
                                    disableHoverListener
                                    disableTouchListener
                                    title="check your internet connection & then try again"
                                    TransitionComponent={Zoom}
                                    arrow
                                >
                                    <StyledButton
                                        isMobile={isMobile}
                                        variant="contained"
                                        onClick={handleReload}
                                    >
                                        <RefreshIcon sx={{ marginRight: "20px" }} /> TRY AGAIN
                                    </StyledButton>
                                </Tooltip>
                            </div>
                        </ClickAwayListener>
                    </Item>
                </Grid>
            </Grid>
        </Box>
    );
}

export default NoInternetMobile;