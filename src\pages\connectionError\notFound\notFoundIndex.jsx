import { useMediaQuery } from 'react-responsive'
import NotFoundMobile from './notFoundMobile';
import NotFoundWeb from './notFoundWeb';
import MasterLayoutWithoutFrames from '../../../components/layouts/masterLayoutWithoutFrames';

function NoInternet() {
    const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' })
    return (
        <MasterLayoutWithoutFrames>
            {isTabletOrMobile ? <NotFoundMobile /> : <NotFoundWeb />}
        </MasterLayoutWithoutFrames>
    );
}

export default NoInternet;