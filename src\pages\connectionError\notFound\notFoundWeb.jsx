import { useNavigate } from 'react-router-dom';
import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import HomeIcon from '@mui/icons-material/Home';
import {ImageBaseURL} from '../../../config';

function NoInternetMobile() {
    const navigate = useNavigate();
    const Item = styled(Paper)(({ theme }) => ({
        ...theme.typography.body2,
        padding: theme.spacing(1),
        textAlign: "center",
        borderRadius: "none",
        boxShadow: "none",
    }));

    const StyledButton = styled(Button)({
        borderRadius: "10px",
        width: "15%",
        fontWeight: "600",
        padding: "15px",
        backgroundColor: "#4891FF",
        "&:hover": {
            backgroundColor: "primary",
        },
    });

    const token = localStorage.getItem('token');
    const handleReload = () => {
        if (token) {
            navigate('/landing');
        } else {
            navigate('/');
        }
    };

    return (
        <Box sx={{
            flexGrow: 1,
            height: "100vh",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
        }} >
            <Grid container spacing={2} sx={{ padding: "30px 0px" }}>
                <Grid item xs={12}>
                    <Item className='page_bg'>
                        <img
                            src={ImageBaseURL+"not_found.png"}
                            alt="Not Found"
                            style={{ maxWidth: "25%", maxHeight: "25%" }}
                        />
                    </Item>
                </Grid>
                <Grid item xs={12}>
                    <Item className='page_bg'>
                        <Typography
                            variant="subtitle2"
                            gutterBottom
                            sx={{ fontWeight: "800" }}
                        >
                            No Such Page Found
                        </Typography>
                    </Item>
                </Grid>
                <Grid item xs={12}>
                    <Item className='page_bg'>
                        <StyledButton
                            variant="contained"
                            onClick={handleReload}
                        >
                            <HomeIcon sx={{ marginRight: "10px" }} /> GO HOME
                        </StyledButton>
                    </Item>
                </Grid>
            </Grid>
        </Box>
    );
}

export default NoInternetMobile;