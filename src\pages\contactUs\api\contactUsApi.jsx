import { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { getUser } from "../../../store/apps/user";
import { useNavigate } from "react-router-dom";
import { contactUsEmail } from "../../../store/apps/car/index";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "bootstrap/dist/css/bootstrap.min.css";
import "bootstrap-icons/font/bootstrap-icons.css";

const ContactUsApi = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [name, setName] = useState("");
  const [userEmail, setUserEmail] = useState("");
  const [message, setMessage] = useState("");
  const [loan_id, setLoan_id] = useState(0);
  const [from_credit, setFrom_credit] = useState("");
  const [loading, setLoading] = useState(true);
  const [emailError, setEmailError] = useState("");
  const [nameError, setNameError] = useState("");

  const validateName = (name) => {
    const namePattern = /^[a-zA-Z]+(?: [a-zA-Z]+)* ?$/;
    return namePattern.test(name);
  };

  const handleNameChange = (event) => {
    let value = event.target.value;
    value = value.replace(/\s+/g, " ");

    const hasTrailingSpace = value.endsWith(" ");

    if (value.trim() === "") {
      setNameError("Name cannot be empty");
    } else if (hasTrailingSpace) {
      setNameError("Invalid name format");
    } else if (validateName(value)) {
      setNameError("");
    } else {
      value = value.replace(/[^a-zA-Z ]/g, "");
    }
    setName(value);
  };

  const validateEmail = (email) => {
    const emailPattern =
      /^[a-zA-Z0-9]+(?:\.[a-zA-Z0-9]+)*@[a-zA-Z0-9]+(?:\.[a-zA-Z0-9]+)*\.[a-zA-Z]{2,}$/;
    return emailPattern.test(email);
  };

  const handleEmailChange = (event) => {
    let value = event.target.value;

    value = value.replace(/[^a-zA-Z0-9.@]/g, "");
    value = value.replace(/\.{2,}/g, ".");

    const atSymbolCount = (value.match(/@/g) || []).length;
    if (atSymbolCount > 1) {
      const firstAtIndex = value.indexOf("@");
      value =
        value.slice(0, firstAtIndex + 1) +
        value.slice(firstAtIndex + 1).replace(/@/g, "");
    }

    if (!validateEmail(value)) {
      setEmailError("Invalid email address");
    } else {
      setEmailError("");
    }

    setUserEmail(value);
  };

  const handleMessageChange = (event) => {
    const value = event.target.value;
    setMessage(value);
  };

  const clearEmail = () => {
    setUserEmail("");
  };

  const clearName = () => {
    setName("");
  };

  useEffect(() => {
    const fetchUserData = async () => {
      const number = localStorage.getItem("ph_number");
      const token = localStorage.getItem("token");
      if (!number || !token) {
        navigate("/");
        return;
      }

      try {
        dispatch(getUser({ phone_number: number, authToken: token })).then(
          (response) => {
            if (
              response &&
              response.meta &&
              response.meta.requestStatus === "fulfilled"
            ) {
              const userData = response.payload;
              setName(
                userData.member_first_name.trim() +
                  " " +
                  userData.member_last_name.trim()
              );
              setUserEmail(userData.member_email);
              setLoan_id(userData.loan_id);
              setFrom_credit(userData.lender_nm);
              setLoading(false);
            } else {
              handleFailedResponse();
              setLoading(false);
            }
          }
        );
      } catch (error) {
        console.error("Error fetching user data:", error);
        setLoading(false);
        handleFailedResponse();
      }
    };

    const handleFailedResponse = () => {
      localStorage.removeItem("ph_number");
      localStorage.removeItem("verified");
      navigate("/");
    };

    fetchUserData();
  }, [dispatch]);

  const onSubmit = () => {
    if (nameError.length || emailError.length) return;
    const mail_body = message;
    const email = userEmail;
    if (!mail_body.length || !name.length || !email.length) {
      toast.warning("All the fields are mandatory", {
        className: "bg-warning text-white fw-800",
        bodyClassName: "text-white",
        icon: <i className="bi bi-exclamation-circle"></i>,
      });
      return;
    }
    try {
      const token = localStorage.getItem("token");
      dispatch(
        contactUsEmail({ name, email, from_credit, loan_id, mail_body, token })
      );
      toast.success("Message sent successfully", {
        className: "bg-success text-white fw-800",
        bodyClassName: "text-white",
        icon: <i className="bi bi-check-circle"></i>,
      });
      setMessage("");
    } catch (error) {
      console.error("Error Sending Mail:", error);
      toast.error("Error sending mail. Please try again later.");
    }
  };

  return {
    name,
    userEmail,
    message,
    nameError,
    emailError,
    loading,
    handleNameChange,
    handleEmailChange,
    handleMessageChange,
    clearEmail,
    clearName,
    onSubmit,
  };
};

export default ContactUsApi;
