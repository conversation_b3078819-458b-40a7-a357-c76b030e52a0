import { useMediaQuery } from "react-responsive";
import ContactUsMobile from "./contactUsMobile";
import ContactUsWeb from "./contactUsWeb";
import MasterLayout from "../../../components/layouts/masterLayout";

function ContactUsIndex() {
  const isTabletOrMobile = useMediaQuery({ query: "(max-width: 1224px)" });
  return (
    <MasterLayout>
      {isTabletOrMobile ? <ContactUsMobile /> : <ContactUsWeb />}
    </MasterLayout>
  );
}

export default ContactUsIndex;
