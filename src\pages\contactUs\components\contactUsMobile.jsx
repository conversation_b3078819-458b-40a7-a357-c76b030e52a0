import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import { useMediaQuery } from "react-responsive";
import { ImageBaseURL } from "../../../config.js";
import CloseIcon from "@mui/icons-material/Close";
import ContactUsApi from "../api/contactUsApi";
import InputAdornment from "@mui/material/InputAdornment";
import CircularProgress from "@mui/material/CircularProgress";

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const labelStyle = {
  color: "#4891FF",
};

const CustomTextField = styled(TextField)({
  "& .MuiInputLabel-root": labelStyle,
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-disabled": {
      "& fieldset": {
        borderColor: "#4891FF", // Border color for disabled state
      },
      "& input": {
        color: "#4891FF", // Text color for disabled state
      },
    },
  },
});

const StyledButton = styled(({ ...other }) => <Button {...other} />)(
  ({ isMobile }) => ({
    borderRadius: "10px",
    width: isMobile ? "50%" : "25%",
    fontSize: "15px",
    fontWeight: "600",
    padding: "15px",
    backgroundColor: "#4891FF",
    // Hover state
    "&:hover": {
      backgroundColor: "#357AE8",
    },

    // Disabled state
    "&.Mui-disabled": {
      backgroundColor: "#4891FF",
      color: "#fff",
      opacity: 0.6,
      cursor: "not-allowed",
    },
  })
);

const ContactUsMobile = () => {
  const isMobile = useMediaQuery({ maxWidth: 767 });
  const {
    name,
    userEmail,
    message,
    nameError,
    emailError,
    loading,
    handleNameChange,
    handleEmailChange,
    handleMessageChange,
    clearEmail,
    clearName,
    onSubmit,
  } = ContactUsApi();

  const handleEmail = () => {
    window.location.href = `mailto:${import.meta.env.VITE_DEALER_HELP_EMAIL}`;
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Grid
        container
        spacing={2}
        sx={{ padding: "70px 10px" }}
        mt={!isMobile && 4}
      >
        <Grid item xs={12}>
          <Item className="page_bg">
            {" "}
            <Typography
              variant={isMobile ? "subtitle1" : "h5"}
              component={isMobile ? "subtitle1" : "h5"}
            >
              <b>CONTACT US</b>
            </Typography>
          </Item>
        </Grid>
        <Grid item xs={12} style={{ paddingTop: "0px" }} mt={!isMobile && 4}>
          <Item className="page_bg">
            <img
              src={ImageBaseURL + "contact_us.png"}
              alt="Contact us"
              style={{ maxWidth: "60%", maxHeight: "30%" }}
            />
          </Item>
        </Grid>
        <Grid item xs={12} style={{ paddingTop: "0px" }}>
          <Item>
            <CustomTextField
              id="outlined-basic"
              label="Your Contact Email"
              variant="outlined"
              value={userEmail}
              sx={{ width: isMobile ? "95%" : "55%" }}
              onChange={handleEmailChange}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    {loading && (
                      <CircularProgress sx={{ color: "#4891FF" }} size={20} />
                    )}
                    {userEmail !== "" && !loading && (
                      <CloseIcon
                        onClick={clearEmail}
                        style={{ cursor: "pointer" }}
                      />
                    )}
                  </InputAdornment>
                ),
              }}
            />
            {emailError && (
              <span
                style={{
                  display: "flex",
                  justifyContent: "center",
                  color: "red",
                }}
              >
                {emailError}
              </span>
            )}
          </Item>
          <Item>
            <CustomTextField
              id="outlined-basic"
              label="Your Name"
              variant="outlined"
              value={name}
              sx={{ width: isMobile ? "95%" : "55%" }}
              onChange={handleNameChange}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    {loading && (
                      <CircularProgress sx={{ color: "#4891FF" }} size={20} />
                    )}
                    {name !== "" && !loading && (
                      <CloseIcon
                        onClick={clearName}
                        style={{ cursor: "pointer" }}
                      />
                    )}
                  </InputAdornment>
                ),
              }}
            />
            {nameError && (
              <span
                style={{
                  display: "flex",
                  justifyContent: "center",
                  color: "red",
                }}
              >
                {nameError}
              </span>
            )}
          </Item>
          <Item className="page_bg">
            <CustomTextField
              id="outlined-multiline-flexible"
              label="Type Your Query Here..."
              variant="outlined"
              onChange={handleMessageChange}
              value={message}
              multiline
              fullWidth
              sx={{
                width: isMobile ? "95%" : "55%",
                background: "#ffffff",
                "& .MuiOutlinedInput-root": {
                  minHeight: isMobile ? "150px" : "200px",
                  alignItems: "start",
                },
              }}
              InputProps={{
                sx: {
                  fontSize: "16px",
                },
              }}
            />
          </Item>
        </Grid>
        <Grid item xs={12} style={{ paddingTop: "0px" }}>
          <Item className="page_bg">
            <StyledButton
              variant="contained"
              isMobile={isMobile}
              disabled={nameError.length || emailError.length}
              onClick={() => {
                onSubmit();
              }}
            >
              SUBMIT
            </StyledButton>
          </Item>
          <Item className="page_bg">
            <Typography>
              <b>
                Your response will be sent to{" "}
                <span
                  style={{ color: "#4891FF", cursor: "pointer" }}
                  onClick={handleEmail}
                >
                  <u>{import.meta.env.VITE_DEALER_HELP_EMAIL} </u>
                </span>
              </b>
            </Typography>
          </Item>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ContactUsMobile;
