import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import { useMediaQuery } from "react-responsive";
import CloseIcon from "@mui/icons-material/Close";
import { ImageBaseURL } from "../../../config.js";
import ContactUsApi from "../api/contactUsApi";
import InputAdornment from "@mui/material/InputAdornment";
import CircularProgress from "@mui/material/CircularProgress";

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const labelStyle = {
  color: "#4891FF",
};

const CustomTextField = styled(TextField)({
  "& .MuiInputLabel-root": labelStyle, // Apply label style
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "semibold", // Bold the input field value
    },
  },
});

const StyledButton = styled(({ ...other }) => <Button {...other} />)(
  ({ isBigScreen }) => ({
    borderRadius: "10px",
    width: isBigScreen ? "20%" : "25%",
    fontWeight: "600",
    fontSize: "16px",
    padding: "15px",
    backgroundColor: "#4891FF",
    color: "#fff",

    // Hover state
    "&:hover": {
      backgroundColor: "#357AE8",
    },

    // Disabled state
    "&.Mui-disabled": {
      backgroundColor: "#4891FF",
      color: "#fff",
      opacity: 0.6,
      cursor: "not-allowed",
    },
  })
);

const ContactUsWeb = () => {
  const isBigScreen = useMediaQuery({ query: "(min-width: 2560px)" });
  const {
    name,
    userEmail,
    nameError,
    message,
    emailError,
    loading,
    handleNameChange,
    handleEmailChange,
    handleMessageChange,
    clearEmail,
    clearName,
    onSubmit,
  } = ContactUsApi();

  const handleEmail = () => {
    window.location.href = `mailto:${import.meta.env.VITE_DEALER_HELP_EMAIL}`;
  };

  return (
    <Box sx={{ flexGrow: 1, mt: isBigScreen ? 40 : 10 }}>
      <Grid container spacing={1} sx={{ padding: "100px 0px" }}>
        <Grid item sm={12}>
          <Item className="page_bg" sx={{ marginTop: isBigScreen ? -20 : -10 }}>
            <Typography variant="h5" component="h5">
              <b>CONTACT US</b>
            </Typography>
          </Item>
        </Grid>
        <Grid
          item
          md={6}
          direction="column"
          alignItems="center"
          justifyContent="center"
        >
          <Item className="page_bg">
            <img
              src={ImageBaseURL + "contact_us.png"}
              alt="Contact us"
              style={{ maxWidth: "70%", maxHeight: "50%" }}
            />
          </Item>
        </Grid>
        <Grid
          container
          item
          xs={12}
          md={6}
          direction="column"
          alignItems="center"
          justifyContent="center"
        >
          <Grid container spacing={2} direction="column" alignItems="center">
            <Grid item xs={12} style={{ width: "100%" }}>
              <Item className="page_bg">
                <CustomTextField
                  id="outlined-basic"
                  label="Your Contact Email"
                  value={userEmail}
                  variant="outlined"
                  sx={{
                    width: isBigScreen ? "50%" : "70%",
                    background: "#ffffff",
                  }}
                  onChange={handleEmailChange}
                  InputProps={{
                    sx: {
                      fontSize: "16px",
                      fontWeight: "semibold", // Bold the input field value
                    },
                    endAdornment: (
                      <InputAdornment position="end">
                        {loading && (
                          <CircularProgress
                            sx={{ color: "#4891FF" }}
                            size={20}
                          />
                        )}
                        {userEmail !== "" && !loading && (
                          <CloseIcon
                            onClick={clearEmail}
                            style={{ cursor: "pointer" }}
                          />
                        )}
                      </InputAdornment>
                    ),
                  }}
                />
              </Item>
              {emailError && (
                <span
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    color: "red",
                  }}
                >
                  {emailError}
                </span>
              )}
              <Item className="page_bg">
                <CustomTextField
                  id="outlined-basic"
                  label="Your Name"
                  value={name}
                  variant="outlined"
                  sx={{
                    width: isBigScreen ? "50%" : "70%",
                    background: "#ffffff",
                  }}
                  onChange={handleNameChange}
                  InputProps={{
                    sx: {
                      fontSize: "16px",
                      fontWeight: "semibold", // Bold the input field value
                    },
                    endAdornment: (
                      <InputAdornment position="end">
                        {loading && (
                          <CircularProgress
                            sx={{ color: "#4891FF" }}
                            size={20}
                          />
                        )}
                        {name !== "" && !loading && (
                          <CloseIcon
                            onClick={clearName}
                            style={{ cursor: "pointer" }}
                          />
                        )}
                      </InputAdornment>
                    ),
                  }}
                />
              </Item>
              {nameError && (
                <span
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    color: "red",
                  }}
                >
                  {nameError}
                </span>
              )}
              <Item className="page_bg">
                <CustomTextField
                  id="outlined-multiline-flexible"
                  label="Type Your Query Here..."
                  variant="outlined"
                  onChange={handleMessageChange}
                  value={message}
                  multiline
                  fullWidth
                  sx={{
                    width: isBigScreen ? "50%" : "70%",
                    background: "#ffffff",
                    "& .MuiOutlinedInput-root": {
                      minHeight: isBigScreen ? "300px" : "150px",
                      alignItems: "start",
                    },
                  }}
                  InputProps={{
                    sx: {
                      fontSize: "16px",
                    },
                  }}
                />
              </Item>
              <Item className="page_bg">
                <StyledButton
                  variant="contained"
                  isBigScreen={isBigScreen}
                  disabled={nameError.length || emailError.length}
                  onClick={() => {
                    onSubmit();
                  }}
                >
                  submit
                </StyledButton>
              </Item>
              <Item className="page_bg">
                <Typography>
                  <b>
                    Your response will be sent to{" "}
                    <span
                      style={{ color: "#4891FF", cursor: "pointer" }}
                      onClick={handleEmail}
                    >
                      <u>{import.meta.env.VITE_DEALER_HELP_EMAIL} </u>
                    </span>
                  </b>
                </Typography>
              </Item>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ContactUsWeb;
