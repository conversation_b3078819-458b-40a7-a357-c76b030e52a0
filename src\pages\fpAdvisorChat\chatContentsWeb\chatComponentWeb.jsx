import {
  Box,
  Paper,
  Typography,
  TextField,
  Stack,
  Avatar,
  Grid,
  InputAdornment,
  Button,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import React, { useRef } from "react";
import CircularProgress from "@mui/material/CircularProgress";
import SendIcon from "@mui/icons-material/Send";
import ChatApi from "../api/chatApi";
import loaderGif from "../../../assets/gifs/loader.gif";

const ChatMessageContainer = styled(Box)({
  display: "flex",
  alignItems: "center",
  width: "100%",
});

const ChatMessage = styled(Paper)(({ theme, alignRight }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  maxWidth: "70%",
  marginBottom: theme.spacing(2),
  backgroundColor: alignRight ? "#4891FF" : "#61C454",
  color: "#fff",
  textAlign: "left",
  alignSelf: alignRight ? "flex-end" : "flex-start",
  borderRadius: alignRight ? "10px 0px 10px 10px" : "0px 10px 10px 10px",
  position: "relative",
  display: "flex",
  flexDirection: "column",
  alignItems: alignRight ? "flex-end" : "flex-start",
  wordBreak: "break-word",
}));

const Timestamp = styled(Typography)(({ theme }) => ({
  fontSize: "0.75rem",
  color: "#fff",
  marginTop: theme.spacing(0.5),
}));

const CustomTextField = styled(TextField)({
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "bold",
    },
  },
});

const ChatComponentWeb = () => {
  const {
    messages,
    sendAskDealerMessage,
    userRequestMsg,
    userMsgSendLoader,
    setUserRequestMsg,
    loading,
    userDetails,
  } = ChatApi();
  const messageEndRef = useRef(null);

  const scrollToBottom = () => {
    messageEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  React.useEffect(() => {
    scrollToBottom();
  }, [messages]);

  if (loading) {
    return (
      <Stack
        alignItems="center"
        justifyContent="center"
        sx={{ height: "70vh" }}
      >
        <img
          className="page_bg"
          src={loaderGif}
          alt="Loading..."
          style={{ width: "200px", height: "200px" }}
        />
        <span style={{ fontWeight: "600" }}>Loading Your Conversations...</span>
      </Stack>
    );
  }

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
        width: "100%",
      }}
    >
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          borderBottom: "1px solid #4981ff",
          ml: 2,
        }}
      >
        <Typography
          variant="h6"
          gutterBottom
          sx={{
            fontWeight: "bold",
            flexGrow: 1,
            textAlign: "center",
          }}
        >
          Chat with Advisor
        </Typography>
      </Box>

      {/* Chat Window */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          padding: 2,
          position: "relative",
          overflowY: "auto",
          flexGrow: 1,
        }}
      >
        {messages &&
          messages.map((msg) => (
            <ChatMessageContainer
              key={msg.id}
              sx={{
                display: "flex",
                alignItems: "center", // Ensures vertical alignment
                justifyContent: msg.fromDealer ? "flex-start" : "flex-end", // Align dealer messages left, user messages right
                gap: "8px", // Space between Avatar and message
              }}
            >
              {/* Show Avatar on left if from Dealer */}
              {msg.fromDealer && (
                <Avatar
                  sx={{
                    bgcolor: "#61C454",
                    color: "#fff",
                    fontSize: "0.75rem",
                    width: 32,
                    height: 32,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    bottom: 8,
                  }}
                >
                  FP
                </Avatar>
              )}

              <ChatMessage
                alignRight={!msg.fromDealer}
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                }}
              >
                {msg.text.split("\n").map((line, index) => (
                  <React.Fragment key={index}>
                    {line}
                    <br />
                  </React.Fragment>
                ))}
                <Timestamp alignRight={!msg.fromDealer}>
                  {msg.timestamp}
                </Timestamp>
              </ChatMessage>

              {/* Show Avatar on right if from User */}
              {!msg.fromDealer && (
                <Avatar
                  sx={{
                    bgcolor: "#4891FF",
                    color: "#fff",
                    fontSize: "0.75rem",
                    width: 32,
                    height: 32,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    bottom: 8,
                  }}
                >
                  {userDetails.user_first_name
                    ? userDetails.user_first_name.charAt(0).toUpperCase()
                    : "U"}
                  {userDetails.user_last_name
                    ? userDetails.user_last_name.charAt(0).toUpperCase()
                    : ""}
                </Avatar>
              )}
            </ChatMessageContainer>
          ))}
        <div ref={messageEndRef} />
      </Box>

      {/* Footer */}
      <Box
        sx={{
          pt: 2,
          borderTop: "1px solid #4981ff",
          textAlign: "center",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
          width: "100%", // Ensures it spans the full width of the footer
          flexShrink: 0, // Prevents shrinking
        }}
      >
        {/* Grid for chat input and DateTimePicker */}
        <Grid
          container
          spacing={1}
          justifyContent="center"
          alignItems="center"
          direction="row"
        >
          <Grid
            item
            md={12}
            xs={12}
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          ></Grid>
          <Grid
            item
            md={10}
            xs={10}
            sx={{ display: "flex", justifyContent: "center" }}
          >
            <CustomTextField
              id="outlined-multiline-flexible"
              label="Type Your Query Here..."
              variant="outlined"
              multiline
              fullWidth
              sx={{ flex: 1, background: "#ffffff" }}
              autoComplete="off"
              value={userRequestMsg}
              onChange={(e) => setUserRequestMsg(e.target.value)}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <Button
                      variant="contained"
                      sx={{
                        backgroundColor: "#61C454",
                        minWidth: "100px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                      onClick={() => sendAskDealerMessage(userRequestMsg)}
                      disabled={userMsgSendLoader}
                    >
                      {userMsgSendLoader ? (
                        <CircularProgress size={24} sx={{ color: "#fff" }} />
                      ) : (
                        <>
                          Send <SendIcon sx={{ ml: 1 }} />
                        </>
                      )}
                    </Button>
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default ChatComponentWeb;
