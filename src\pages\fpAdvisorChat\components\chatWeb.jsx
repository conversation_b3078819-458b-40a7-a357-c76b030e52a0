import { Box, Grid, Paper} from "@mui/material";
import { styled } from "@mui/material/styles";
import ChatComponentWeb from "../chatContentsWeb/chatComponentWeb";
// Styled components
const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const ChatWeb = () => {
  return (
    <Box sx={{ flexGrow: 1, mt: 12 }}>
      <Grid container spacing={2} sx={{ height: "calc(100vh - 150px)" }}>
        {/* right side */}
        <Grid item md={12}>
          <Item className="page_bg" sx={{ height: "calc(100vh - 200px)" }}>
            <ChatComponentWeb />
          </Item>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ChatWeb;
