import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import {
  getMakeData,
  getBodyTypes,
  getMakeFromBodytype,
  getGarageVehicleVins,
  getTrimData,
  getModelData,
  getVehicleMinPrice
} from "../../../store/apps/car/index";
import { getloggedInUserDetails } from "../../../store/apps/user";
import { fetchLocationData } from "../../../store/apps/location";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "bootstrap/dist/css/bootstrap.min.css";
import "bootstrap-icons/font/bootstrap-icons.css";
import { CarLookingOptions } from "../../../config";
import { getYearRange } from "../../../util/yearMaxMin";
import debounce from "lodash.debounce";
import { pageMetaData } from "../../../../data/pageMetaData";
import { pageComponentData } from "../../../../data/componentMetaData";

const GenericSearchApi = () => {
  const dispatch = useDispatch(); // Redux dispatch function
  const [carBrand, setCarBrand] = useState([]); // Store car brand
  const [bodyTypes, setBodyTypes] = useState([]); // Store car body types
  const [userCoordinates, setUserCoordinates] = useState({
    latitude: null,
    longitude: null,
  });
  const [loading, setLoading] = useState(true); // Manage loading state
  const [bodyToMakeLoading, setBodyToMakeLoading] = useState(true);
  const bodyTypeData = useSelector((state) => state.car.bodyTypes); // Get car body types
  const [carBrandFromBody, setCarBrandFromBody] = useState([]);
  const makeData = useSelector((state) => state.car.makeData); // Get car brand
  const [userCarLookingFor, setUserCarLookingFor] = useState(CarLookingOptions || ""); // Store user car looking for
  const [userVhicleVins, setuserVhicleVins] = useState([]);
  const [selectedExteriorColors, setSelectedExteriorColors] = useState([]); // Store selected exterior colors
  const [selectedInteriorColors, setSelectedInteriorColors] = useState([]); // Store selected interior colors
  const [selectedDistance, setSelectedDistance] = useState(import.meta.env.VITE_DEFAULT_MILES); // selected distance
  const [make, setMake] = useState([]);
  const [carModel, setCarModel] = useState(""); // Store car model
  const [carModelOptions, setCarModelOptions] = useState([]); // Store car model options
  const [shouldReorder, setShouldReorder] = useState(false); // State to track reordering
  const [allModels, setAllModels] = useState([]); // Store all models initially
  const [vehicleTrimOptions, setVehicleTrimOptions] = useState([]); // Store all trims
  const [selectedBrands, setSelectedBrands] = useState([]); // State to track selected brands
  const [selectedModelId, setSelectedModelId] = useState(null); // Store selected model ID
  const [selectedTrims, setSelectedTrims] = useState([]); // Store selected trims
  const [selectedFuelTypes, setSelectedFuelTypes] = useState([]); // Store selected fuel types
  const [zipCode, setZipCode] = useState(""); // Store zip code
  const [initialZipCode, setInitialZipCode] = useState(""); // Store zip code
  const [shouldFetchUserData, setShouldFetchUserData] = useState(true); // State to track if user data should be fetched
  const [isButtonDisabled, setIsButtonDisabled] = useState(false); // State to track if button is disabled
  const [errorMessage, setErrorMessage] = useState(""); // State to track error message
  const [loadingUser, setLoadingUser] = useState(false); // State to track if user data is loading
  const navigate = useNavigate(); // Navigate hook
  const [selectedBodyTypes, setSelectedBodyTypes] = useState([]); // State to track selected body types
  const isBigScreen = useMediaQuery({ query: "(min-width: 1824px)" }); // Check if screen width is greater than 1824px
  const [disabledEV, setDisabledEV] = useState(false); // Track if EV should be disabled
  const [disabledOtherBodyStyles, setDisabledOtherBodyStyles] = useState(false); // Track if other body styles should be disabled
  const [initialShownBrands, setInitialShownBrands] = useState([]); // Store zip code
  const [filteredMakes, setFilteredMakes] = useState([]); // Store filtered makes
  const [priceValue, setPriceValue] = useState([0, 0]); // Initial value, assuming min and max both 0 initially
  const [yearValue, setYearValue] = useState([0, 0]); // Default value
  const [isModalButtonDisabled, setIsModalButtonDisabled] = useState(false); // State to track if button is disabled
  const [minInput, setMinInput] = useState(0); // State to store the minimum input
  const [maxInput, setMaxInput] = useState(0); // State to store the maximum input
  const [minPriceError, setMinPriceError] = useState("");  // State to store the minimum price error
  const [maxPriceError, setMaxPriceError] = useState("");  // State to store the maximum price error
  const [timeoutId, setTimeoutId] = useState(null); // State to store the timeout ID
  const [minYearInput, setMinYearInput] = useState(0); // State to store the minimum year
  const [maxYearInput, setMaxYearInput] = useState(0); // State to store the maximum year
  const [minYearError, setMinYearError] = useState(""); // State to store the minimum year error
  const [maxYearError, setMaxYearError] = useState(""); // State to store the maximum year error
  const [minYearValue, setMinYearValue] = useState(0); // State to store the minimum year
  const [maxYearValue, setMaxYearValue] = useState(0); // State to store the maximum year
  const [yearValueFromDb, setYearValueFromDb] = useState([0, 0]); // State to store the year value from the database
  const [inventoryPriceYearData, setInventoryPriceYearData] = useState({}); // State to store the inventory price year data
  const [loadingLocation, setLoadingLocation] = useState(false); // State to track if location is loading
  const [previousCarLookingOption, setPrevCarLookingOption] = useState('');
  const showToast = useRef(false); // Ref to track modal visibility
  const toastSwitch = useRef(false); // Ref to track toast visibility
  const toastShow = useRef(false); // Ref to track toast visibility
  const [pageId, setPageId] = useState(null); // State to store the page_id
  const [componentMetaData, setComponentMetaData] = useState(null); // State to store the componentMetaData

  // Fetch data when component mounts
  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = localStorage.getItem("token");

        // Dispatch actions to fetch car data
        await dispatch(getMakeData(token));
        await dispatch(getBodyTypes(token));

        const vinsResponse = await dispatch(getGarageVehicleVins(token));
        setuserVhicleVins(vinsResponse.payload);
        const modelData = await dispatch(getModelData(token));
        const trimData = await dispatch(getTrimData(token));
        setAllModels(modelData.payload?.data ?? []);
        setVehicleTrimOptions(trimData.payload?.data ?? []);

        // Fetch user data and coordinates
        if (!shouldFetchUserData) return;
        if (!userCoordinates.latitude || !userCoordinates.longitude || !zipCode) {
          setLoadingUser(true);
          setLoadingLocation(true);
          const { payload: { data: userDetails = [] } = {} } = await dispatch(
            getloggedInUserDetails(token)
          );

          if (userDetails.length) {
            setUserCoordinates({
              latitude: userDetails[0].latitude,
              longitude: userDetails[0].longitude,
            });
            setZipCode(userDetails[0].zip_code);
            if (userDetails[0].zip_code == 0 || userDetails[0].zip_code == '') {
              setIsButtonDisabled(true);
            } else {
              setIsButtonDisabled(false);
            }
            setInitialZipCode(userDetails[0].zip_code);
            setIsButtonDisabled(userDetails[0].zip_code == '' || userDetails[0].zip_code == 0);

            const vehicleMinPrice = await dispatch(getVehicleMinPrice(token));
            const vehicleData = vehicleMinPrice.payload.data[0] ?? {
              new_min_price: 0,
              used_min_price: 0,
              new_min_year: 0,
              used_min_year: 0,
              new_max_year: 0,
              used_max_year: 0,
            };

            setInventoryPriceYearData(vehicleData);
            const userCarLookingFor = userDetails[0].user_car_looking_for ?? "";

            let minYear = 0;
            let maxYear = 0;

            switch (userCarLookingFor) {
              case "new or used":
                minYear = vehicleData.new_min_year;
                maxYear = vehicleData.new_max_year;
                break;

              case "new":
                minYear = new Date().getFullYear() - import.meta.env.VITE_NEW_INVENTORY_YEAR_RANGE;
                maxYear = vehicleData.new_max_year;
                break;

              case "used":
                minYear = vehicleData.used_min_year;
                maxYear = vehicleData.used_max_year;
                break;

              default:
                minYear = Math.min(vehicleData.new_min_year, vehicleData.used_min_year);
                maxYear = Math.max(vehicleData.new_max_year, vehicleData.used_max_year);
            }

            setMinYearValue(minYear);
            setMaxYearValue(maxYear);
            setYearValue([minYear, maxYear]);
            setMinYearInput(minYear);
            setMaxYearInput(maxYear);
          }
          setUserCarLookingFor(userDetails[0].user_car_looking_for ?? "");
          setPrevCarLookingOption(userDetails[0].user_car_looking_for ?? "")
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Error fetching data. Please try again later.");
      } finally {
        setLoadingUser(false);
        setLoading(false);
        setLoadingLocation(false);
      }
    };

    fetchData();
  }, [
    shouldFetchUserData,
    dispatch,
  ]);

  const debouncedFetchLocation = debounce((value, fetchLocation) => {
    fetchLocation(value);
  }, 300); // Adjust debounce delay as needed

  const handleZipCodeChange = (event) => {
    const value = event.target.value;

    // If input is empty, reset the states
    if (value === "") {
      setZipCode("");
      setIsButtonDisabled(true);
      return;
    }

    // Ensure only numeric values with a maximum of 5 digits are allowed
    if (/^\d{0,5}$/.test(value)) {
      setZipCode(value);

      // Trigger API call when exactly 5 digits are entered
      if (value.length === 5) {
        debouncedFetchLocation(value, fetchLocation); // Call the API when we have a valid 5-digit zip code
      } else {
        setIsButtonDisabled(true); // Disable the button if less than 5 digits are entered
      }
    }
  };

  // Fetch location data
  const fetchLocation = async (zip) => {
    const token = localStorage.getItem("token");
    setLoadingLocation(true);
    try {
      const response = await dispatch(
        fetchLocationData({ zipcode: zip, token: token })
      );
      if (fetchLocationData.fulfilled.match(response)) {
        const { latitude, longitude } = response.payload.data;
        setUserCoordinates({ latitude, longitude });
        setErrorMessage("");
        setIsButtonDisabled(false);
        setLoadingLocation(false);
      } else {
        console.error("Error fetching location data:", response.payload);
        setErrorMessage("Invalid Zip Code. Please Enter Correct Zip Code.");
        setIsButtonDisabled(true);
        setLoadingLocation(false);
      }
    } catch (error) {
      console.error("Unexpected error:", error);
    }
  };

  // Fetch data when component mounts
  useEffect(() => {
    const fetchMakeFromBody = async () => {
      try {
        const token = localStorage.getItem("token");
        // Dispatch actions to fetch car data
        const response = await dispatch(getMakeFromBodytype(token));
        const make = response.payload?.data ?? [];
        setCarBrandFromBody(make);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Error fetching data. Please try again later.");
      } finally {
        // Set loading to false after all data is fetched
        setBodyToMakeLoading(false);
      }
    };
    fetchMakeFromBody();
  }, []);

  // Update brands when brand data changes
  useEffect(() => {
    if (!makeData?.data || !userCarLookingFor) return; // Ensure data exists before filtering

    // Call filterMakes with makeData and userCarLookingFor
    const filteredMakes = filterMakes(makeData.data, userCarLookingFor);

    // Sort the filtered makes (optional: if needed)
    const sortedFilteredMakes = filteredMakes.slice().sort((a, b) => {
      return a.order_no - b.order_no; // Sorting by order_no
    });

    let partial_data = sortedFilteredMakes.slice(9).sort((a, b) => {
      if (a.make < b.make) return -1;
      else if (a.make > b.make) return 1;
      return 0;
    });

    let sortedData = { ...makeData };
    sortedData.data = [...sortedFilteredMakes.slice(0, 9), ...partial_data];
    // Update car brands with filtered and sorted data
    setCarBrand(sortedData);

  }, [makeData, userCarLookingFor]);

  // Filter Makes according to userCarLookingFor
  const filterMakes = (makes, userCarLookingFor) => {
    switch (userCarLookingFor) {
      case "new or used":
        return makes.filter(make =>
          (make.new_inv_flag === true || make.new_inv_flag === false) ||
          (make.used_inv_flag === true || make.used_inv_flag === false)
        );

      case "new":
        return makes.filter(make => make.new_inv_flag);

      case "used":
        return makes.filter(make => make.used_inv_flag);

      default:
        return makes; // Fallback if none match
    }
  };

  // Update body types when body type data changes
  useEffect(() => {
    setBodyTypes(bodyTypeData); // Update body types with new data
  }, [bodyTypeData]);

  // Handle exterior color selection
  const handleExteriorColorChange = (event, value) => {
    const choosenExteriorColors = value.map(color => color.title);
    setSelectedExteriorColors(choosenExteriorColors);
  };

  // Handle interior color selection
  const handleInteriorColorChange = (event, value) => {
    const choosenInteriorColors = value.map(color => color.title);
    setSelectedInteriorColors(choosenInteriorColors);
  };

  // Handle distance change
  const handleDistanceChange = (event, newValue) => {
    setSelectedDistance(newValue ? newValue.value : import.meta.env.VITE_DEFAULT_MILES);
  };

  const bodyTypedatas = bodyTypes?.data || []; // Get body types
  const brands =
    !bodyToMakeLoading && make.length ? make : carBrand?.data || []; // Get car brands

  let mappedData = [];
  if (!bodyToMakeLoading) {
    for (let i = 0; i < 10; i++) {
      let arr = [];
      carBrandFromBody.forEach((entry) => {
        if (entry.bodytype_id == i) {
          // Check if carBrand and carBrand.data are defined
          if (carBrand && Array.isArray(carBrand.data)) {
            carBrand.data.forEach((car) => {
              if (car.id == entry.make_id) arr.push(car);
            });
          }
        }
      });

      mappedData.push(arr);
    }
  }

  // Handle body type click
  const handleBodyTypeClick = (bodyType) => {
    const isEV = bodyType.body_type === "EV";
    let updatedBodyTypes;

    if (selectedBodyTypes.includes(bodyType)) {
      // Deselect the body type
      updatedBodyTypes = selectedBodyTypes.filter((bt) => bt.id !== bodyType.id);

      // Deselect associated brands
      const associatedBrands = mappedData[bodyType.id] || [];
      const updatedBrands = selectedBrands.filter(
        (brand) => !associatedBrands.some((associatedBrand) => associatedBrand.id === brand.id)
      );
      setSelectedBrands(updatedBrands);
    } else {
      // Select the body type
      if (selectedBodyTypes.length >= 3) {
        toast.warning("You cannot select more than 3 body types", {
          className: "bg-warning text-white fw-800",
          bodyClassName: "text-white",
          icon: <i className="bi bi-exclamation-circle"></i>,
        });
        return;
      }
      updatedBodyTypes = [...selectedBodyTypes, bodyType];
    }

    setSelectedBodyTypes(updatedBodyTypes);

    if (isEV) {
      setDisabledOtherBodyStyles(!disabledOtherBodyStyles); // Toggle other body styles
    } else {
      setDisabledEV(updatedBodyTypes.some((bt) => bt.body_type !== "EV")); // Disable EV if any non-EV body type is selected
    }

    // Filter brands based on updated body types
    filterBrandsBasedOnBodyTypes(updatedBodyTypes);
  };

  const [disabledBodyTypes, setDisabledBodyTypes] = useState([]);

  // Handle the change event
  const handleBodyStyleChange = (event, value) => {
    let updatedBodyTypes = value; // This will be the current selection from the dropdown

    // Check if the number of selected body types exceeds the limit
    if (updatedBodyTypes.length > 3) {
      toast.warning("You cannot select more than 3 body types", {
        className: "bg-warning text-white fw-800",
        bodyClassName: "text-white",
        icon: <i className="bi bi-exclamation-circle"></i>,
      });
      return; // Exit if limit is exceeded
    }

    setSelectedBodyTypes(updatedBodyTypes); // Update the selected body types

    // Check if "EV" is selected
    const isEVSelected = updatedBodyTypes.some((bt) => bt.body_type === "EV");

    // If "EV" is selected, disable all other body types
    if (isEVSelected) {
      setDisabledBodyTypes(
        bodyTypedatas
          .filter((bt) => bt.body_type !== "EV") // Disable all non-EV options
          .map((bt) => bt.body_type)
      );
    } else {
      // If any other body type is selected, disable "EV"
      setDisabledBodyTypes(
        updatedBodyTypes.some((bt) => bt.body_type !== "EV")
          ? ["EV"]
          : []
      );
    }

    // Filter brands based on updated body types
    filterBrandsBasedOnBodyTypes(updatedBodyTypes);
  };

  // Handle make change for dropdown
  const handleMakeChange = (event, value) => {
    let updatedBrands = value; // This will be the current selection from the dropdown

    // Check if the user is trying to select more than 3 brands
    if (updatedBrands.length > 3) {
      toast.warning("You cannot select more than 3 brands", {
        className: "bg-warning text-white fw-800",
        bodyClassName: "text-white",
        icon: <i className="bi bi-exclamation-circle"></i>,
      });
      return; // Exit if limit is exceeded
    }

    setSelectedBrands(updatedBrands); // Update the selected brands

    const initialBrands = isBigScreen
      ? brands.slice(0, 12)
      : brands.slice(0, 9);
    setInitialShownBrands(initialBrands);
    const isInitialBrand = initialBrands.some((br) => br.id === updatedBrands.id);

    // Update reorder status
    if (updatedBrands.length > 0 && !isInitialBrand) {
      setShouldReorder(true);
    } else if (updatedBrands.length === 0) {
      setShouldReorder(false);
    }
  }

  // Handle brand click
  const handleBrandClick = (brand) => {
    let updatedBrands;
    const initialBrands = isBigScreen
      ? brands.slice(0, 12)
      : brands.slice(0, 9);
    setInitialShownBrands(initialBrands);
    const isInitialBrand = initialBrands.some((br) => br.id === brand.id);

    if (selectedBrands.includes(brand)) {
      updatedBrands = selectedBrands.filter((br) => br.id !== brand.id);
    } else {
      if (selectedBrands.length >= 3) {
        toast.warning("You cannot select more than 3 brands", {
          className: "bg-warning text-white fw-800",
          bodyClassName: "text-white",
          icon: <i className="bi bi-exclamation-circle"></i>,
        });
        return;
      }
      updatedBrands = [...selectedBrands, brand];
    }
    setSelectedBrands(updatedBrands);

    if (updatedBrands.length > 0 && !isInitialBrand) {
      setShouldReorder(true);
    } else if (updatedBrands.length === 0) {
      setShouldReorder(false);
    }
  };


  // Filter brands based on selected body types
  const filterBrandsBasedOnBodyTypes = (updatedBodyTypes) => {
    // Check if the length of selectedBrands is greater than 1 to avoid Duplicate Brands
    if (selectedBrands.length > 0) {
      // Reset brand selection and order
      setMake(initialShownBrands);
      setSelectedBrands([]);
      setShouldReorder(false);
    }

    let filteredBrands = [];
    updatedBodyTypes.forEach((body) => {
      filteredBrands = [...mappedData[body.id], ...filteredBrands];
    });
    filteredBrands = [...new Set(filteredBrands)];
    filteredBrands.sort((a, b) => {
      if (a.order_no <= 9 && b.order_no <= 9) {
        return a.order_no - b.order_no;
      } else if (a.order_no > 9 && b.order_no > 9) {
        return a.make.localeCompare(b.make);
      } else if (a.order_no <= 9) {
        return -1;
      } else {
        return 1;
      }
    });
    const filteredMakes = filterMakes(filteredBrands, userCarLookingFor);
    setMake(filteredMakes);

    // Show the toast message if user chooses a different body style
    showToast.current = true;
    if (showToast.current && previousCarLookingOption === userCarLookingFor && !toastSwitch.current && selectedBrands.length > 0 && selectedBodyTypes.length > 0) {
      toastShow.current = true;
    } else if (showToast.current && previousCarLookingOption === userCarLookingFor && !toastSwitch.current && selectedBrands.length > 0 && selectedBodyTypes.length === 0) {
      toastShow.current = true;
    } else {
      toastShow.current = false;
    }

    if (toastShow.current) {
      toast.warning("Choosing or deselecting a body style after brand selection has reset your previous brand selections. Your latest choice will now take priority. You may reselect that brand if desired.", {
        className: "bg-warning text-white fw-800",
        bodyClassName: "text-white",
        icon: <i className="bi bi-exclamation-circle"></i>,
      },
        {
          autoClose: 5000
        }
      );
    }
  };


  // Reorder brands
  const reorderBrands = () => {
    const selectedSet = new Set(selectedBrands.map((br) => br.id)); // Create a set of selected brand IDs

    // Separate the brands into selected and non-selected groups
    const selectedBrandsFirst = brands.filter((brand) => selectedSet.has(brand.id));
    const remainingBrands = brands.filter((brand) => !selectedSet.has(brand.id));

    // Keep the original order for the first 9 or 12 brands
    const nonSelectedFirst = initialShownBrands.filter((brand) => !selectedSet.has(brand.id));

    // Combine selected brands first, followed by the remaining non-selected brands
    let reorderedBrands = [...selectedBrandsFirst, ...nonSelectedFirst, ...remainingBrands];

    return isBigScreen ? reorderedBrands.slice(0, 12) : reorderedBrands.slice(0, 9);
  };

  // Filter models based on selected brands
  const filterModelOptions = () => {
    const selectedBrandIds = selectedBrands.map((brand) => brand.id);
    const filteredModels = allModels.filter((model) =>
      selectedBrandIds.includes(model.make_id)
    );
    setCarModelOptions(filteredModels);
  };

  useEffect(() => {
    if (selectedBrands.length > 0) {
      filterModelOptions();
    } else {
      setCarModel(""); // Clear the selected car model when no brands are selected
      setSelectedModelId([]); // Clear selected model IDs
      setCarModelOptions([]); // Clear models if no brand is selected
    }
  }, [selectedBrands, allModels]);

  // Handle model change
  const handleModelChange = (event, newValue) => {
    const selectedModelNames = newValue.map(option => option.model); // Store an array of selected models
    const selectedModelIds = newValue.map(option => option.id); // Store an array of selected model IDs

    setCarModel(selectedModelNames); // Update car model state
    setSelectedModelId(selectedModelIds); // Update selected model ID state

    // Reset trims if no model is selected
    if (newValue.length === 0) {
      setSelectedTrims([]); // Clear selected trims when no models are selected
    }
  };

  // Clear selected trims when carModel is cleared
  useEffect(() => {
    if (carModel.length === 0) {
      setSelectedTrims([]); // Ensure trims are cleared when no model is selected
    }
  }, [carModel]);

  // Handle trim selection
  const handleTrimsChange = (event, value) => {
    const trimValues = value.map(item => item.trim); // Extract the 'trim' values
    setSelectedTrims(trimValues); // Store only the 'trim' values
  };

  // Filter the vehicle trim options based on the selected models
  const filteredVehicleTrimOptions = vehicleTrimOptions.filter(
    (option) => option.trim && option.trim !== "NULL" && option.trim !== null && option.trim !== " "
  );

  // Filter vehicle trim options based on selected models
  const filteredTrimOptions = selectedModelId?.length > 0
    ? filteredVehicleTrimOptions
      .filter((option) => selectedModelId.includes(option.model_id))
      .reduce((uniqueOptions, currentOption) => {
        if (!uniqueOptions.some(option => option.trim === currentOption.trim)) {
          uniqueOptions.push(currentOption);
        }
        return uniqueOptions;
      }, [])
    : filteredVehicleTrimOptions;

  // Handle fuel type selection
  const handleFuelTypeChange = (event, value) => {
    setSelectedFuelTypes(value);
  };

  // Handle clear selection
  const handleClearSelection = () => {
    setSelectedBrands([]);
  };

  // Handle body selection
  const handleBodySelection = () => {
    // Clear selected body types
    setSelectedBodyTypes([]);

    // Reset disabled states
    setDisabledEV(false);
    setDisabledOtherBodyStyles(false);

    // Reset brands to initial state
    if (selectedBrands.length > 0) {
      setMake(initialShownBrands); // Reset to initial list of brands
      setSelectedBrands([]);
      setShouldReorder(false);
    }

    // Optionally, filter brands based on updated (now empty) body types
    filterBrandsBasedOnBodyTypes([]);
  };

  // Handle zip code change
  const clearZipCode = () => {
    setZipCode("");
    setIsButtonDisabled(true);
  };

  const firstShownBrands = isBigScreen
    ? brands.slice(0, 12)
    : brands.slice(0, 9); // First 12 brands
  const displayedBrands = shouldReorder ? reorderBrands() : firstShownBrands; // Displayed brands based on shouldReorder state

  // Handle car looking for change
  const handleCarLookingChange = async (event, newValue) => {
    setUserCarLookingFor(newValue?.value ?? "");
    let minYear = 0;
    let maxYear = 0;
    switch (newValue?.value) {
      case "new or used":
        // If user is looking for either New or Used, we should set the lowest min price and the earliest min year, 
        // and the latest max year.
        minYear = Math.min(inventoryPriceYearData.new_min_year, inventoryPriceYearData.used_min_year);
        maxYear = Math.max(inventoryPriceYearData.new_max_year, inventoryPriceYearData.used_max_year);
        break;

      case "new":
        // If user is looking for a New car only
        minYear = new Date().getFullYear() - import.meta.env.VITE_NEW_INVENTORY_YEAR_RANGE;
        maxYear = inventoryPriceYearData.new_max_year;
        break;

      case "used":
        // If user is looking for a Used car only
        minYear = inventoryPriceYearData.used_min_year;
        maxYear = inventoryPriceYearData.used_max_year;
        break;

      default:
        minYear = inventoryPriceYearData.new_min_year;
        maxYear = inventoryPriceYearData.new_max_year;
    }
    // Check if any of the selected options has changed
    if (
      selectedBodyTypes.length > 0 ||
      selectedBrands.length > 0 ||
      selectedTrims.length > 0 ||
      selectedFuelTypes.length > 0 ||
      carModel.length > 0 ||
      zipCode !== initialZipCode ||
      minYearInput !== minYear ||
      maxYearInput !== maxYear
    ) {
      toast.warning("Changing vehicle car looking for option will reset your previous selected combination. You may reselect that combination if desired.", {
        className: "bg-warning text-white fw-800",
        bodyClassName: "text-white",
        icon: <i className="bi bi-exclamation-circle"></i>,
      },
        {
          autoClose: 1000
        }
      );
      toastSwitch.current = true;
    }
    setMinYearValue(minYear);
    setMaxYearValue(maxYear);
    setYearValue([minYear, maxYear]);
    setMinYearInput(minYear);
    setMaxYearInput(maxYear);
    setSelectedBodyTypes([]);
    setSelectedBrands([]);
    setSelectedTrims([]);
    setSelectedInteriorColors([]);
    setSelectedFuelTypes([]);
    setSelectedExteriorColors([]);
    setCarModel([]);
    setSelectedDistance(import.meta.env.VITE_DEFAULT_MILES); // Reset the distance value
    setZipCode(initialZipCode); // Clear the Zip Code
    setMinYearError("");
    setMaxYearError("");
    setMinPriceError("");
    setMaxPriceError("");
    handleBodySelection();
    setIsButtonDisabled(false);
    setDisabledBodyTypes([]);
  };

  // Handle search click
  const handleSearchClick = () => {
    if (selectedBrands.length === 0 && selectedBodyTypes.length === 0) {
      toast.warning(
        "Please select at least one brand or one body type or both to continue",
        {
          className: "bg-warning text-white fw-800",
          bodyClassName: "text-white",
          icon: <i className="bi bi-exclamation-circle"></i>,
        }
      );
      return;
    }
    // Extract body_type values from selectedBodyTypes
    const selectedBodyTypesArray = selectedBodyTypes.map((bt) => bt.body_type);

    // Extract make values from selectedBrands
    const selectedMakesArray = selectedBrands.map((br) => br.make);

    const [userMinYear, userMaxYear] = yearValue;
    const { minYear, maxYear } = getYearRange();
    const yearData = {
      min: minYear,
      max: maxYear
    };
    const yearValuesMatch = yearData.min === yearValue[0] && yearData.max === yearValue[1];

    // Create the object in the desired format
    const params = {
      body_type: selectedBodyTypesArray,
      carBrand: selectedMakesArray,
      carModel: carModel,
      latitude: userCoordinates.latitude,
      longitude: userCoordinates.longitude,
      trims: selectedTrims,
      exterior_colors: selectedExteriorColors,
      interior_colors: selectedInteriorColors,
      fuel_types: selectedFuelTypes,
      distance: selectedDistance,
      zipCode: zipCode,
      user_pre_aprv_loan_amount: null,
      user_total_purchase_power: null,
      user_car_looking_option: userCarLookingFor,
      prevRoute: "generic-search",
      addedCarinGarageVins: userVhicleVins,
      priceRangeValue: null,
      yearRangeValue: [userMinYear, userMaxYear],
      yearValuesMatch: yearValuesMatch,
      priceValuesMatch: null
    };

    navigate("/swipe-cards", { state: { params } });
  };

  useEffect(() => {
    // Get the page_id from pageMetaData based on slug
    const slug = "generic-search"; // Replace this with the desired slug
    const pageData = pageMetaData.find((page) => page.slug === slug);
    if (pageData) {
      setPageId(pageData.id);
    }

    // Get Page Componenet Meta Data
    const matchingComponents = pageComponentData.filter(
      (pageComponent) => pageComponent.page_id === pageId
    );

    if (matchingComponents.length > 0) {
      setComponentMetaData(matchingComponents);
    }
  }, [pageId]);

  return {
    userCoordinates,
    loading,
    userCarLookingFor,
    userVhicleVins,
    handleExteriorColorChange,
    selectedDistance,
    setSelectedDistance,
    handleDistanceChange,
    bodyTypedatas,
    brands,
    filterBrandsBasedOnBodyTypes,
    make,
    mappedData,
    filterModelOptions,
    handleBrandClick,
    selectedBrands,
    setSelectedBrands,
    shouldReorder,
    carModelOptions,
    handleModelChange,
    carModel,
    filteredTrimOptions,
    handleTrimsChange,
    selectedTrims,
    handleInteriorColorChange,
    handleFuelTypeChange,
    handleZipCodeChange,
    selectedExteriorColors,
    selectedInteriorColors,
    selectedFuelTypes,
    isButtonDisabled,
    errorMessage,
    zipCode,
    clearZipCode,
    loadingUser,
    setErrorMessage,
    handleSearchClick,
    handleBodyTypeClick,
    selectedBodyTypes,
    setSelectedBodyTypes,
    disabledEV,
    setDisabledEV,
    disabledOtherBodyStyles,
    setDisabledOtherBodyStyles,
    setSelectedTrims,
    setSelectedInteriorColors,
    setSelectedFuelTypes,
    setSelectedExteriorColors,
    setCarModel,
    setZipCode,
    initialZipCode,
    handleClearSelection,
    handleBodySelection,
    reorderBrands,
    displayedBrands,
    carBrandFromBody,
    filteredMakes,
    handleBodyStyleChange,
    handleMakeChange,
    CarLookingOptions,
    handleCarLookingChange,
    setUserCarLookingFor,
    priceValue,
    setPriceValue,
    yearValue,
    setYearValue,
    isModalButtonDisabled,
    setIsModalButtonDisabled,
    minInput,
    setMinInput,
    maxInput,
    setMaxInput,
    minPriceError,
    setMinPriceError,
    maxPriceError,
    setMaxPriceError,
    timeoutId,
    setTimeoutId,
    minYearInput,
    setMinYearInput,
    maxYearInput,
    setMaxYearInput,
    minYearError,
    setMinYearError,
    maxYearError,
    setMaxYearError,
    minYearValue,
    setMinYearValue,
    maxYearValue,
    setMaxYearValue,
    yearValueFromDb,
    setYearValueFromDb,
    loadingLocation,
    setFilteredMakes,
    disabledBodyTypes,
    setShouldFetchUserData,
    previousCarLookingOption,
    setIsButtonDisabled,
    setDisabledBodyTypes,
    showToast,
    componentMetaData
  };
};

export default GenericSearchApi;
