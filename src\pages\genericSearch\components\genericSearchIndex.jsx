import { useMediaQuery } from 'react-responsive'
import GenericSearchMobile from './genericSearchMobile';
import GenericSearchWeb from './genericSearchWeb';
import MasterLayout from '../../../components/layouts/masterLayout';

function GenericSearchIndex() {
    const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' })
    return (
        <MasterLayout>
            {isTabletOrMobile ? <GenericSearchMobile /> : <GenericSearchWeb />}
        </MasterLayout>
    );
}

export default GenericSearchIndex;