import { useEffect, useState } from "react";
import { useMediaQuery } from "react-responsive";
import genericSearchApi from "../api/genericSearchApi";
import {
  Typography,
  Box,
  Grid,
  styled,
  Paper,
  ButtonBase,
  Tooltip,
  Zoom,
  Button,
  Stack,
} from "@mui/material";
import { ImageBaseURL } from "../../../config";
import ShowBrandsModal from "../modal/showBrandsModal";
import GenericAdvancedSearchModalWeb from "../modal/genericAdvancedSearchModalWeb";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import "react-toastify/dist/ReactToastify.css";
import "bootstrap/dist/css/bootstrap.min.css";
import "bootstrap-icons/font/bootstrap-icons.css";
import ManageSearchIcon from "@mui/icons-material/ManageSearch";
import loaderGif from "../../../assets/gifs/loader.gif";
import ConfirmModalCloseDialog from "../modal/ConfirmModalCloseDialog";
import { useDispatch } from "react-redux";
import { callComponentActionApi } from "../../../util/callComponenetActionApi";

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const BodyTypeBox = styled(({ ...other }) => <ButtonBase {...other} />)(
  ({ theme, isSelectedBody, isBigScreen }) => ({
    width: isBigScreen ? 200 : 115,
    height: isBigScreen ? 200 : 115,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center", // Center content vertically
    padding: theme.spacing(1), // Add padding for spacing
    borderRadius: theme.shape.borderRadius,
    backgroundColor: isSelectedBody ? "#808080" : "#ffffff", // Light blue background when selected
    boxShadow: "0px 6px 12px rgba(0, 0, 0, 0.2)",
    transition: "transform 0.1s, box-shadow 0.1s",
    "&:hover": {
      bgcolor: "primary.dark",
      transform: "translateY(-2px)",
      boxShadow: "0px 8px 16px rgba(0, 0, 0, 0.3)",
    },
    "&:active": {
      bgcolor: "primary.darker",
      transform: "translateY(2px)",
      boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
    },
    "& img": {
      width: "100%", // Ensure image does not overflow
      height: "70%", // Limit the image height to prevent overflow
      objectFit: "contain", // Maintain aspect ratio
    },
    "& .MuiTypography-root": {
      marginTop: theme.spacing(1), // Space between image and text
      textAlign: "center", // Center text
      overflow: "hidden", // Ensure text does not overflow
      textOverflow: "ellipsis", // Add ellipsis for long text
    },
    "& .tick-overlay-bodies": {
      position: "absolute",
      top: 0,
      right: 0,
      width: "100%",
      height: "100%",
      backgroundColor: "rgba(128, 128, 128, 0.5)", // Semi-transparent overlay
      display: isSelectedBody ? "flex" : "none",
      alignItems: "center",
      justifyContent: "center",
      color: "white",
      fontSize: 24,
      zIndex: 1,
    },
  })
);

const CarBrandBox = styled(({ ...other }) => <ButtonBase {...other} />)(
  ({ theme, isSelected, isBigScreen }) => ({
    width: isBigScreen ? 250 : 150,
    height: isBigScreen ? 115 : 80,
    fontSize: 12,
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: theme.spacing(1),
    borderRadius: theme.shape.borderRadius,
    border: isSelected ? "2px solid #4891FF" : "1px solid #4891FF", // Highlight selected
    backgroundColor: isSelected ? "#e3f2fd" : "#ffffff", // Light blue background when selected
    boxShadow: "0px 6px 12px rgba(0, 0, 0, 0.2)",
    transition: "transform 0.1s, box-shadow 0.1s",
    position: "relative", // To position the tick overlay
    "&:hover": {
      bgcolor: "primary.dark",
      transform: "translateY(-2px)",
      boxShadow: "0px 8px 16px rgba(0, 0, 0, 0.3)",
    },
    "&:active": {
      bgcolor: "primary.darker",
      transform: "translateY(2px)",
      boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
    },
    "& img": {
      width: "45%",
      height: "90%",
      objectFit: "contain",
    },
    "& .MuiTypography-root": {
      marginLeft: theme.spacing(1),
      textAlign: "center",
      overflow: "hidden",
      textOverflow: "ellipsis",
      flex: 1,
    },
    "& .tick-overlay": {
      position: "absolute",
      top: 0,
      right: 0,
      width: "100%",
      height: "100%",
      backgroundColor: "rgba(72, 145, 255, 0.5)", // Semi-transparent overlay
      display: isSelected ? "flex" : "none",
      alignItems: "center",
      justifyContent: "center",
      color: "white",
      fontSize: 24,
      zIndex: 1,
    },
  })
);

const StyledButton = styled(Button)({
  borderRadius: "10px",
  width: "40%",
  fontWeight: "600",
  padding: "15px",
  backgroundColor: "#4891FF",
  "&:hover": {
    backgroundColor: "#4891FF",
  },
  // Disabled state
  "&.Mui-disabled": {
    backgroundColor: "#4891FF",
    color: "#fff",
    opacity: 0.6,
    cursor: "not-allowed",
  },
});

const GenericSearchWeb = () => {
  const isBigScreen = useMediaQuery({ query: "(min-width: 1824px)" }); // Check if screen width is greater than 1824px
  const {
    loading,
    maxApprovalAmount,
    purchasePower,
    userCarLookingFor,
    handleExteriorColorChange,
    selectedDistance,
    setSelectedDistance,
    handleDistanceChange,
    bodyTypedatas,
    brands,
    make,
    handleBrandClick,
    selectedBrands,
    carModelOptions,
    handleModelChange,
    carModel,
    filteredTrimOptions,
    handleTrimsChange,
    selectedTrims,
    handleInteriorColorChange,
    handleFuelTypeChange,
    handleZipCodeChange,
    selectedExteriorColors,
    selectedInteriorColors,
    selectedFuelTypes,
    isButtonDisabled,
    errorMessage,
    zipCode,
    clearZipCode,
    loadingUser,
    setErrorMessage,
    handleSearchClick,
    handleBodyTypeClick,
    selectedBodyTypes,
    disabledEV,
    disabledOtherBodyStyles,
    setSelectedTrims,
    setSelectedInteriorColors,
    setSelectedFuelTypes,
    setSelectedExteriorColors,
    setCarModel,
    setZipCode,
    initialZipCode,
    handleClearSelection,
    handleBodySelection,
    displayedBrands,
    carBrandFromBody,
    filteredMakes,
    handleBodyStyleChange,
    handleBrandChange,
    handleMakeChange,
    CarLookingOptions,
    handleCarLookingChange,
    invVehicleMinPrice,
    priceValue,
    setPriceValue,
    minMaxPrice,
    setMinMaxPrice,
    yearValue,
    setYearValue,
    isModalButtonDisabled,
    setIsModalButtonDisabled,
    minInput,
    setMinInput,
    maxInput,
    setMaxInput,
    minPriceError,
    setMinPriceError,
    maxPriceError,
    setMaxPriceError,
    timeoutId,
    setTimeoutId,
    minYearInput,
    setMinYearInput,
    maxYearInput,
    setMaxYearInput,
    minYearError,
    setMinYearError,
    maxYearError,
    setMaxYearError,
    minYearValue,
    setMinYearValue,
    maxYearValue,
    setMaxYearValue,
    loadingLocation,
    setSelectedBodyTypes,
    setSelectedBrands,
    disabledBodyTypes,
    previousCarLookingOption,
    setUserCarLookingFor,
    setIsButtonDisabled,
    setDisabledBodyTypes,
    showToast,
    componentMetaData,
  } = genericSearchApi(); // Get car brand and body types
  const [showModal, setShowModal] = useState(false); // State to track modal visibility
  const [modalOpen, setModalOpen] = useState(false); // State for modal opening
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false); // State for confirm dialog
  const dispatch = useDispatch();
  // Get car looking for
  const selectedCarLookingOption =
    CarLookingOptions.find((option) => option.value == userCarLookingFor) ||
    CarLookingOptions.find((option) => option.value === "new or used");

  // Handle advanced search
  const handleAdvancedSearch = () => {
    setModalOpen(true);
  };

  // Function to close modal
  const handleCloseSearchModal = () => {
    const yearData = {
      min: minYearValue,
      max: maxYearValue,
    };
    const yearValuesMatch =
      yearData.min === yearValue[0] && yearData.max === yearValue[1];
    if (
      selectedTrims.length > 0 ||
      selectedBodyTypes.length > 0 ||
      selectedBrands.length > 0 ||
      selectedInteriorColors.length > 0 ||
      selectedFuelTypes.length > 0 ||
      selectedExteriorColors.length > 0 ||
      carModel.length > 0 ||
      zipCode !== initialZipCode ||
      !yearValuesMatch > 0 ||
      selectedDistance !== import.meta.env.VITE_DEFAULT_MILES ||
      previousCarLookingOption !== userCarLookingFor
    ) {
      // Open the confirmation dialog instead of confirm alert
      setConfirmDialogOpen(true);
    } else {
      if (initialZipCode == 0 || initialZipCode == "") {
        setIsButtonDisabled(true);
      } else {
        setIsButtonDisabled(false);
      }
      setDisabledBodyTypes([]);
      closeModal(); // Directly close the modal if no changes
    }
  };

  const closeModal = () => {
    handleClose();
    setModalOpen(false);
    setIsButtonDisabled(initialZipCode === 0 || initialZipCode === "");
    setDisabledBodyTypes([]);
  };

  const handleClose = () => {
    // Simply close the confirmation dialog
    setConfirmDialogOpen(false);
  };

  const handleConfirm = () => {
    // Reset all fields if the user confirms
    showToast.current = false;
    setSelectedTrims([]);
    setSelectedInteriorColors([]);
    setSelectedFuelTypes([]);
    setSelectedExteriorColors([]);
    setSelectedBodyTypes([]);
    setSelectedTrims([]);
    setSelectedBrands([]);
    setCarModel([]);
    setSelectedDistance(import.meta.env.VITE_DEFAULT_MILES); // Reset the distance value
    setZipCode(initialZipCode); // Clear the Zip Code
    setMinYearError("");
    setMaxYearError("");
    setMinPriceError("");
    setMaxPriceError("");
    setMinYearInput(minYearValue);
    setMaxYearInput(maxYearValue);
    setMinYearValue(minYearValue);
    setMaxYearValue(maxYearValue);
    setYearValue([minYearValue, maxYearValue]);
    setErrorMessage(""); // Clear any error messages
    handleBodySelection();
    setModalOpen(false);
    setUserCarLookingFor(
      previousCarLookingOption !== userCarLookingFor
        ? previousCarLookingOption
        : userCarLookingFor
    );
    if (initialZipCode == 0 || initialZipCode == "") {
      setIsButtonDisabled(true);
    } else {
      setIsButtonDisabled(false);
    }
    setDisabledBodyTypes([]);
    closeModal();
  };
  const handleOpenModal = () => setShowModal(true); // Open modal
  const handleCloseModal = () => setShowModal(false); // Close modal

  useEffect(() => {
    let isMounted = true; // Add a flag to check if the component is still mounted

    if (!loading && isMounted) {
      try {
        const component = componentMetaData.find(
          (comp) => comp.slug === "generic-search" && comp.type === "url"
        );
        if (!component) {
          console.error(`Component with slug "generic-search" not found.`);
          return;
        }
        if (import.meta.env.VITE_IS_GENERIC_SEARCH_SHOW === "true")
          // Call the API with the component ID
          callComponentActionApi(dispatch, component.id);
      } catch (error) {
        console.error("Error handling button click:", error);
      }
    }

    // Cleanup function to reset the flag when the component is unmounted
    return () => {
      isMounted = false;
    };
  }, [loading, componentMetaData]);

  const fallbackImg = `${ImageBaseURL}carBrandLogos/no-image.jpg`;
  // Render loading state while data is being fetched
  if (loading) {
    return (
      <Stack
        alignItems="center"
        justifyContent="center"
        sx={{ height: "100vh" }}
      >
        <img
          className="page_bg"
          src={loaderGif}
          alt="Loading..."
          style={{ width: "200px", height: "200px" }}
        />
        <span style={{ fontWeight: "600" }}>
          Fetching Vehicle Body Types & Brands...
        </span>
      </Stack>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }} mt={isBigScreen ? 16 : 12} mb={4} mx={2}>
      <Grid container spacing={2}>
        <Grid item md={6}>
          <Item className="page_bg">
            <Typography variant="h6" gutterBottom sx={{ fontWeight: "bold" }}>
              Select Upto 3 Vehicle Body Styles
            </Typography>
          </Item>
          <Item className="page_bg">
            <Grid container spacing={2} justifyContent="center">
              {bodyTypedatas.map((bodyType) => {
                let displayName = bodyType.body_type;
                if (displayName === "SUV") {
                  displayName = "SUV/Crossover";
                } else if (displayName === "Pickup") {
                  displayName = "Pickup Truck";
                }
                const isEV = bodyType.body_type === "EV";
                const isDisabled =
                  (isEV && disabledEV) || (!isEV && disabledOtherBodyStyles);
                return (
                  <Grid
                    item
                    md={4}
                    key={bodyType.id}
                    sx={{ display: "flex", justifyContent: "center" }}
                  >
                    <BodyTypeBox
                      isBigScreen={isBigScreen}
                      onClick={() =>
                        !isDisabled && handleBodyTypeClick(bodyType)
                      }
                      isSelectedBody={selectedBodyTypes.includes(bodyType)}
                      disabled={isDisabled} // Disable based on state
                      className={isDisabled ? "body-type-button-disabled" : ""}
                    >
                      <img
                        src={`${ImageBaseURL}carBodyTypes/${bodyType.image_name}`}
                        alt={displayName}
                      />
                      <Typography variant="body2" sx={{ fontWeight: "bold" }}>
                        {displayName}
                      </Typography>
                      <div className="tick-overlay-bodies">
                        <CheckCircleIcon fontSize="large" />
                      </div>
                    </BodyTypeBox>
                  </Grid>
                );
              })}
            </Grid>
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                mt: isBigScreen ? 4 : 2,
              }}
            >
              <Button
                variant="outlined"
                onClick={handleBodySelection}
                className="map-text"
                style={{
                  textDecoration: "underline",
                  background: "transparent",
                  border: "none",
                  color: "#4891FF",
                  fontWeight: "800",
                }}
              >
                Clear Selected Body Styles
              </Button>
            </Box>
          </Item>
        </Grid>
        <Grid item md={6}>
          <Item className="page_bg">
            <Typography variant="h6" gutterBottom sx={{ fontWeight: "bold" }}>
              Select Upto 3 Brands
            </Typography>
          </Item>
          {selectedBodyTypes.length && !make.length ? (
            <Item className="page_bg">
              <Typography
                variant="h6"
                gutterBottom
                sx={{
                  fontWeight: "bold",
                  height: 200,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                No matching brand found depending your body type selection !
              </Typography>
            </Item>
          ) : (
            <>
              <Item className="page_bg">
                <Grid container spacing={2} justifyContent="center">
                  {displayedBrands.map((brand) => (
                    <Grid
                      item
                      md={4}
                      key={brand.id}
                      sx={{ display: "flex", justifyContent: "center" }}
                    >
                      <CarBrandBox
                        isSelected={selectedBrands.includes(brand)}
                        onClick={() => handleBrandClick(brand)}
                        isBigScreen={isBigScreen}
                      >
                        <img
                          src={
                            `${ImageBaseURL}carBrandLogos/${brand.image_name}` ||
                            fallbackImg
                          }
                          alt={brand.make}
                          onError={(e) => {
                            e.target.src = fallbackImg;
                          }}
                        />
                        <Tooltip
                          TransitionComponent={Zoom}
                          title={brand.make.toUpperCase()}
                          arrow
                        >
                          <Typography
                            variant="subtitle2"
                            sx={{ fontWeight: "bold" }}
                          >
                            {brand.make.toUpperCase()}
                          </Typography>
                        </Tooltip>
                        <div className="tick-overlay">
                          <CheckCircleIcon fontSize="large" />
                        </div>
                      </CarBrandBox>
                    </Grid>
                  ))}
                </Grid>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    my: isBigScreen ? 4 : 0,
                    mx: isBigScreen ? 8 : 3,
                  }}
                >
                  <Button
                    variant="outlined"
                    onClick={handleClearSelection}
                    className="map-text"
                    style={{
                      textDecoration: "underline",
                      background: "transparent",
                      border: "none",
                      color: "#4891FF",
                      fontWeight: "800",
                    }}
                  >
                    Clear Selected Brands
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={handleOpenModal}
                    className="map-text"
                    style={{
                      textDecoration: "underline",
                      background: "transparent",
                      border: "none",
                      color: "#4891FF",
                      fontWeight: "800",
                    }}
                  >
                    View All Brands
                  </Button>
                </Box>
              </Item>
              <Item>
                <span
                  className="fs-6 fw-bold btn-link"
                  style={{ color: "#4891FF", cursor: "pointer" }}
                  onClick={handleAdvancedSearch}
                >
                  Advanced Search
                </span>
              </Item>
              <Item className="page_bg">
                <StyledButton variant="contained" onClick={handleSearchClick}>
                  <ManageSearchIcon fontSize="large" sx={{ mr: 1 }} />
                  get your search results
                </StyledButton>
              </Item>
            </>
          )}
        </Grid>
      </Grid>

      <ShowBrandsModal
        show={showModal}
        onClose={handleCloseModal}
        brands={brands}
        isBigScreen={isBigScreen}
        selectedBrands={selectedBrands}
        handleBrandClick={handleBrandClick}
      />
      <GenericAdvancedSearchModalWeb
        open={modalOpen}
        onClose={handleCloseSearchModal}
        handleExteriorColorChange={handleExteriorColorChange}
        selectedDistance={selectedDistance}
        handleDistanceChange={handleDistanceChange}
        carModelOptions={carModelOptions}
        handleModelChange={handleModelChange}
        carModel={carModel}
        filteredTrimOptions={filteredTrimOptions}
        handleTrimsChange={handleTrimsChange}
        handleInteriorColorChange={handleInteriorColorChange}
        handleFuelTypeChange={handleFuelTypeChange}
        handleZipCodeChange={handleZipCodeChange}
        isButtonDisabled={isButtonDisabled}
        errorMessage={errorMessage}
        zipCode={zipCode}
        clearZipCode={clearZipCode}
        loadingUser={loadingUser}
        selectedBrands={selectedBrands}
        handleSearchClick={handleSearchClick}
        bodyTypedatas={bodyTypedatas}
        selectedBodyTypes={selectedBodyTypes}
        carBrandFromBody={carBrandFromBody}
        filteredMakes={filteredMakes}
        handleBodyStyleChange={handleBodyStyleChange}
        brands={brands}
        handleBrandChange={handleBrandChange}
        handleBodyTypeClick={handleBodyTypeClick}
        handleMakeChange={handleMakeChange}
        disabledEV={disabledEV}
        disabledOtherBodyStyles={disabledOtherBodyStyles}
        carLookingOptions={CarLookingOptions}
        handleCarLookingChange={handleCarLookingChange}
        selectedCarLookingOption={selectedCarLookingOption}
        make={make}
        userMaxPrice={purchasePower || maxApprovalAmount}
        invVehicleMinPrice={invVehicleMinPrice}
        priceValue={priceValue}
        setPriceValue={setPriceValue}
        minMaxPrice={minMaxPrice}
        setMinMaxPrice={setMinMaxPrice}
        yearValue={yearValue}
        setYearValue={setYearValue}
        isModalButtonDisabled={isModalButtonDisabled}
        setIsModalButtonDisabled={setIsModalButtonDisabled}
        minInput={minInput}
        setMinInput={setMinInput}
        maxInput={maxInput}
        setMaxInput={setMaxInput}
        minPriceError={minPriceError}
        setMinPriceError={setMinPriceError}
        maxPriceError={maxPriceError}
        setMaxPriceError={setMaxPriceError}
        timeoutId={timeoutId}
        setTimeoutId={setTimeoutId}
        minYearInput={minYearInput}
        setMinYearInput={setMinYearInput}
        maxYearInput={maxYearInput}
        setMaxYearInput={setMaxYearInput}
        minYearError={minYearError}
        setMinYearError={setMinYearError}
        maxYearError={maxYearError}
        setMaxYearError={setMaxYearError}
        minYearValue={minYearValue}
        setMinYearValue={setMinYearValue}
        maxYearValue={maxYearValue}
        setMaxYearValue={setMaxYearValue}
        selectedTrims={selectedTrims}
        loadingLocation={loadingLocation}
        disabledBodyTypes={disabledBodyTypes}
        setIsButtonDisabled={setIsButtonDisabled}
      />
      <ConfirmModalCloseDialog
        open={confirmDialogOpen}
        onClose={handleClose}
        onConfirm={handleConfirm}
        handleCloseSearchModal={handleCloseSearchModal}
      />
    </Box>
  );
};

export default GenericSearchWeb;
