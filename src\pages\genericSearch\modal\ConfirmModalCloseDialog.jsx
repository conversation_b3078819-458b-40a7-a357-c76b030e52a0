import { Button, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle } from '@mui/material';
import PropTypes from "prop-types";
import { useLocation } from 'react-router-dom';

const ConfirmModalCloseDialog = ({ open, onClose, onConfirm }) => {
    const location = useLocation();

    // Access the current route's pathname
    const currentRoute = location.pathname;

    return (
        <Dialog
            open={open}
            onClose={onClose}
            disableEnforceFocus
            disableScrollLock
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
        >
            <DialogTitle id="alert-dialog-title">
                {"Confirm Close Modal"}
            </DialogTitle>
            <DialogContent>
                <DialogContentText id="alert-dialog-description">
                    {currentRoute === "/specific-search"
                        ? " Are you sure you want to close the modal? Once the modal is closed all your selected filters except vehicle looking for, make, model, distance & zip code, will be cleared."
                        : "Are you sure you want to close the modal? Once the modal is closed all your selected filters will be cleared."
                    }
                </DialogContentText>
            </DialogContent>
            <DialogActions>
                <Button onClick={onClose}>Cancel</Button>
                <Button onClick={onConfirm} sx={{ color: "red" }} autoFocus>
                    Close
                </Button>
            </DialogActions>
        </Dialog>
    );
};

ConfirmModalCloseDialog.propTypes = {
    open: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    onConfirm: PropTypes.func.isRequired,
};

export default ConfirmModalCloseDialog;
