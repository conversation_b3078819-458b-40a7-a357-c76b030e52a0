import PropTypes from "prop-types";
import { Box, Button, TextField, Autocomplete, Checkbox, CircularProgress, InputAdornment, Typography, Slider } from "@mui/material";
import { Modal } from "react-bootstrap";
import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import ManageSearchIcon from "@mui/icons-material/ManageSearch";
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CloseIcon from "@mui/icons-material/Close";
import LensIcon from '@mui/icons-material/Lens';
import { VehicleColors, FuelTypes, DistanceOptions, EvFuelTypes } from "../../../config";
import { useEffect, useState } from "react";

const Item = styled(Paper)(({ theme }) => ({
    ...theme.typography.body2,
    padding: theme.spacing(1),
    textAlign: "center",
    borderRadius: "none",
    boxShadow: "none",
}));
const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;
const enabledLabelStyle = {
    color: "#4891FF", // Blue color for enabled state
};

const disabledLabelStyle = {
    color: "#C0C0C0", // Grey color for disabled state
};

const CustomTextField = styled(TextField)(({ disabled }) => ({
    "& .MuiInputLabel-root": disabled ? disabledLabelStyle : enabledLabelStyle,
    "& .MuiOutlinedInput-root": {
        "& fieldset": {
            borderColor: "#4891FF",
            borderWidth: "2px",
        },
        "&:hover fieldset": {
            borderColor: "#4891FF",
        },
        "&.Mui-focused fieldset": {
            borderColor: "#4891FF",
        },
        "& input": {
            fontWeight: "semibold",
        },
    },
    '& .MuiSvgIcon-root': {
        color: disabled ? '#C0C0C0' : '#4891FF',
    },
}));

const CustomTextFieldZip = styled(TextField)({
    "& .MuiInputLabel-root": enabledLabelStyle, // Apply label style
    "& .MuiOutlinedInput-root": {
        "& fieldset": {
            borderColor: "#4891FF",
            borderWidth: "2px",
        },
        "&:hover fieldset": {
            borderColor: "#4891FF",
        },
        "&.Mui-focused fieldset": {
            borderColor: "#4891FF",
        },
        "& input": {
            fontWeight: "semibold", // Bold the input field value
        },
    },
});

const CustomAutocomplete = styled(Autocomplete)(({ disabled }) => ({
    "& .MuiOutlinedInput-root": {
        "& fieldset": {
            borderColor: "#4891FF",
            borderWidth: "2px",
        },
        "&:hover fieldset": {
            borderColor: "#4891FF",
        },
        "&.Mui-focused fieldset": {
            borderColor: "#4891FF",
        },
        "& input": {
            fontWeight: "semibold",
        },
    },
    "& .MuiAutocomplete-option": {
        fontWeight: "semibold",
    },
    '& .MuiSvgIcon-root': {
        color: disabled ? '#C0C0C0' : '#4891FF',
    },
}));

const StyledButton = styled(({ ...other }) => <Button {...other} />)(
    () => ({
        borderRadius: "10px",
        width: "30%",
        fontWeight: "600",
        fontSize: "12px",
        padding: "12px",
        backgroundColor: "#4891FF",
        color: "#fff",

        "&:hover": {
            backgroundColor: "#357AE8",
        },

        "&.Mui-disabled": {
            backgroundColor: "#4891FF",
            color: "#fff",
            opacity: 0.6,
            cursor: "not-allowed",
        },
    })
);

const GenericAdvancedSearchModalWeb = ({
    open,
    onClose,
    handleExteriorColorChange,
    selectedDistance,
    handleDistanceChange,
    carModelOptions,
    handleModelChange,
    carModel,
    filteredTrimOptions,
    handleTrimsChange,
    handleInteriorColorChange,
    handleFuelTypeChange,
    handleZipCodeChange,
    errorMessage,
    zipCode,
    clearZipCode,
    selectedBrands,
    handleSearchClick,
    isButtonDisabled,
    bodyTypedatas,
    selectedBodyTypes,
    handleBodyStyleChange,
    brands,
    handleMakeChange,
    carLookingOptions,
    handleCarLookingChange,
    selectedCarLookingOption,
    yearValue,
    setYearValue,
    minYear,
    maxYear,
    setIsModalButtonDisabled,
    minYearInput,
    setMinYearInput,
    maxYearInput,
    setMaxYearInput,
    minYearError,
    setMinYearError,
    maxYearError,
    setMaxYearError,
    minYearValue,
    maxYearValue,
    make,
    selectedTrims,
    loadingLocation,
    disabledBodyTypes,
    setIsButtonDisabled
}) => {

    const [availableFuelTypes, setAvailableFuelTypes] = useState(FuelTypes || ''); // Create a local state for showing the filtered fuel types
    const [filteredBrands, setFilteredBrands] = useState([]); // Create a local state for showing the filtered brands

    useEffect(() => {
        // Scenario 1: If both are empty
        if (selectedBodyTypes.length === 0 && make.length === 0) {
            setFilteredBrands(brands); // Set to original brands list
        }
        // Scenario 2: If selectedBodyTypes has items but make is empty
        else if (selectedBodyTypes.length > 0 && make.length === 0) {
            setFilteredBrands([]); // Clear the brands
        }
        // Default case: If neither of the above conditions are true
        else {
            setFilteredBrands(brands); // Set brands to the original list
        }
        if (zipCode == 0 || zipCode == '') {
            setIsButtonDisabled(true);
        } else {
            setIsButtonDisabled(false);
        }
    }, [make, brands, selectedBodyTypes]);

    // checking if EV is selected
    useEffect(() => {
        const isOnlyEVSelected = selectedBodyTypes.length === 1 && selectedBodyTypes[0].body_type === "EV";

        if (isOnlyEVSelected) {
            setAvailableFuelTypes(EvFuelTypes || []); // Show all EV fuel types if EV is selected
        } else {
            // Show all fuel types when EV is not selected
            setAvailableFuelTypes(FuelTypes); // Ensure FuelTypes is an array
        }
    }, [selectedBodyTypes]); // Run effect whenever selectedBodyTypes changes

    const isNumeric = (value) => {
        return /^-?\d+(\.\d+)?$/.test(value); // Regular expression for numeric values (including decimals)
    };

    // Year Slider //
    const handleYearChange = (event, newValue) => {
        setYearValue(newValue);
        setMinYearInput(newValue[0]);
        setMaxYearInput(newValue[1]);
    };

    const handleMinYearChange = (event) => {
        const newMin = Number(event.target.value); // Get the user input as a number
        if (!isNumeric(newMin) && newMin !== "") {
            setMinYearError("Please enter a valid numeric value.");
            return; // If not a number, do not set the input
        } else {
            setMinYearError(""); // Clear error if valid
        }

        setMinYearInput(event.target.value); // Update the input field immediately

        // Check if newMin is within valid range and less than or equal to the current max
        if (newMin < minYear || newMin > maxYear) {
            setMinYearError(`Please enter a value between ${minYear} and ${maxYear}`);
            setIsModalButtonDisabled(true);
        } else if (newMin > yearValue[1]) {
            setMinYearError(
                `Please enter a value less than or equal to ${yearValue[1]}`
            );
            setIsModalButtonDisabled(true);
        } else {
            setMinYearError(""); // Clear the error message
            setYearValue([newMin, yearValue[1]]); // Update the slider value
            setIsModalButtonDisabled(false);
        }
    };

    const handleMaxYearChange = (event) => {
        const newMax = Number(event.target.value); // Get the user input as a number
        if (!isNumeric(newMax) && newMax !== "") {
            setMaxYearError("Please enter a valid numeric value.");
            return; // If not a number, do not set the input
        } else {
            setMaxYearError(""); // Clear error if valid
        }

        setMaxYearInput(event.target.value); // Update the input field immediately

        if (newMax > maxYear || newMax < minYear) {
            setMaxYearError(`Please enter a value between ${minYear} and ${maxYear}`);
            setIsModalButtonDisabled(true);
        } else if (newMax < yearValue[0]) {
            setMaxYearError(
                `Please enter a value greater than or equal to ${yearValue[0]}`
            );
            setIsModalButtonDisabled(true);
        } else {
            setMaxYearError(""); // Clear the error message
            setYearValue([yearValue[0], newMax]); // Update the slider value
            setIsModalButtonDisabled(false);
        }
    };

    // Year Slider End//

    return (
        <Modal show={open} onHide={onClose} centered size="lg">
            <Modal.Header className="modal-header-fixed" closeButton>
                <Modal.Title>Advanced Search</Modal.Title>
            </Modal.Header>
            <Modal.Body className="modal-body-scrollable">
                <Box sx={{ flexGrow: 1 }}>
                    <Grid container spacing={2}>
                        {/* Car Looking For */}
                        <Grid item xs={6}>
                            <Item className="page_bg">
                                <CustomAutocomplete
                                    value={selectedCarLookingOption}
                                    id="distance-select"
                                    variant="outlined"
                                    disableClearable
                                    onChange={handleCarLookingChange}
                                    options={carLookingOptions}
                                    autoHighlight
                                    getOptionLabel={(option) => option.label}
                                    style={{ borderColor: "#4891FF", background: '#ffffff' }}
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            label="Vehicle Looking For"
                                            InputLabelProps={{
                                                classes: {
                                                    root: "input_fonts",
                                                },
                                            }}
                                        />
                                    )}
                                />
                            </Item>
                        </Grid>
                        <Grid item xs={6}>
                            <Item className="page_bg">
                                <CustomAutocomplete
                                    value={DistanceOptions.find(option => option.value == selectedDistance) || ''}
                                    id="distance-select"
                                    variant="outlined"
                                    disableClearable
                                    onChange={handleDistanceChange}
                                    options={DistanceOptions}
                                    autoHighlight
                                    getOptionLabel={(option) => (option && option.value !== undefined) ? `${option.value} miles` : ''}
                                    style={{ borderColor: "#4891FF", background: '#ffffff' }}
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            label="Select Distance"
                                            InputLabelProps={{
                                                classes: {
                                                    root: "input_fonts",
                                                },
                                            }}
                                        />
                                    )}
                                />
                            </Item>
                        </Grid>
                        <Grid item xs={6}>
                            <Item className="page_bg">
                                <CustomTextFieldZip
                                    fullWidth
                                    id="outlined-basic"
                                    label="Zip Code"
                                    variant="outlined"
                                    autoComplete="off"
                                    value={zipCode}
                                    onChange={handleZipCodeChange}
                                    InputProps={{
                                        sx: {
                                            fontSize: "16px",
                                            fontWeight: "semibold", // Bold the input field value
                                        },
                                        endAdornment: (
                                            <InputAdornment position="end">
                                                {loadingLocation ? (
                                                    <CircularProgress
                                                        sx={{ color: "#4891FF" }}
                                                        size={20}
                                                    />
                                                ) : (
                                                    <>
                                                        {zipCode !== "" && !loadingLocation && (
                                                            <CloseIcon
                                                                onClick={clearZipCode}
                                                                style={{ cursor: "pointer" }}
                                                            />
                                                        )}
                                                    </>
                                                )}
                                            </InputAdornment>
                                        ),
                                    }}
                                />
                                {errorMessage && (
                                    <div style={{ color: "red", marginTop: "5px" }}>
                                        {errorMessage}
                                    </div>
                                )}
                            </Item>
                        </Grid>
                        {/* vehicle body styles */}
                        <Grid item xs={6}>
                            <Item className="page_bg">
                                <CustomAutocomplete
                                    multiple
                                    limitTags={1}
                                    id="checkboxes-tags-demo"
                                    value={selectedBodyTypes}
                                    options={bodyTypedatas} // Use your bodyTypedatas array here
                                    onChange={handleBodyStyleChange} // Handle multiple selections
                                    disableCloseOnSelect
                                    getOptionLabel={(option) => {
                                        // Customize the label based on body type
                                        switch (option.body_type) {
                                            case "Pickup":
                                                return "Pickup Truck";
                                            case "SUV":
                                                return "SUV/Crossover";
                                            default:
                                                return option.body_type; // Default case
                                        }
                                    }}
                                    renderOption={(props, option, { selected }) => {
                                        const { key, ...optionProps } = props;
                                        const isDisabled = disabledBodyTypes.includes(option.body_type); // Disable logic

                                        return (
                                            <li
                                                key={key}
                                                {...optionProps}
                                                style={{
                                                    opacity: isDisabled ? 0.5 : 1,
                                                    pointerEvents: isDisabled ? 'none' : 'auto',
                                                }} // Prevent mouse events if disabled
                                            >
                                                <div
                                                    onClick={(e) => {
                                                        if (isDisabled) {
                                                            e.stopPropagation(); // Prevent any action if the option is disabled
                                                            e.preventDefault();
                                                        }
                                                    }}
                                                >
                                                    <Checkbox
                                                        icon={icon}
                                                        checkedIcon={checkedIcon}
                                                        style={{ marginRight: 8 }}
                                                        checked={selected}
                                                        disabled={isDisabled} // Disable the checkbox to visually show it's not selectable
                                                    />
                                                    {option.body_type === "SUV"
                                                        ? "SUV/Crossover"
                                                        : option.body_type === "Pickup"
                                                            ? "Pickup Truck"
                                                            : option.body_type} {/* Display customized body type */}
                                                </div>
                                            </li>
                                        );
                                    }}
                                    renderInput={(params) => (
                                        <CustomTextField {...params} label="Select Body Type" />
                                    )}
                                />
                            </Item>
                        </Grid>
                        {(!selectedBrands?.length > 0 || !carModel?.length > 0) && (
                            <Grid item xs={12}>
                                <Item className="page_bg" style={{ display: "flex", justifyContent: "center", paddingBottom: 0 }}>
                                    {!selectedBrands?.length > 0 && (
                                        <p className="input_fonts_disabled">N.B.: Please Select a Brand first to View the Vehicle Model List</p>
                                    )}
                                    {selectedBrands?.length > 0 && carModel === '' && (
                                        <p className="input_fonts_disabled">N.B.: Please Select a Model first to View the Vehicle Trim List</p>
                                    )}
                                </Item>
                            </Grid>
                        )}
                        {/* vehicle brands */}
                        <Grid item xs={6}>
                            <Item className="page_bg">
                                <CustomAutocomplete
                                    multiple
                                    limitTags={1}
                                    id="checkboxes-tags-demo"
                                    value={selectedBrands}
                                    options={filteredBrands}
                                    disabled={!selectedBodyTypes?.length > 0 && !brands?.length > 0}
                                    onChange={handleMakeChange}
                                    disableCloseOnSelect
                                    getOptionLabel={(option) => option.make} // Display the make
                                    renderOption={(props, option, { selected }) => {
                                        const { key, ...optionProps } = props;
                                        return (
                                            <li key={key} {...optionProps}>
                                                <Checkbox
                                                    icon={icon}
                                                    checkedIcon={checkedIcon}
                                                    style={{ marginRight: 8 }}
                                                    checked={selected}
                                                />
                                                {option.make}
                                            </li>
                                        );
                                    }}
                                    renderInput={(params) => (
                                        <CustomTextField {...params} label="Select Make" />
                                    )}
                                />
                            </Item>
                        </Grid>
                        {/* vehicle models */}
                        <Grid item xs={6}>
                            <Item className="page_bg">
                                <CustomAutocomplete
                                    multiple
                                    limitTags={1}
                                    id="checkboxes-tags-demo"
                                    options={carModelOptions} // Use filtered model options here
                                    autoHighlight
                                    onChange={handleModelChange}
                                    disabled={!selectedBrands?.length > 0}
                                    value={carModelOptions.filter(option => carModel.includes(option.model))}
                                    getOptionLabel={(option) => option.model} // Access the model property for label
                                    renderOption={(props, option, { selected }) => {
                                        const { key, ...optionProps } = props;
                                        return (
                                            <li key={key} {...optionProps}>
                                                <Checkbox
                                                    icon={icon}
                                                    checkedIcon={checkedIcon}
                                                    style={{ marginRight: 8 }}
                                                    checked={selected}
                                                />
                                                {option.model} {/* Display the model name */}
                                            </li>
                                        );
                                    }}
                                    renderInput={(params) => (
                                        <CustomTextField {...params} label="Select Model" />
                                    )}
                                />
                            </Item>
                        </Grid>
                        {/* vehicle trim list */}
                        <Grid item xs={6}>
                            <Item className="page_bg">
                                <CustomAutocomplete
                                    multiple
                                    limitTags={1}
                                    id="checkboxes-tags-demo"
                                    options={filteredTrimOptions}
                                    onChange={handleTrimsChange}
                                    value={filteredTrimOptions.filter(option => selectedTrims.includes(option.trim))}
                                    disabled={carModel == ''}
                                    disableCloseOnSelect
                                    getOptionLabel={(option) => option.trim}
                                    renderOption={(props, option, { selected }) => {
                                        const { key, ...optionProps } = props;
                                        return (
                                            <li key={key} {...optionProps}>
                                                <Checkbox
                                                    icon={icon}
                                                    checkedIcon={checkedIcon}
                                                    style={{ marginRight: 8 }}
                                                    checked={selected}
                                                />
                                                {option.trim}
                                            </li>
                                        );
                                    }}
                                    renderInput={(params) => (
                                        <CustomTextField {...params} label="Select Trim" />
                                    )}
                                />
                            </Item>
                        </Grid>
                        {/* exterior colors */}
                        <Grid item xs={6}>
                            <Item className="page_bg">
                                <CustomAutocomplete
                                    multiple
                                    limitTags={1}
                                    id="checkboxes-tags-demo"
                                    options={VehicleColors}
                                    onChange={handleExteriorColorChange}
                                    disableCloseOnSelect
                                    getOptionLabel={(option) => option.title}
                                    renderOption={(props, option, { selected }) => {
                                        const { key, ...optionProps } = props;
                                        return (
                                            <li key={key} {...optionProps}>
                                                <Checkbox
                                                    icon={icon}
                                                    checkedIcon={checkedIcon}
                                                    style={{ marginRight: 8 }}
                                                    checked={selected}
                                                />
                                                <LensIcon sx={{ color: option.colorCode, marginRight: 1, border: "1px solid #D3D3D3", borderRadius: "50%" }} />
                                                {option.title}
                                            </li>
                                        );
                                    }}
                                    renderInput={(params) => (
                                        <CustomTextField {...params} label="Select Exterior Colors" />
                                    )}
                                />
                            </Item>
                        </Grid>
                        {/* interior colors */}
                        <Grid item xs={6}>
                            <Item className="page_bg">
                                <CustomAutocomplete
                                    multiple
                                    limitTags={1}
                                    id="checkboxes-tags-demo"
                                    options={VehicleColors}
                                    onChange={handleInteriorColorChange}
                                    disableCloseOnSelect
                                    getOptionLabel={(option) => option.title}
                                    renderOption={(props, option, { selected }) => {
                                        const { key, ...optionProps } = props;
                                        return (
                                            <li key={key} {...optionProps}>
                                                <Checkbox
                                                    icon={icon}
                                                    checkedIcon={checkedIcon}
                                                    style={{ marginRight: 8 }}
                                                    checked={selected}
                                                />
                                                <LensIcon sx={{ color: option.colorCode, marginRight: 1, border: "1px solid #D3D3D3", borderRadius: "50%" }} />
                                                {option.title}
                                            </li>
                                        );
                                    }}
                                    renderInput={(params) => (
                                        <CustomTextField {...params} label="Select Interior Colors" />
                                    )}
                                />
                            </Item>
                        </Grid>
                        {/* fuel type */}
                        <Grid item xs={6}>
                            <Item className="page_bg">
                                <CustomAutocomplete
                                    multiple
                                    limitTags={1}
                                    id="checkboxes-tags-demo"
                                    options={availableFuelTypes}
                                    onChange={handleFuelTypeChange}
                                    disableCloseOnSelect
                                    getOptionLabel={(option) => option}
                                    renderOption={(props, option, { selected }) => {
                                        const { key, ...optionProps } = props;
                                        return (
                                            <li key={key} {...optionProps}>
                                                <Checkbox
                                                    icon={icon}
                                                    checkedIcon={checkedIcon}
                                                    style={{ marginRight: 8 }}
                                                    checked={selected}
                                                />
                                                {option}
                                            </li>
                                        );
                                    }}
                                    renderInput={(params) => (
                                        <CustomTextField {...params} label="Select Fuel Type" />
                                    )}
                                />
                            </Item>
                        </Grid>
                        {/* Min Year Dropdown */}
                        <Grid item xs={6}>
                            <Item className="page_bg">
                                <Typography align="left" style={{ fontWeight: "600" }} >
                                    Select Model Year
                                </Typography>
                            </Item>

                            {/* Min and Max fields side by side */}
                            <Grid container spacing={2}>
                                <Grid item xs={6}>
                                    <Item className="page_bg">
                                        <select
                                            value={minYearInput}
                                            onChange={handleMinYearChange}
                                            style={{
                                                width: "100%",
                                                padding: "12px",
                                                height: "57px", // Adjust this value to increase height
                                                lineHeight: "50px", // Adjust to center-align the text vertically
                                                borderRadius: "4px",
                                                border: `2px solid ${minYearError ? "red" : "#4891FF"}`,
                                                label: "Min",
                                                backgroundColor: "#fff",
                                                fontSize: "16px",
                                            }}
                                        >
                                            {Array.from(
                                                { length: Number(maxYearValue) - Number(minYearValue) },
                                                (_, index) => Number(minYearValue) + index
                                            ).map((year) => (
                                                <option
                                                    key={year}
                                                    value={year}
                                                    style={{ color: "#585858", fontSize: "16px", fontWeight: "400" }}
                                                >
                                                    {year}
                                                </option>
                                            ))}
                                        </select>
                                        {minYearError && (
                                            <Typography color="error" variant="caption">
                                                {minYearError}
                                            </Typography>
                                        )}
                                    </Item>
                                </Grid>

                                <Grid item xs={6}>
                                    <Item className="page_bg">
                                        <select
                                            value={maxYearInput}
                                            onChange={handleMaxYearChange}
                                            style={{
                                                width: "100%",
                                                padding: "12px",
                                                height: "57px", // Adjust this value to increase height
                                                lineHeight: "50px", // Adjust to center-align the text vertically
                                                borderRadius: "4px",
                                                border: `2px solid ${minYearError ? "red" : "#4891FF"}`,
                                                label: "Min",
                                                backgroundColor: "#fff",
                                                fontSize: "16px",
                                            }}
                                        >
                                            {Array.from(
                                                { length: Number(maxYearValue) - Number(minYearValue) },
                                                (_, index) => Number(maxYearValue) - index
                                            ).map((year) => (
                                                <option
                                                    key={year}
                                                    value={year}
                                                    style={{ color: "#585858", fontSize: "16px", fontWeight: "400" }}
                                                >
                                                    {year}
                                                </option>
                                            ))}
                                        </select>
                                        {maxYearError && (
                                            <Typography color="error" variant="caption">
                                                {maxYearError}
                                            </Typography>
                                        )}
                                    </Item>
                                </Grid>
                            </Grid>

                            <Item className="page_bg">
                                <Slider
                                    min={Number(minYearValue)}
                                    max={Number(maxYearValue)}
                                    value={yearValue}
                                    onChange={handleYearChange}
                                    getAriaValueText={(val) => `${val}`}
                                />
                            </Item>
                        </Grid>
                    </Grid>
                </Box>
            </Modal.Body>
            <Modal.Footer className="modal-footer-fixed">
                <StyledButton
                    variant="contained"
                    disabled={isButtonDisabled}
                    onClick={handleSearchClick}
                >
                    <ManageSearchIcon sx={{ mr: 1 }} />
                    continue to search
                </StyledButton>
            </Modal.Footer>
        </Modal>
    );
};

// Update propTypes for GenericAdvancedSearchModalWeb
GenericAdvancedSearchModalWeb.propTypes = {
    open: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    handleExteriorColorChange: PropTypes.func.isRequired,
    key: PropTypes.string.isRequired,
    selectedDistance: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number
    ]).isRequired,
    handleDistanceChange: PropTypes.func.isRequired,
    handleSearchClick: PropTypes.func.isRequired,
    carModelOptions: PropTypes.arrayOf(
        PropTypes.shape({
            model: PropTypes.string.isRequired
        })
    ).isRequired,
    handleModelChange: PropTypes.func.isRequired,
    carModel: PropTypes.arrayOf(PropTypes.string).isRequired,
    filteredTrimOptions: PropTypes.arrayOf(
        PropTypes.shape({
            trim: PropTypes.string.isRequired
        })
    ).isRequired,
    handleTrimsChange: PropTypes.func.isRequired,
    handleInteriorColorChange: PropTypes.func.isRequired,
    handleFuelTypeChange: PropTypes.func.isRequired,
    handleZipCodeChange: PropTypes.func.isRequired,
    isButtonDisabled: PropTypes.bool.isRequired,
    errorMessage: PropTypes.string,
    zipCode: PropTypes.string.isRequired,
    clearZipCode: PropTypes.func.isRequired,
    priceValue: PropTypes.array.isRequired,
    setPriceValue: PropTypes.func.isRequired,
    minMaxPrice: PropTypes.object.isRequired,
    setMinMaxPrice: PropTypes.func.isRequired,
    setMinYearError: PropTypes.func.isRequired,
    maxYearError: PropTypes.string,
    setMaxYearError: PropTypes.func.isRequired,
    minYearValue: PropTypes.number.isRequired,
    maxYearValue: PropTypes.number.isRequired,
    yearValue: PropTypes.array.isRequired,
    setYearValue: PropTypes.func.isRequired,
    minInput: PropTypes.number.isRequired,
    maxInput: PropTypes.number.isRequired,
    setMinInput: PropTypes.func.isRequired,
    setMaxInput: PropTypes.func.isRequired,
    minPriceError: PropTypes.string,
    maxPriceError: PropTypes.string,
    timeoutId: PropTypes.number,
    setMinPriceError: PropTypes.func.isRequired,
    setMaxPriceError: PropTypes.func.isRequired,
    minPriceValue: PropTypes.number.isRequired,
    maxPriceValue: PropTypes.number.isRequired,
    setMinPriceValue: PropTypes.func.isRequired,
    setMaxPriceValue: PropTypes.func.isRequired,
    handleBrandClick: PropTypes.func.isRequired,
    brands: PropTypes.array.isRequired,
    isModalButtonDisabled: PropTypes.bool.isRequired,
    setIsModalButtonDisabled: PropTypes.func.isRequired,
    setMinYearInput: PropTypes.func.isRequired,
    setMaxYearInput: PropTypes.func.isRequired,
    minYearInput: PropTypes.number.isRequired,
    maxYearInput: PropTypes.number.isRequired,
    selectedBodyTypes: PropTypes.arrayOf(
        PropTypes.shape({
            id: PropTypes.number.isRequired,
            name: PropTypes.string.isRequired,
            body_type: PropTypes.string.isRequired
        })
    ).isRequired,
    selectedBrands: PropTypes.arrayOf(
        PropTypes.shape({
            id: PropTypes.number.isRequired,
            make_id: PropTypes.number.isRequired,
            name: PropTypes.string.isRequired
        })
    ).isRequired,
    selectedModels: PropTypes.arrayOf(
        PropTypes.shape({
            id: PropTypes.number.isRequired,
            make_id: PropTypes.number.isRequired,
            name: PropTypes.string.isRequired
        })
    ).isRequired,
    bodyTypedatas: PropTypes.arrayOf(
        PropTypes.shape({
            id: PropTypes.number.isRequired,
            name: PropTypes.string.isRequired
        })
    ).isRequired,
    handleMakeChange: PropTypes.func.isRequired,
    handleBodyStyleChange: PropTypes.func.isRequired,
    carLookingOptions: PropTypes.arrayOf(
        PropTypes.shape({
            id: PropTypes.number.isRequired,
            name: PropTypes.string.isRequired
        })
    ).isRequired,
    handleCarLookingChange: PropTypes.func.isRequired,
    selectedCarLookingOption: PropTypes.shape({
        id: PropTypes.number.isRequired,
        name: PropTypes.string.isRequired
    }).isRequired,
    userMaxPrice: PropTypes.oneOfType([
        PropTypes.number,
        PropTypes.string
    ]).isRequired,
    invVehicleMinPrice: PropTypes.oneOfType([
        PropTypes.number,
        PropTypes.string
    ]).isRequired,
    selectedTrims: PropTypes.array.isRequired,
    minYear: PropTypes.number.isRequired,
    maxYear: PropTypes.number.isRequired,
    minYearError: PropTypes.string,
    setTimeoutId: PropTypes.func.isRequired,
    make: PropTypes.array.isRequired,
    setMakes: PropTypes.func.isRequired,
    loadingLocation: PropTypes.bool.isRequired,
    disabledBodyTypes: PropTypes.bool.isRequired,
    setIsButtonDisabled: PropTypes.func.isRequired
};

export default GenericAdvancedSearchModalWeb;
