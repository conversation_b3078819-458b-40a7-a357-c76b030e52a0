import PropTypes from "prop-types";
import {
    <PERSON>po<PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>m,
} from "@mui/material";
import { <PERSON><PERSON>, Button } from "react-bootstrap";
import { styled } from "@mui/material/styles";
import Grid from "@mui/material/Grid";
import ButtonBase from "@mui/material/ButtonBase";
import { ImageBaseURL } from "../../../config";
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

const CarBrandBox = styled(({ ...other }) => <ButtonBase {...other} />)(({ theme, isSelected }) => ({
    width: 150,
    height: 80,
    fontSize: 12,
    display: 'flex',
    flexDirection: 'row', // Arrange items in a row
    alignItems: 'center', // Center content vertically
    justifyContent: 'space-between', // Space between image and text
    padding: theme.spacing(1), // Add padding for spacing
    borderRadius: theme.shape.borderRadius,
    border: isSelected ? '2px solid #4891FF' : '1px solid #4891FF', // Highlight selected
    backgroundColor: isSelected ? '#e3f2fd' : '#ffffff', // Light blue background when selected
    boxShadow: '0px 6px 12px rgba(0, 0, 0, 0.2)',
    transition: 'transform 0.1s, box-shadow 0.1s',
    position: 'relative', // To position the tick overlay
    '&:hover': {
        bgcolor: 'primary.dark',
        transform: 'translateY(-2px)',
        boxShadow: '0px 8px 16px rgba(0, 0, 0, 0.3)',
    },
    '&:active': {
        bgcolor: 'primary.darker',
        transform: 'translateY(2px)',
        boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.1)',
    },
    '& img': {
        width: '45%',  // Adjust width to fit nicely on the left
        height: '90%', // Maintain aspect ratio
        objectFit: 'contain', // Ensure the image fits well within its container
    },
    '& .MuiTypography-root': {
        marginLeft: theme.spacing(1), // Space between image and text
        textAlign: 'center', // Align text to the right
        overflow: 'hidden', // Ensure text does not overflow
        textOverflow: 'ellipsis', // Add ellipsis for long text
        flex: 1, // Take up remaining space
    },
    '& .tick-overlay': {
        position: 'absolute',
        top: 0,
        right: 0,
        width: '100%',
        height: '100%',
        backgroundColor: 'rgba(72, 145, 255, 0.5)', // Semi-transparent overlay
        display: isSelected ? 'flex' : 'none',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        fontSize: 24,
        zIndex: 1,
    },
}));

const ShowBrandsModal = ({
    show,
    onClose,
    brands,
    isBigScreen,
    selectedBrands,
    handleBrandClick,
}) => {
    const fallbackImg = `${ImageBaseURL}carBrandLogos/no-image.jpg`;
    return (
        <Modal centered size="lg" show={show} onHide={onClose}>
            <Modal.Header className="modal-header-fixed" style={{ justifyContent: "center" }} closeButton>
                <Modal.Title>Select Upto 3 Brands</Modal.Title>
            </Modal.Header>
            <Modal.Body className="modal-body-scrollable">
                <Grid container spacing={2} justifyContent="center">
                    {brands.map((brand) => (
                        <Grid item md={4} key={brand.id} sx={{ display: 'flex', justifyContent: 'center' }}>
                            <CarBrandBox
                                isBigScreen={isBigScreen}
                                isSelected={selectedBrands.includes(brand)}
                                onClick={() => handleBrandClick(brand)}
                            >
                                <img
                                    src={`${ImageBaseURL}carBrandLogos/${brand.image_name}` || fallbackImg}
                                    alt={brand.make}
                                    onError={(e) => {
                                        e.target.src = fallbackImg;
                                    }}
                                />
                                <Tooltip TransitionComponent={Zoom} title={brand.make.toUpperCase()} arrow>
                                    <Typography variant="subtitle2" sx={{ fontWeight: "bold" }}>
                                        {brand.make.toUpperCase()}
                                    </Typography>
                                </Tooltip>
                                <div className="tick-overlay">
                                    <CheckCircleIcon />
                                </div>
                            </CarBrandBox>
                        </Grid>
                    ))}
                </Grid>
            </Modal.Body>
            <Modal.Footer className="modal-footer-fixed">
                <Button
                    variant="btn btn-danger"
                    onClick={onClose}
                    className="fw-600"
                    style={{ width: "25%" }}
                >
                    CLOSE
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

ShowBrandsModal.propTypes = {
    show: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    brands: PropTypes.array.isRequired,
    isBigScreen: PropTypes.bool.isRequired,
    selectedBrands: PropTypes.array.isRequired,
    handleBrandClick: PropTypes.func.isRequired,
};

export default ShowBrandsModal;
