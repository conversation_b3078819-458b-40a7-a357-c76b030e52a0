import { useMediaQuery } from 'react-responsive'
import HomeMobile from './homeMobile';
import HomeWeb from './homeWeb';
import MasterLayoutWithoutFrames from '../../../components/layouts/masterLayoutWithoutFrames';

function Home() {
  const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' })
  return (
    <MasterLayoutWithoutFrames>
      {isTabletOrMobile ? <HomeMobile /> : <HomeWeb />}
    </MasterLayoutWithoutFrames>
  );
}

export default Home;