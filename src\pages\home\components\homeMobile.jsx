import { useState, useEffect } from 'react';
import { styled } from "@mui/material/styles";
import { useMediaQuery } from 'react-responsive';
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import EastIcon from '@mui/icons-material/East';
import { useNavigate } from 'react-router-dom';
import TermsModal from "../modals/terms&conditionsModal";
import { ImageBaseURL } from '../../../config';

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const StyledButtonAccept = styled(({ ...other }) => <Button {...other} />)(({ isMobile }) => ({
  borderRadius: "10px",
  width: isMobile ? "80%" : "50%",
  fontSize: "14px",
  fontWeight: "600",
  padding: "14px",
  backgroundColor: "#4891FF",
  "&:hover": {
    backgroundColor: "primary",
  },
}));

const HomeMobile = () => {
  const navigate = useNavigate();
  const isMobile = useMediaQuery({ maxWidth: 767 });
  const [showModal, setShowModal] = useState(false);
  const handleShow = () => setShowModal(true);
  const handleClose = () => setShowModal(false);

  const handleRedirect = () => {
    navigate('/send-otp');
  };

  useEffect(() => {
    // Check if token is available
    const token = localStorage.getItem("token");
    if (token) {
      navigate("/landing");
    }
  }, [navigate]);

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Grid container spacing={2} sx={{ padding: "15px 10px" }}>
        <Grid item xs={12}>
          <Item className='page_bg'>
            <img
              src={ImageBaseURL + "fp_logo.png"}
              alt="Fastpass"
              style={isMobile ? { maxWidth: "75%", maxHeight: "75%" } : { maxWidth: "55%", maxHeight: "55%" }}
            />
          </Item>
        </Grid>
        <Grid item xs={12} style={{ paddingTop: "0px" }}>
          <Item className='page_bg'>
            <img
              src={ImageBaseURL + "index_img.png"}
              alt="indexImage"
              style={isMobile ? { maxWidth: "80%", maxHeight: "80%" } : { maxWidth: "60%", maxHeight: "60%" }}
            />
          </Item>
        </Grid>
        <Grid item xs={12} style={{ paddingTop: "0px" }}>
          <Item className='page_bg'>
            <StyledButtonAccept
              isMobile={isMobile}
              variant="contained"
              onClick={handleRedirect}
            >
              <span>ACCEPT & CONTINUE</span>
              <EastIcon sx={{ marginLeft: "20px" }} fontSize="large" />
            </StyledButtonAccept>
          </Item>
        </Grid>
        <Grid item xs={12}>
          <Item className='page_bg'>
            <Typography
              variant="subtitle2"
              gutterBottom
              sx={{ fontWeight: "800", fontSize: "14px" }}
              mx={2}
            >
              By Clicking &quot;Accept &amp; Continue&quot;, You Agree to Our
              <br />
              <button
                className="fs-6 fw-bold btn btn-link"
                style={{ fontSize: "14px" }}
                onClick={handleShow}
              >
                Terms of Service
              </button>
            </Typography>
            <TermsModal
              show={showModal}
              onHide={handleClose}
              src="https://www.loanify.net/loanify-privacy-policy"
            />
          </Item>
        </Grid>
      </Grid>
    </Box >
  );
};

export default HomeMobile;
