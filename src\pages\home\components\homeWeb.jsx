import { useState, useEffect } from 'react';
import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import EastIcon from '@mui/icons-material/East';
import { useNavigate } from 'react-router-dom';
import TermsModal from "../modals/terms&conditionsModal";
import Tooltip from '@mui/material/Tooltip';
import Zoom from '@mui/material/Zoom';
import { ImageBaseURL } from '../../../config';

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const StyledButtonAccept = styled(Button)({
  borderRadius: "10px",
  width: "50%",
  fontWeight: "600",
  padding: "15px",
  backgroundColor: "#4891FF",
  "&:hover": {
    backgroundColor: "primary",
  },
});

const HomeWeb = () => {
  const navigate = useNavigate();
  const [showModal, setShowModal] = useState(false);
  const handleShow = () => setShowModal(true);
  const handleClose = () => setShowModal(false);

  const handleRedirect = () => {
    navigate('/send-otp');
  };

  useEffect(() => {
    // Check if token is available
    const token = localStorage.getItem("token");
    if (token) {
      navigate("/landing");
    }
  }, [navigate]);

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Grid container spacing={2} sx={{ padding: "20px 0px" }}>
        <Grid item sm={12}>
          <Item className='page_bg'>
            <img
              src={ImageBaseURL + "fp_logo.png"}
              alt="Fastpass"
              style={{ maxWidth: "30%", maxHeight: "30%" }}
            />
          </Item>
        </Grid>
        <Grid item xs={6}>
          <Item className='page_bg'>
            <img
              src={ImageBaseURL + "index_img.png"}
              alt="IndexImage"
              style={{ maxWidth: "70%", maxHeight: "70%" }}
            />
          </Item>
        </Grid>
        <Grid item xs={6} sx={{ alignSelf: "center" }} style={{ marginTop: '6%' }}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Item className='page_bg'>
                <Tooltip TransitionComponent={Zoom} title="Accept & Continue" arrow>
                  <StyledButtonAccept
                    variant="contained"
                    onClick={handleRedirect}
                  >
                    <span style={{ fontSize: "18px" }}>ACCEPT & CONTINUE</span>
                    <EastIcon sx={{ marginLeft: "20px" }} fontSize="large" />
                  </StyledButtonAccept>
                </Tooltip>
              </Item>
            </Grid>
            <Grid item xs={12}>
              <Item className='page_bg'>
                <Typography
                  variant="subtitle2"
                  gutterBottom
                  sx={{ fontWeight: "800", lineHeight: "1" }}
                >
                  By Clicking &quot;Accept &amp; Continue&quot;, You Agree to Our
                  <br />
                  <Tooltip TransitionComponent={Zoom} title="View Terms of Service" arrow>
                    <button
                      className="fs-6 fw-bold btn btn-link"
                      onClick={handleShow}
                    >
                      Terms of Service
                    </button>
                  </Tooltip>
                </Typography>
                <TermsModal
                  show={showModal}
                  onHide={handleClose}
                  src="https://www.loanify.net/loanify-privacy-policy"
                />
              </Item>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};
export default HomeWeb;
