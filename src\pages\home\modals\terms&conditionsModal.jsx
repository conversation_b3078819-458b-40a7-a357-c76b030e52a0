import { Mo<PERSON>, But<PERSON> } from "react-bootstrap";
import PropTypes from "prop-types";
import PrivacyPolicy from "../../privacyPolicy";

const termsModal = ({ show, onHide }) => {
  return (
    <Modal show={show} onHide={onHide} centered>
      <Modal.Header className="modal-header-fixed" closeButton>
        <Modal.Title>Terms of Service</Modal.Title>
      </Modal.Header>
      <Modal.Body className="modal-body-scrollable">
        <PrivacyPolicy />
      </Modal.Body>
      <Modal.Footer className="modal-footer-fixed">
        <Button variant="btn btn-danger" onClick={onHide} className="fw-600" style={{ width: "25%" }}>
          CLOSE
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
termsModal.propTypes = {
  show: PropTypes.bool.isRequired,
  onHide: PropTypes.func.isRequired
};
export default termsModal;
