import { useEffect, useState, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useDispatch } from "react-redux";
import { getUser } from "../../../store/apps/user";
import { pageMetaData } from "../../../../data/pageMetaData";
import { pageComponentData } from "../../../../data/componentMetaData";
import { useUserData } from "../../UserDataContext";
import { useModal } from '../../../context/ModalContext'; // Import the modal context

export const useLandingApi = () => {
  const [memberInfo, setMemberInfo] = useState({
    member_first_name: "",
    member_last_name: "",
    loan_id: [],
    preapproved_expire: "",
    selectedTerm: "",
    preapproved_pct: "",
    calculate_emi: "",
    cuLogo: "",
  });

  const { setUserData } = useUserData();
  const { userData } = useUserData();
  const [loading, setLoading] = useState(true);
  const apiCallCompletedRef = useRef(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation(); // Get current URL
  const [pageId, setPageId] = useState(null); // State to store the page_id
  const [componentMetaData, setComponentMetaData] = useState(null); // State to store the componentMetaData
  const [chatModal, setChatModal] = useState(false);
  const [urlMemberId, setUrlMemberId] = useState(null);
  const [urlChatId, setUrlChatId] = useState(null);
  const [isUrlChecked, setIsUrlChecked] = useState(false); // Ensures we check URL before API call
  const { setShowChatSelectionModal } = useModal(); // Get the function to set modal visibility


  useEffect(() => {
    // Extract and decode member_id from URL (only if query parameters exist)
    const getQueryParam = (param) => {
      const urlParams = new URLSearchParams(location.search);
      return urlParams.get(param);
    };
  
    const encodedData = getQueryParam("data");
  
    if (encodedData) {
      try {
        const decodedData = JSON.parse(atob(decodeURIComponent(encodedData)));
        if (decodedData.member_id) {
          setUrlMemberId(decodedData.member_id); // Store extracted member_id
          setUrlChatId(decodedData.chat_id); // Store extracted chat_id
        } else {
          throw new Error("member_id not found");
        }
      } catch (error) {
        console.error("Invalid encoded data", error);
        navigate("/access-denied"); // Redirect if decoding fails
      }
    }
    setIsUrlChecked(true); // Mark that URL check is done
  }, [location.search, navigate]); // Runs only when URL changes
  

  useEffect(() => {
    if (!isUrlChecked) return;
    const fetchUserData = async () => {
      const number = localStorage.getItem("ph_number");
      const token = localStorage.getItem("token");
      if (!number || !token) {
        navigate("/");
        return;
      }

      try {
        dispatch(getUser({ phone_number: number, authToken: token })).then(
          (response) => {
            if (
              response &&
              response.meta &&
              response.meta.requestStatus === "fulfilled"
            ) {
              const userData = response.payload;

              // Save user data in context
              setUserData(userData);
              // If URL contains a redirection parameter, validate the member_id
              if (urlMemberId && response.payload.member_id !== urlMemberId) {
                navigate("/access-denied"); // Redirect if IDs do not match
                return;
              }

              if (urlChatId && urlChatId === '1234') {
                setShowChatSelectionModal(true); // Open the modal if member IDs match
              }

              const cardnoString = userData.loan_id.toString();
              const chunkedArray = [];
              for (let i = 0; i < cardnoString.length; i += 4) {
                chunkedArray.push(cardnoString.slice(i, i + 4));
              }

              const date = new Date(userData.preapproved_expire);
              const month = (date.getMonth() + 1).toString().padStart(2, "0");
              const day = date.getDate().toString().padStart(2, "0");
              const year = date.getFullYear().toString().slice(-2);
              const formattedDate = `${month}/${day}/${year}`;

              setMemberInfo({
                member_first_name: userData.member_first_name,
                member_last_name: userData.member_last_name,
                selectedTerm: userData.term,
                loan_id: chunkedArray,
                preapproved_expire: formattedDate,
                preapproved_amount: userData.preapproved_max_amt,
                cuLogo: userData.logo_url,
              });
              localStorage.setItem("user_id", userData.member_id);
              localStorage.setItem("member_type", userData.member_type);
              apiCallCompletedRef.current = true;
              setLoading(false);
            } else {
              handleFailedResponse();
            }
          }
        );
      } catch (error) {
        console.error("Error fetching user data:", error);
        handleFailedResponse();
      }
    };

    const handleFailedResponse = () => {
      localStorage.removeItem("token");
      localStorage.removeItem("ph_number");
      localStorage.removeItem("verified");
      navigate("/");
    };

    if (!apiCallCompletedRef.current) {
      fetchUserData();
    }
    // Include dispatch and navigate in the dependency array
  }, [dispatch, navigate, apiCallCompletedRef, isUrlChecked, setShowChatSelectionModal]);

  useEffect(() => {
    // Get the page_id from pageMetaData based on slug
    const slug = "landing"; // Replace this with the desired slug
    const pageData = pageMetaData.find((page) => page.slug === slug);
    if (pageData) {
      setPageId(pageData.id);
    }

    // Get Page Componenet Meta Data
    const matchingComponents = pageComponentData.filter(
      (pageComponent) => pageComponent.page_id === pageId
    );

    if (matchingComponents.length > 0) {
      setComponentMetaData(matchingComponents);
    }
  }, [pageId]);

  let userMemberType = Number(userData?.member_type); // identifying member type
  /**
   * A function that returns the tooltip message based on the user's member type
   * @returns {string} The tooltip message
   */
  const getTooltipMessage = () => {
    // Switch statement to get the tooltip message based on the user's member type
    switch (userMemberType) {
      // Member type 1 - Chat with our Credit Union support
      case 1:
        return "Chat with our Credit Union support";
      // Member type 2 - Chat with our Credit Union support
      case 2:
        return "Chat with our Credit Union support";
      // Member type 3 - This link will take you to a Credit Union Application portal
      case 3:
        return "This link will take you to a Credit Union Application portal";
      // Default - Chat with our Credit Union support
      default:
        return "Chat with our Credit Union support";
    }
  };

  const cuLoansURL = import.meta.env.VITE_CU_VEHICLE_LOANS_URL; // Credit Union Loans URL
  /**
   * A function that redirects the user to a specific URL based on their member type
   * @param {number} userMemberType - The member type of the user
   */
  const redirectionURL = () => {
    switch (userMemberType) {
      // If the user is a member type 2, open the chat modal
      case 2:
        setChatModal(true);
        break;
      // If the user is a member type 3, redirect them to the vehicle loans page
      case 3:
        window.open(cuLoansURL, "_blank", "noopener,noreferrer");
        break;
      // For all other member types, redirect them to the CU conversations page
      default:
        navigate('/cu-conversations');
    }
  };

  return {
    memberInfo,
    loading,
    pageId,
    componentMetaData,
    getTooltipMessage,
    redirectionURL,
    chatModal,
    setChatModal,
    userMemberType,
  };
};

export default useLandingApi;
