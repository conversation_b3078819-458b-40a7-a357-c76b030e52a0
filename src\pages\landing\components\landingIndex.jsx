import { useMediaQuery } from 'react-responsive'
import LandingMobile from './landingMobile';
import LandingWeb from './landingWeb';
import MasterLayout from '../../../components/layouts/masterLayout';

function LandingIndex() {
    const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' })
    return (
        <MasterLayout>
            {isTabletOrMobile ? <LandingMobile /> : <LandingWeb />}
        </MasterLayout>
    );
}

export default LandingIndex;