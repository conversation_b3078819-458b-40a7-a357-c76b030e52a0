import { styled } from "@mui/material/styles";
import { useMediaQuery } from "react-responsive";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import Stack from '@mui/material/Stack';
import { ImageBaseURL } from "../../../config";
import Icon from "../../../icon";
import useLandingApi from "../api/landingApi";
import loaderGif from '../../../assets/gifs/loader.gif';
import { useNavigate } from "react-router-dom";
import SearchConfirmationModal from "../../landing/modals/searchConfirmationModal";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { callComponentActionApi } from "../../../util/callComponenetActionApi";
import MemberTwoChatModal from "../../../components/drawer/modals/memberTwoChatModal";
import { useGlobalTutorialModal } from "../../../context/TutorialModalContext";
import { hasSeenTutorial, shouldShowTutorial } from "../../../context/TutorialStorage";
import Tooltip from "@mui/material/Tooltip";

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

// eslint-disable-next-line no-unused-vars
const StyledButton = styled(({ isMobile, ...other }) => <Button {...other} />)(
  ({ isMobile }) => ({
    borderRadius: "10px",
    width: isMobile ? "80%" : "50%",
    fontSize: "14px",
    fontWeight: "600",
    padding: "10px",
    backgroundColor: "#4891FF",
    "&:hover": {
      backgroundColor: "primary",
    },
    "&.Mui-disabled": {
      backgroundColor: "#4891FF",
      color: "#fff",
      opacity: 0.8,
    },
  })
);

// eslint-disable-next-line no-unused-vars
const StyledButtonQA = styled(({ isMobile, ...other }) => <Button {...other} />)(
  ({ isMobile }) => ({
    borderRadius: "10px",
    width: isMobile ? "80%" : "50%",
    fontSize: "14px",
    fontWeight: "600",
    padding: "10px",
    backgroundColor: "#28A744",
    "&:hover": {
      backgroundColor: "#28C744",
    },
    "&.Mui-disabled": {
      backgroundColor: "#4891FF",
      color: "#fff",
      opacity: 0.8,
    },
  })
);

const LandingMobile = () => {
  const isMobile = useMediaQuery({ maxWidth: 767 });
  const { memberInfo, loading, componentMetaData, redirectionURL, chatModal, setChatModal, userMemberType } = useLandingApi();
  const navigate = useNavigate();
  const token = localStorage.getItem("token") ? true : false;
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const isGenericSearchShow = import.meta.env.VITE_IS_GENERIC_SEARCH_SHOW;
  const handleConfirmationModalShow = (slug) => {
    setCurrentSlug(slug); // Save the slug for use in the modal
    setShowConfirmationModal(true);
  };
  const handleConfirmationModalClose = () => setShowConfirmationModal(false);
  const [currentSlug, setCurrentSlug] = useState(null);
  const dispatch = useDispatch();
  const [nameTooltipOpen, setNameTooltipOpen] = useState(false);
  const handleNameClick = () => {
    setNameTooltipOpen(true);
    setTimeout(() => setNameTooltipOpen(false), 1500);
  };
  if (!token) {
    navigate("/");
  }
  const handleButtonClick = async (slug, route) => {
    // Navigate to the desired route
    navigate(route);
    try {
      // Find the component by slug
      const component = componentMetaData.find((comp) => comp.slug === slug);
      if (!component) {
        console.error(`Component with slug "${slug}" not found.`);
        return;
      }

      // Call the API with the component ID
      await callComponentActionApi(dispatch, component.id);
    } catch (error) {
      console.error("Error handling button click:", error);
    }
  };

  useEffect(() => {
    let isMounted = true; // Add a flag to check if the component is still mounted

    if (!loading && isMounted) {
      try {
        const component = componentMetaData.find((comp) => comp.slug === "landing" && comp.type === "url");
        if (!component) {
          console.error(`Component with slug "my-garage" not found.`);
          return;
        }
        // Call the API with the component ID
        callComponentActionApi(dispatch, component.id);
      } catch (error) {
        console.error("Error handling button click:", error);
      }
    }

    // Cleanup function to reset the flag when the component is unmounted
    return () => {
      isMounted = false;
    };
  }, [loading, componentMetaData]);

  // for tutorial
  const { openTutorialModal } = useGlobalTutorialModal();
  useEffect(() => {
    const checkAndShowTutorial = async () => {
      if (loading) return; // Don't proceed if loading

      const localFlag = hasSeenTutorial("landing_commandCenter");
      if (localFlag) return; // Already seen locally, skip

      const shouldShow = await shouldShowTutorial({
        tutorialKey: "landing_commandCenter",
        dispatch,
      });

      if (shouldShow) {
        openTutorialModal({
          bodyImageSrc: ImageBaseURL + "tutorials/images/landing-tutorial-feature-image.png",
          additionalTopText: (
            <span>Click on</span>
          ),
          mainText: (
            <span>Command Center</span>
          ),
          subText: (
            <span>Verify Your Loan Details</span>
          ),
          audioSrc: ImageBaseURL + "tutorials/audios/landing-audio.mp3"
        });
      }
    };

    checkAndShowTutorial();
  }, [loading]);

  return (
    <Box sx={{ flexGrow: 1, margin: "8vh 0vh 5vh 0vh" }}>
      {loading ? (
        <Box
          sx={{
            flexGrow: 1,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Stack alignItems="center" justifyContent="center" mt={16}>
            <img className='page_bg' src={loaderGif} alt="Loading..." style={{ width: '200px', height: '200px' }} />
            <span style={{ fontWeight: '600' }}>Loading Details...</span>
          </Stack>
        </Box>
      ) : (
        <Box sx={{ flexGrow: 1, margin: "8vh 0vh 5vh 0vh" }}>
          {isMobile ? (
            <Grid container spacing={2} sx={{ padding: "5px 0px 0px 0px" }}>
              <Grid item xs={12}>
                <Item className="page_bg">
                  <img
                    alt="cu Logo"
                    src={memberInfo.cuLogo}
                    style={
                      isMobile
                        ? { maxWidth: "50%", maxHeight: "50%" }
                        : { maxWidth: "35%", maxHeight: "35%" }
                    }
                  />
                </Item>
              </Grid>
              <Grid item xs={12} style={{ paddingTop: "10px" }}>
                <Grid item sm={6}>
                  <Item className="page_bg">
                    <div className="card-main-mobile page_bg">
                      <div className="card-inner">
                        <div className="front_mobile">
                          <img
                            src={ImageBaseURL + "card_image.png"}
                            className="map-img"
                            alt="Card"
                          />
                          <div
                            className="card-row card_font_mobile"
                            style={{ marginTop: "30%" }}
                          >
                            <div className="loan-ids-grid-mb">
                              <span>{memberInfo.loan_id[0]}</span>
                              <span>{memberInfo.loan_id[1]}</span>
                              <span>{memberInfo.loan_id[2]}</span>
                              <span>{memberInfo.loan_id[3]}</span>
                            </div>
                          </div>
                          <div
                            className="row text-start card-holder-mobile fw-400"
                            style={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                            }}
                          >
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                flexGrow: 1,
                              }}
                            >
                              <span
                                onClick={handleNameClick}
                                style={{
                                  whiteSpace: "nowrap",
                                  overflow: "hidden",
                                  textOverflow: "ellipsis",
                                  zIndex: 2,
                                  cursor: "pointer",
                                  width: "35%",
                                }}
                              >
                                <Tooltip
                                  title={`${memberInfo.member_first_name} ${memberInfo.member_last_name}`}
                                  open={nameTooltipOpen}
                                  onClose={() => setNameTooltipOpen(false)}
                                  disableFocusListener
                                  disableHoverListener
                                  disableTouchListener
                                  placement="top"
                                >
                                  <span style={{ pointerEvents: "none" }}>
                                    {memberInfo.member_first_name +
                                      " " +
                                      memberInfo.member_last_name}
                                  </span>
                                </Tooltip>
                              </span>
                            </div>
                            <div
                              style={{
                                display: "flex",
                                flexDirection: "column",
                                textAlign: "center",
                                marginTop: "-15px",
                                marginLeft: "25px",
                              }}
                            >
                              <span>VALID THRU:</span>
                              <span>{memberInfo.preapproved_expire}</span>
                            </div>
                            <div
                              style={{
                                display: "flex",
                                flexDirection: "column",
                                textAlign: "left",
                              }}
                            >
                              <span>
                                AMOUNT:&nbsp;$ {memberInfo.preapproved_amount}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Item>
                </Grid>
              </Grid>
              <Grid item xs={12} style={{ paddingTop: "8px" }}>
                <Grid item sm={12}>
                  <Item className="page_bg">
                    <StyledButton isMobile={isMobile} variant="contained" onClick={() => navigate('/command-center')}>
                      <Icon icon="loan-interest-time" size={40} />
                      <span className="btn_class_landing_mobile">
                        COMMAND CENTER
                      </span>
                    </StyledButton>
                  </Item>
                </Grid>
                <Grid item sm={12}>
                  <Item className="page_bg">
                    <StyledButton
                      isMobile={isMobile}
                      variant="contained"
                      onClick={isGenericSearchShow === "true"
                        ? () => handleConfirmationModalShow("shop-local-inventory")
                        : () => handleButtonClick("shop-local-inventory", "/specific-search")
                      }
                    >
                      <Icon icon="phone-in-hand" size={40} />
                      <span className="btn_class_landing_mobile">
                        SHOP LOCAL INVENTORY
                      </span>
                    </StyledButton>
                  </Item>
                </Grid>
                <Grid item sm={12}>
                  <Item className="page_bg">
                    <StyledButtonQA isMobile={isMobile} variant="contained"
                      onClick={redirectionURL}
                    >
                      <svg fill="#fff" version="1.1" id="earth" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" width="40px" height="40px" viewBox="0 0 256 202" enableBackground="new 0 0 256 202" xmlSpace="preserve" stroke="#fff"> <g id="SVGRepo_bgCarrier" strokeWidth="0" /><g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" /><g id="SVGRepo_iconCarrier"> <path d="M189.103,2.162c-35.842,0-64.897,29.055-64.897,64.897c0,35.841,29.055,64.897,64.897,64.897S254,102.9,254,67.059 C254,31.217,224.945,2.162,189.103,2.162z M189.457,111.449c-5.808,0-10.551-4.743-10.551-10.551 c0-5.842,4.743-10.551,10.551-10.551c5.808,0,10.551,4.743,10.551,10.551C200.008,106.706,195.265,111.449,189.457,111.449z M214.854,60.998c-1.512,2.234-4.158,4.88-8.317,7.904l-4.158,3.024c-2.199,1.856-3.712,3.712-4.502,6.014 c-0.447,1.512-0.79,3.78-0.79,6.805h-15.431c0.344-6.392,0.722-10.894,1.856-13.197c0.722-2.303,3.368-5.327,7.492-8.317 l3.815-3.368c1.512-1.065,2.577-2.234,3.368-3.368c1.512-1.924,2.302-4.158,2.302-6.805c0-3.024-1.134-5.602-2.646-7.904 c-1.512-2.234-4.537-3.368-9.039-3.368c-4.502,0-7.526,1.478-9.382,4.502c-1.512,2.234-2.646,5.258-2.646,7.904 c0,0.447,0,0.447,0,0.79c-0.447,4.158-3.815,7.492-8.317,7.492c-4.502,0-7.973-3.368-8.317-7.526c0,0,0-1.856,0-2.646 c1.134-9.451,4.949-16.187,11.306-20.345c4.537-2.646,10.207-4.158,16.565-4.158c8.626,0,15.774,1.856,21.445,6.014 c5.602,4.158,8.626,10.173,9.21,18.146C218.669,53.472,217.501,57.63,214.854,60.998z M229.382,148.01l-59.641,13.668 c-0.414,2.899-1.657,6.006-3.52,8.491c-3.52,5.384-8.905,9.319-15.117,10.976c-6.213,1.45-12.839,0.414-18.224-3.313l-51.358-32.72 c-2.485-1.45-2.899-4.349-1.45-6.627c1.45-2.278,4.349-2.899,6.42-1.45l51.565,32.72c6.834,4.142,15.739,2.278,20.087-4.349 c4.349-6.627,2.278-15.739-4.349-20.087l-64.197-40.589c-10.561-6.627-22.78-11.804-34.376-6.006L2,128.958l0.207,58.813 l37.897-26.3c6.006-1.45,12.632-0.207,18.224,3.313l44.731,28.164c10.976,6.834,24.436,8.491,36.033,5.384l96.917-22.158 c7.662-1.657,12.839-9.319,10.976-17.602C244.913,151.117,237.251,146.354,229.382,148.01z" /> </g></svg>
                      <span className="btn_class_landing_mobile">Loan Question? ASK CU</span>
                    </StyledButtonQA>
                  </Item>
                </Grid>
              </Grid>
            </Grid>
          ) : (
            <Grid container spacing={2} sx={{ padding: "10px 0px 0px 0px" }}>
              <Grid item xs={12}>
                <Item className="page_bg">
                  <img
                    alt="cu Logo"
                    src={memberInfo.cuLogo}
                    style={{ maxWidth: "35%", maxHeight: "35%" }}
                  />
                </Item>
              </Grid>
              <Grid
                item
                xs={12}
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Grid item sm={6}>
                  <Item className="page_bg">
                    <div className="card-main-tab page_bg">
                      <div className="card-inner-tab">
                        <div className="front_mobile">
                          <img
                            src={ImageBaseURL + "card_image.png"}
                            className="map-img"
                            alt="Card"
                          />
                          <div
                            className="card-row card_font_tab"
                            style={{ marginTop: "32%" }}
                          >
                            <div className="loan-ids-grid">
                              <span>{memberInfo.loan_id[0]}</span>
                              <span>{memberInfo.loan_id[1]}</span>
                              <span>{memberInfo.loan_id[2]}</span>
                              <span>{memberInfo.loan_id[3]}</span>
                            </div>
                          </div>
                          <div
                            className="row text-start card-holder-mobile fw-400 mt-2"
                            style={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                            }}
                          >
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                flexGrow: 1,
                              }}
                            >
                              <span
                                onClick={handleNameClick}
                                style={{
                                  whiteSpace: "nowrap",
                                  overflow: "hidden",
                                  textOverflow: "ellipsis",
                                  zIndex: 2,
                                  cursor: "pointer",
                                  width: "35%",
                                }}
                                title={`${memberInfo.member_first_name} ${memberInfo.member_last_name}`}
                              >
                                <Tooltip
                                  title={`${memberInfo.member_first_name} ${memberInfo.member_last_name}`}
                                  open={nameTooltipOpen}
                                  onClose={() => setNameTooltipOpen(false)}
                                  disableFocusListener
                                  disableHoverListener
                                  disableTouchListener
                                  placement="top"
                                >
                                  <span style={{ pointerEvents: "none" }}>
                                    {memberInfo.member_first_name +
                                      " " +
                                      memberInfo.member_last_name}
                                  </span>
                                </Tooltip>
                              </span>
                            </div>
                            <div
                              style={{
                                display: "flex",
                                flexDirection: "column",
                                textAlign: "center",
                                marginTop: "-15px",
                                marginLeft: "10px",
                              }}
                            >
                              <span>VALID THRU:</span>
                              <span>{memberInfo.preapproved_expire}</span>
                            </div>
                            <div
                              style={{
                                display: "flex",
                                flexDirection: "column",
                                textAlign: "left",
                              }}
                            >
                              <span>
                                AMOUNT:&nbsp;$ {memberInfo.preapproved_amount}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Item>
                </Grid>
              </Grid>
              <Grid item xs={12}>
                <Grid item sm={12}>
                  <Item className="page_bg">
                    <StyledButton isMobile={isMobile} variant="contained"
                      onClick={() => handleButtonClick("command-center", "/command-center")}
                    >
                      <Icon icon="loan-interest-time" size={40} />
                      <span className="btn_class_landing_mobile">
                        COMMAND CENTER
                      </span>
                    </StyledButton>
                  </Item>
                </Grid>
                <Grid item sm={12}>
                  <Item className="page_bg">
                    <StyledButton
                      isMobile={isMobile}
                      variant="contained"
                      onClick={isGenericSearchShow === "true"
                        ? () => handleConfirmationModalShow("shop-local-inventory")
                        : () => handleButtonClick("shop-local-inventory", "/specific-search")
                      }
                    >
                      <Icon icon="phone-in-hand" size={40} />
                      <span className="btn_class_landing_mobile">
                        SHOP LOCAL INVENTORY
                      </span>
                    </StyledButton>
                  </Item>
                </Grid>
                <Grid item sm={12}>
                  <Item className="page_bg">
                    <StyledButtonQA isMobile={isMobile} variant="contained"
                      onClick={redirectionURL}
                    >
                      <svg fill="#fff" version="1.1" id="earth" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" width="40px" height="40px" viewBox="0 0 256 202" enableBackground="new 0 0 256 202" xmlSpace="preserve" stroke="#fff"> <g id="SVGRepo_bgCarrier" strokeWidth="0" /><g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" /><g id="SVGRepo_iconCarrier"> <path d="M189.103,2.162c-35.842,0-64.897,29.055-64.897,64.897c0,35.841,29.055,64.897,64.897,64.897S254,102.9,254,67.059 C254,31.217,224.945,2.162,189.103,2.162z M189.457,111.449c-5.808,0-10.551-4.743-10.551-10.551 c0-5.842,4.743-10.551,10.551-10.551c5.808,0,10.551,4.743,10.551,10.551C200.008,106.706,195.265,111.449,189.457,111.449z M214.854,60.998c-1.512,2.234-4.158,4.88-8.317,7.904l-4.158,3.024c-2.199,1.856-3.712,3.712-4.502,6.014 c-0.447,1.512-0.79,3.78-0.79,6.805h-15.431c0.344-6.392,0.722-10.894,1.856-13.197c0.722-2.303,3.368-5.327,7.492-8.317 l3.815-3.368c1.512-1.065,2.577-2.234,3.368-3.368c1.512-1.924,2.302-4.158,2.302-6.805c0-3.024-1.134-5.602-2.646-7.904 c-1.512-2.234-4.537-3.368-9.039-3.368c-4.502,0-7.526,1.478-9.382,4.502c-1.512,2.234-2.646,5.258-2.646,7.904 c0,0.447,0,0.447,0,0.79c-0.447,4.158-3.815,7.492-8.317,7.492c-4.502,0-7.973-3.368-8.317-7.526c0,0,0-1.856,0-2.646 c1.134-9.451,4.949-16.187,11.306-20.345c4.537-2.646,10.207-4.158,16.565-4.158c8.626,0,15.774,1.856,21.445,6.014 c5.602,4.158,8.626,10.173,9.21,18.146C218.669,53.472,217.501,57.63,214.854,60.998z M229.382,148.01l-59.641,13.668 c-0.414,2.899-1.657,6.006-3.52,8.491c-3.52,5.384-8.905,9.319-15.117,10.976c-6.213,1.45-12.839,0.414-18.224-3.313l-51.358-32.72 c-2.485-1.45-2.899-4.349-1.45-6.627c1.45-2.278,4.349-2.899,6.42-1.45l51.565,32.72c6.834,4.142,15.739,2.278,20.087-4.349 c4.349-6.627,2.278-15.739-4.349-20.087l-64.197-40.589c-10.561-6.627-22.78-11.804-34.376-6.006L2,128.958l0.207,58.813 l37.897-26.3c6.006-1.45,12.632-0.207,18.224,3.313l44.731,28.164c10.976,6.834,24.436,8.491,36.033,5.384l96.917-22.158 c7.662-1.657,12.839-9.319,10.976-17.602C244.913,151.117,237.251,146.354,229.382,148.01z" /> </g></svg>
                      <span className="btn_class_landing_mobile">Loan Question? ASK CU</span>
                    </StyledButtonQA>
                  </Item>
                </Grid>
              </Grid>
            </Grid>
          )}
          <SearchConfirmationModal
            show={showConfirmationModal}
            onHide={handleConfirmationModalClose}
            slug={currentSlug} // Pass the slug to the modal
            componentMetaData={componentMetaData} // Pass metadata to modal
          />
          <MemberTwoChatModal open={chatModal} close={() => setChatModal(false)} memberType={userMemberType} />
        </Box>
      )}
    </Box>
  );
};

export default LandingMobile;
