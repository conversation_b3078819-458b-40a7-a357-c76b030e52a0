import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import PropTypes from "prop-types";
import { FaTimes, FaCheck } from "react-icons/fa";
import { useNavigate } from 'react-router-dom';
import { callComponentActionApi } from "../../../util/callComponenetActionApi";
import { useDispatch } from "react-redux";

const SearchConfirmationModal = ({ show, onHide, componentMetaData, slug }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const handleResponse = async (response) => {
    if (response === 'yes') {
      navigate('/specific-search'); // Navigate to the desired URL for 'YES'
    } else {
      navigate('/generic-search'); // Navigate to the desired URL for 'NO'
    }
    onHide(); // Close the modal after navigation
    const component = componentMetaData.find((comp) => comp.slug === slug);
      if (!component) {
        console.error(`Component with slug "${slug}" not found.`);
        return;
      }
    // Call the API with the component ID
    await callComponentActionApi(dispatch, component.id);
  };

  return (
    <Modal show={show} onHide={onHide} centered>
      <Modal.Header
        className="conf-modal-header-fixed"
        closeButton
      ></Modal.Header>
      <Modal.Body className="conf-modal-body-scrollable">
        <div className="conf-modal-question">
          <b>Have you found a vehicle to purchase yet?</b>
        </div>
        <div className="conf-modal-buttons">
          <Button className="custom-button no-button" onClick={() => handleResponse('no')}>
            <FaTimes className="conf-modal-icon" /> NO
          </Button>
          <Button className="custom-button yes-button" onClick={() => handleResponse('yes')}>
            <FaCheck className="conf-modal-icon" /> YES
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
};

SearchConfirmationModal.propTypes = {
  show: PropTypes.bool.isRequired,
  onHide: PropTypes.func.isRequired,
  componentMetaData: PropTypes.array.isRequired,
  slug: PropTypes.string.isRequired,
};

export default SearchConfirmationModal;
