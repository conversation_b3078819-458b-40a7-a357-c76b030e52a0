import { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import {
  getUser,
  getInterest,
  updateUserLoanDetails,
} from "../../../store/apps/user";
import { validateInput } from "../../../util/inputFieldNumberValidation";
import { pageMetaData } from "../../../../data/pageMetaData";
import { pageComponentData } from "../../../../data/componentMetaData";
import { toast } from "react-toastify";
import { callComponentActionApi } from "../../../util/callComponenetActionApi";
import { getloggedInUserDetails } from "../../../store/apps/user";

export const useCommandCenterApi = () => {
  const [loading, setLoading] = useState(true);
  const apiCallCompletedRef = useRef(false);
  const [term, setTerm] = useState("");
  const [preAprvAmount, setpreAprvAmount] = useState("");
  const [inputValue, setInputValue] = useState("");
  const [totalPurchaseAmount, setTotalPurchaseAmount] = useState("");
  const [memberInfo, setMemberInfo] = useState({
    selectedTerm: "",
    preapproved_pct: "",
    calculate_emi: "",
  });
  const [interestDropdown, setInterestDropdown] = useState([]);
  const [termOptions, setTermOptions] = useState([]);
  const [inputError, setInputError] = useState("");
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const token = localStorage.getItem("token");
  const inputRef = useRef(null);
  const [pageId, setPageId] = useState(null); // State to store the page_id
  const [componentMetaData, setComponentMetaData] = useState(null); // State to store the componentMetaData
  const [downPayment, setDownPayment] = useState("");
  const [downPaymentErrorMessage, setDownPaymentErrorMessage] = useState("");
  const [isSearchButtonLoader, setisSearchButtonLoader] = useState(false);
  const [financeAmount, setFinanceAmount] = useState("");
  const hasFetchedUserDetails = useRef(false);
  const [userDetails, setUserDetails] = useState({});
  const [isShopLocalLoader, setIsShopLocalLoader] = useState(false);
  const [principal, setPrincipal] = useState("");
  const [annualRate, setAnnualRate] = useState("");
  const [totalPayments, setTotalPayments] = useState("");
  const [newDownPayment, setNewDownPayment] = useState("");
  const [totalAmountAccured, setTotalAmountAccured] = useState("");
  const [adjustedPrincipal, setAdjustedPrincipal] = useState("");
  const [newMonthlyPayment, setNewMonthlyPayment] = useState("");
  const [isMonthlyPaymentChanged, setIsMonthlyPaymentChanged] = useState(false);
  const [savedMonthlyPayment, setSavedMonthlyPayment] = useState("");
  const [isDownPaymentChanged, setIsDownPaymentChanged] = useState(false);
  const [monthlyPaymentErrorMessage, setMonthlyPaymentErrorMessage] =
    useState("");
  const [isDownPaymentAddedToPrice, setIsDownPaymentAddedToPrice] =
    useState(false);
  const [previousDownPayment, setPreviousDownPayment] = useState(0);
  const [baseTotalPurchaseAmount, setBaseTotalPurchaseAmount] = useState(0);
  const [isDownpaymentCheckboxChecked, setIsDownpaymentCheckboxChecked] =
    useState(false);
  const [initialMonthlyPayment, setInitialMonthlyPayment] = useState(0);
  // Fetch User Details - Only once
  useEffect(() => {
    /**
     * Fetches the user details from the server using the token.
     * Sets the 'userDetails' state with the user's loan ID if the API call is successful.
     * Does nothing if the token is not available or if the API call has already been made.
     * @returns {Promise<void>}
     */
    const fetchUserDetails = async () => {
      if (hasFetchedUserDetails.current || !token) return;
      hasFetchedUserDetails.current = true;

      try {
        const { payload: { data: userDetailsVal = [] } = {} } = await dispatch(
          getloggedInUserDetails(token)
        );
        if (userDetailsVal.length) {
          setUserDetails({
            loan_id: userDetailsVal[0].loan_id,
            user_first_name: userDetailsVal[0].member_first_name,
            user_last_name: userDetailsVal[0].member_last_name,
            user_down_payment: userDetailsVal[0].user_down_payment,
            user_purchase_power: userDetailsVal[0].user_total_purchase_power,
          });
          const downPaymentValue = Number(
            userDetailsVal[0].user_down_payment ?? 0
          );
          const user_purchase_power_amount = Number(
            userDetailsVal[0].user_total_purchase_power ?? 0
          );
          setNewDownPayment(
            new Intl.NumberFormat("en-US", {
              minimumFractionDigits: 0, // No decimal places
              maximumFractionDigits: 0, // No decimal places
            }).format(Number(downPaymentValue))
          );
          setTotalPurchaseAmount(
            new Intl.NumberFormat("en-US", {
              minimumFractionDigits: 0, // No decimal places
              maximumFractionDigits: 0, // No decimal places
            }).format(Number(user_purchase_power_amount))
          );
          setBaseTotalPurchaseAmount(user_purchase_power_amount);
        }
      } catch (error) {
        console.error("Error fetching user details:", error);
      }
    };

    fetchUserDetails();
  }, [dispatch, token]);

  useEffect(() => {
    const fetchUserData = async () => {
      const number = localStorage.getItem("ph_number");
      if (!number || !token) {
        navigate("/");
        return;
      }

      try {
        dispatch(getUser({ phone_number: number, authToken: token })).then(
          (response) => {
            if (
              response &&
              response.meta &&
              response.meta.requestStatus === "fulfilled"
            ) {
              const userData = response.payload;
              setpreAprvAmount(userData.preapproved_max_amt);
              setInputValue(userData.preapproved_max_amt.toLocaleString());
              setFinanceAmount(userData.preapproved_max_amt.toLocaleString());
              setAdjustedPrincipal(
                Number(userData.preapproved_max_amt) +
                  Number(String(downPayment).replace(/,/g, ""))
              );
              setTerm(userData.term);
              getUserInterest(
                userData.lender_id,
                userData.fico_score,
                userData.term,
                userData.preapproved_max_amt
              );
              // Compute tax immediately and set values accordingly

              const taxValue = calculateTotalTax(
                userData.preapproved_max_amt,
                downPayment
              );
              if (!userDetails.user_purchase_power) {
                setTotalPurchaseAmount(userData.preapproved_max_amt - taxValue);
                setBaseTotalPurchaseAmount(userData.preapproved_max_amt - taxValue);
              } else {
                setTotalPurchaseAmount(userDetails.user_purchase_power);
                setBaseTotalPurchaseAmount(userDetails.user_purchase_power);
              }
              // End //

              apiCallCompletedRef.current = true;
              setLoading(false);
            } else {
              navigate("/");
            }
          }
        );
      } catch (error) {
        console.error("Error fetching user data:", error);
        navigate("/");
      }
    };

    const getUserInterest = (lender_id, fico_score, term, preAprvAmount) => {
      try {
        dispatch(
          getInterest({
            lenderId: lender_id,
            creditScore: fico_score,
            authToken: token,
          })
        ).then((response) => {
          if (
            response &&
            response.meta &&
            response.meta.requestStatus === "fulfilled"
          ) {
            const interestDropdown = response.payload;
            setInterestDropdown(interestDropdown);

            // Update termOptions with preapproved_pct
            const updatedTermOptions = [
              { label: "36 months", value: "36" },
              { label: "48 months", value: "48" },
              { label: "60 months", value: "60" },
              { label: "72 months", value: "72" },
              { label: "84 months", value: "84" },
            ].map((termOption) => {
              const interest = findMatchingInterest(
                termOption.value,
                interestDropdown
              );
              return {
                ...termOption,
                preapproved_pct: interest ? interest.interest_rate : "N/A",
                label: `${termOption.value} months - ${
                  interest ? interest.interest_rate : "N/A"
                }% APR`,
              };
            });
            setTermOptions(updatedTermOptions);

            const matchingObject = findMatchingInterest(term, interestDropdown);
            if (matchingObject) {
              const emi = calculateEmi(
                preAprvAmount,
                matchingObject.interest_rate,
                term
              );
              setMemberInfo((prevInfo) => ({
                ...prevInfo,
                preapproved_pct: matchingObject.interest_rate,
                calculate_emi: emi,
              }));
              setSavedMonthlyPayment(emi);
              setInitialMonthlyPayment(emi);
              // downPaymentCalculation(emi);
            }
          }
        });
      } catch (error) {
        console.error("Error fetching user data:", error);
        navigate("/");
      }
    };

    if (!apiCallCompletedRef.current) {
      fetchUserData();
    }

    // Include dispatch and navigate in the dependency array
  }, [dispatch, navigate, apiCallCompletedRef, token, userDetails]);

  useEffect(() => {
    getTotalAccuredAmount(adjustedPrincipal, term);
  }, [
    adjustedPrincipal,
    term,
    memberInfo.preapproved_pct,
    memberInfo.calculate_emi,
    isMonthlyPaymentChanged, // Add this to ensure recalculation
  ]);

  const handleZipCodeChange = (event) => {
    const value = event.target.value;
    if (/^\d{0,8}$/.test(value)) {
      setpreAprvAmount(value);
    }
  };

  const findMatchingInterest = (term, interestArray) => {
    for (const element of interestArray) {
      if (term >= element.min_term && term <= element.max_term) {
        return element;
      }
    }
    return null;
  };

  const calculateEmi = (
    principal,
    interestRate,
    term,
    updatedDownPayment = null
  ) => {
    if (!principal || !interestRate || !term) return 0;
    let newPrincipal = 0;
    if (
      updatedDownPayment > 0 &&
      updatedDownPayment != null &&
      updatedDownPayment <= principal
    ) {
      newPrincipal = principal - updatedDownPayment;
    } else {
      newPrincipal = principal;
    }

    // Convert annual interest rate to decimal
    const rate = interestRate / 100;
    // Calculate total amount accrued using simple interest formula: A = P(1 + rt)
    const totalAmount = newPrincipal * (1 + rate * (term / 12));
    // Calculate monthly payment
    const emi = totalAmount / term;
    return parseFloat(emi.toFixed(2)); // Ensure two decimal places
  };

  const handleTermChange = (event, newValue) => {
    setNewDownPayment(0);
    setMonthlyPaymentErrorMessage("");
    const numericValue = Number(inputValue.replace(/,/g, ""));
    setFinanceAmount(numericValue);
    setAdjustedPrincipal(numericValue);
    const value = newValue.value;
    if (inputValue != 0) {
      const selectedInterest = findMatchingInterest(value, interestDropdown);

      if (selectedInterest) {
        const emi = calculateEmi(
          parseFloat(inputValue.replace(/,/g, "")) ||
            parseFloat(preAprvAmount.replace(/,/g, "")),
          selectedInterest.interest_rate,
          value
        );
        setMemberInfo((prevInfo) => ({
          ...prevInfo,
          preapproved_pct: selectedInterest.interest_rate,
          calculate_emi: emi.toLocaleString(),
        }));
        setTerm(value);
        setSavedMonthlyPayment(emi);
        setInitialMonthlyPayment(emi);
      }
      // Ensure both values are converted to numbers
      const numericFinanceAmount =
        parseFloat(financeAmount.toString().replace(/,/g, "")) || 0;
      const numericAdjustedPrincipal = parseFloat(adjustedPrincipal) || 0;

      // Ensure only a valid numeric value is passed
      const validPrincipal = isNaN(numericFinanceAmount)
        ? numericAdjustedPrincipal
        : numericFinanceAmount;

      purchsePowerCalculation(validPrincipal, 0);
    }
  };

  const handleInputChange = (e) => {
    const inputElement = e.target;
    let start = inputElement.selectionStart; // Get the initial cursor position
    const initialLength = e.target.value.length; // Initial length before modification

    // Remove commas and spaces, keep only numbers and a single decimal point
    let enteredValue = e.target.value.replace(/[^0-9.]/g, "");

    // Ensure only one decimal point is present
    if ((enteredValue.match(/\./g) || []).length > 1) {
      setInputError("Invalid input: Only one decimal point is allowed.");
      return;
    }

    // Validate the input using the validateInput function
    const { isValid, parsedValue } = validateInput(enteredValue);

    if (!isValid) {
      setInputError(
        "Invalid input: Amount cannot exceed 15 digits and 2 decimal places."
      );
      return;
    }

    // If the input is valid, update the state and clear any error messages
    setInputError("");
    setInputValue(enteredValue);
    updateValues(parsedValue, downPayment);

    // Adjust cursor position based on the length change after formatting
    setTimeout(() => {
      const newLength = inputElement.value.length; // New length after modification
      const lengthDifference = newLength - initialLength;

      // Adjust the cursor position based on the difference in length
      start += lengthDifference;

      // Ensure the cursor stays within the valid range
      if (start < 0) start = 0;
      if (start > newLength) start = newLength;

      inputElement.setSelectionRange(start, start);
    }, 0);
  };

  const handleBlur = () => {
    if (!financeAmount) {
      // Handles "", null, and undefined
      setFinanceAmount(""); // Ensure empty value stays empty
      return;
    }

    // Ensure financeAmount is treated as a string before calling replace
    const numericValue = parseFloat(String(financeAmount).replace(/,/g, ""));

    if (isNaN(numericValue)) {
      setFinanceAmount(""); // Prevent NaN from appearing
      return;
    }

    // Check if the value has any decimal places
    const hasDecimal = String(financeAmount).includes(".");

    const formattedValue = numericValue.toLocaleString(undefined, {
      minimumFractionDigits: hasDecimal ? 2 : 0, // Only show decimals if the user entered them
      maximumFractionDigits: 2,
    });

    setFinanceAmount(formattedValue);
  };

  // Updated function to use latest down payment directly
  const updateValues = (purchaseAmount, updatedDownPayment) => {
    if (purchaseAmount === 0 || purchaseAmount === undefined) {
      purchaseAmount = inputValue;
    }
    const numericPurchaseAmount =
      parseFloat(purchaseAmount.toString().replace(/,/g, "")) || 0;
    const numericDownPayment =
      parseFloat(updatedDownPayment.toString().replace(/,/g, "")) || 0;
    if (numericPurchaseAmount !== 0 && !isDownpaymentCheckboxChecked) {
      const emi = calculateEmi(
        numericPurchaseAmount,
        memberInfo.preapproved_pct,
        term,
        updatedDownPayment
      );
      const taxValue = calculateTotalTax(numericPurchaseAmount, 0);
      // Update total purchase amount immediately
      setTotalPurchaseAmount(numericPurchaseAmount + 0 - taxValue);
      setBaseTotalPurchaseAmount(numericPurchaseAmount + 0 - taxValue);
      // Format EMI
      const formattedEmi = emi.toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
      if(!isDownpaymentCheckboxChecked){
        setMemberInfo((prevInfo) => {
          const newInfo = {
            ...prevInfo,
            calculate_emi: formattedEmi,
          };
          return newInfo;
        });
        setSavedMonthlyPayment(emi);
      }
    }
  };

  useEffect(() => {
    // Get the page_id from pageMetaData based on slug
    const slug = "command-center"; // Replace this with the desired slug
    const pageData = pageMetaData.find((page) => page.slug === slug);
    if (pageData) {
      setPageId(pageData.id);
    }

    // Get Page Componenet Meta Data
    const matchingComponents = pageComponentData.filter(
      (pageComponent) => pageComponent.page_id === pageId
    );

    if (matchingComponents.length > 0) {
      setComponentMetaData(matchingComponents);
    }
  }, [pageId]);

  // Command Center New Calculation //
  const calculateTotalTax = (maxApprovalAmount, downPayment) => {
    return Number(maxApprovalAmount) * 0.12 + Number(downPayment) * 0.12;
  };

  /**
   * Handles changes to the down payment input field. Removes all non-numeric characters
   * except for a single decimal point, and validates the input using the utility
   * function. If the input is invalid, sets an error message. If the input is valid,
   * clears any error message and updates the state with the new value.
   */
  const handleDownPaymentChange = (event) => {
    setIsDownPaymentChanged(true);
    setIsMonthlyPaymentChanged(false);
    setMonthlyPaymentErrorMessage("");

    const input = event.target;
    let value = input.value.trim().replace(/,/g, ""); // Remove all commas
    // If input is empty, reset values and return early
    if (value === "") {
      setDownPayment("");
      setNewDownPayment("");
      setDownPaymentErrorMessage(""); // Clear any error message
      updateValues(financeAmount, 0); // Ensure recalculations reset to zero
      setAdjustedPrincipal(0);
      if (isDownPaymentAddedToPrice) {
        setTotalPurchaseAmount(baseTotalPurchaseAmount); // 🛠️ Reset to base
        setPreviousDownPayment(0);
      }
      return;
    }

    // ✅ Allow only numbers (0-9) and a single decimal point (.)
    if (!/^\d*\.?\d*$/.test(value)) {
      setDownPaymentErrorMessage(
        "Only numbers (0-9) and a single decimal point are allowed."
      );
      return;
    }

    // Remove non-numeric characters except decimal point
    value = value.replace(/[^0-9.]/g, "");

    // Ensure only one decimal point is present
    if ((value.match(/\./g) || []).length > 1) {
      setDownPaymentErrorMessage("Invalid input. Please enter a valid amount.");
      return;
    }

    // Validate input using utility function
    if (!validateInput(value).isValid) {
      setDownPaymentErrorMessage(
        "Amount cannot exceed 15 digits, including up to 2 decimals."
      );
      return;
    }

    // Convert to a valid number while keeping decimals
    const numericValue = parseFloat(value) || 0;
    if (isNaN(numericValue)) {
      setDownPaymentErrorMessage("Invalid input. Please enter a valid amount.");
      return;
    }

    // Get loan approval amount for comparison
    const loanApprovalAmount = parseFloat(inputValue.replace(/,/g, "")) || 0;

    if (numericValue > loanApprovalAmount) {
      setDownPaymentErrorMessage(
        "Down payment cannot be greater than Your Loan Approval Amount."
      );
      setDownPayment(value);
    }

    setDownPaymentErrorMessage(""); // Clear error message

    const financeNumericValue = financeAmount
      ? parseFloat(String(financeAmount).replace(/,/g, "")) || 0
      : 0;

    setDownPayment(value);
    setNewDownPayment(value);
    updateValues(
      financeNumericValue !== 0 ? financeNumericValue : inputValue,
      numericValue
    );

    if (isDownPaymentAddedToPrice) {
      const updatedTotal = baseTotalPurchaseAmount + numericValue;
      setTotalPurchaseAmount(updatedTotal);
      setPreviousDownPayment(numericValue);
    }
  };

  /**
   * When the user blurs the down payment input field, apply U.S. number formatting
   * with up to 2 decimal places to the value. If the value is not a valid number,
   * do not update the state.
   */
  const handleDownPaymentBlur = () => {
    if (downPayment === null || downPayment === undefined) return; // Ensure it's defined

    const valueWithoutCommas = String(downPayment).replace(/,/g, ""); // Convert to string
    const numericValue = parseFloat(valueWithoutCommas); // Convert to number

    if (!isNaN(numericValue)) {
      setDownPayment(
        new Intl.NumberFormat("en-US", {
          minimumFractionDigits: 0,
          maximumFractionDigits: 2,
        }).format(numericValue)
      );
    }
  };

  /**
   * Handles the search button click event.
   *
   * If the total purchase amount is negative, displays an error toast and returns.
   * Otherwise, sets the search button loader to true, dispatches the
   * updateUserLoanDetails action, and on success navigates to the specific search
   * page. On failure, displays an error toast. Finally, attempts to call the
   * component action API for the search button component.
   */
  const handleSearchButton = async (fromShopLocal = false) => {
    if (totalPurchaseAmount < 0) {
      toast.error("Total Purchase Power cannot be negative!", {
        className: "bg-danger text-white fw-600 fw-bolder",
        bodyClassName: "text-white",
        icon: <i className="bi bi-exclamation-circle"></i>,
      });
      return;
    }
    // Set loader only for the respective button
    if (fromShopLocal) {
      setIsShopLocalLoader(true);
    } else {
      setisSearchButtonLoader(true);
    }
    const userLoanDetails = {
      car_looking_for: "new or used",
      down_payment:
        downPayment === 0 ? 0 : Number(String(newDownPayment).replace(/,/g, "")),
      user_total_purchase_power:
        totalPurchaseAmount === 0 ? 0 : Number(totalPurchaseAmount),
      is_include_trade: false,
      user_worth: 0,
      user_owe: 0,
    };
    try {
      const response = await dispatch(
        updateUserLoanDetails({ token, userLoanDetails })
      );

      if (response.meta.requestStatus === "fulfilled") {
        setisSearchButtonLoader(false);
        navigate("/specific-search");
      } else {
        setisSearchButtonLoader(false);
        toast.error("Failed to update loan details. Please try again.");
      }
    } catch (error) {
      if (fromShopLocal) {
        setIsShopLocalLoader(false);
      } else {
        setisSearchButtonLoader(false);
      }
      toast.error("An error occurred while updating loan details.");
    }

    try {
      const component = componentMetaData.find(
        (comp) => comp.slug === "search" && comp.type === "button"
      );
      if (component?.id) {
        await callComponentActionApi(dispatch, component.id);
      }
    } catch (error) {
      console.error("Error handling button click:", error);
    }
  };

  /**
   * Handle the click event for the Shop Local button.
   *
   * This function calls the `handleSearchButton` function which handles the search
   * functionality. The `handleSearchButton` function is called with a try-catch block
   * and regardless of whether the function call succeeds or fails, the loader is
   * stopped by setting `isShopLocalLoader` to false.
   */
  const handleShopLocalButton = async () => {
    setIsShopLocalLoader(true);

    try {
      await handleSearchButton(true);
    } finally {
      setIsShopLocalLoader(false);
    }
  };

  /**
   * Handles the input change event for the finance amount input field.
   *
   * This function validates the input and if valid, updates the state and clears any
   * error messages. If invalid, sets an error message and stops further processing.
   *
   * Additionally, this function ensures that the input field's value is correctly
   * formatted and that the cursor position is adjusted accordingly.
   *
   * @param {Event} e The input change event.
   */
  const handleFinanceAmountChange = (e) => {
    const inputElement = e.target;
    let start = inputElement.selectionStart; // Get the initial cursor position
    const initialLength = e.target.value.length; // Initial length before modification
    setMonthlyPaymentErrorMessage("");

    // Remove commas and spaces, keep only numbers and a single decimal point
    let enteredValue = e.target.value.replace(/[^0-9.]/g, "");

    // Ensure only one decimal point is present
    if ((enteredValue.match(/\./g) || []).length > 1) {
      setInputError("Invalid input: Only one decimal point is allowed.");
      return;
    }

    // ✅ **Fix: Allow full deletion of input**
    if (enteredValue === "") {
      setFinanceAmount("");
      setAdjustedPrincipal(""); // Ensure this is cleared too
      setNewDownPayment(0);
      setInputError(""); // Clear error message if any
      updateValues(0, 0); // Ensure values are updated properly
      return; // Exit early so further code does not interfere
    }

    // Validate the input using the validateInput function
    const { isValid, parsedValue } = validateInput(enteredValue);

    if (!isValid) {
      setInputError(
        "Invalid input: Amount cannot exceed 15 digits and 2 decimal places."
      );
      return;
    }

    // ✅ **Prevent entering 0**
    if (parseFloat(enteredValue) === 0) {
      setInputError("Finance amount must be greater than 0.");
      return; // Stop execution to prevent updating state
    }

    const numericLoanApprovalValue = Number(inputValue.replace(/,/g, ""));
    if (parseFloat(enteredValue) > numericLoanApprovalValue) {
      setInputError(
        `You cannot enter vehicle purchase price greater than $${inputValue}`
      );
      return;
    }

    // If the input is valid, update the state and clear any error messages
    setInputError("");
    setFinanceAmount(enteredValue);
    setAdjustedPrincipal(enteredValue);
    setNewDownPayment(0);
    updateValues(parsedValue, 0);

    // Adjust cursor position based on the length change after formatting
    setTimeout(() => {
      const newLength = inputElement.value.length; // New length after modification
      const lengthDifference = newLength - initialLength;

      // Adjust the cursor position based on the difference in length
      start += lengthDifference;

      // Ensure the cursor stays within the valid range
      if (start < 0) start = 0;
      if (start > newLength) start = newLength;

      inputElement.setSelectionRange(start, start);
    }, 0);
  };

  /**
   * Handles changes to the monthly payment input field. Removes all non-numeric characters
   * except for a single decimal point, and restricts the input to 2 decimal places.
   * If the input is greater than the saved monthly payment, sets an error message.
   * If the input is valid, clears any error message and updates the state.
   * @param {ChangeEvent<HTMLInputElement>} e The event object
   */
  const handleMonthlyPaymentChange = (e) => {
    let inputElement = e.target.value;

    // Remove non-numeric characters except decimal point
    inputElement = inputElement.replace(/[^0-9.]/g, "");

    // Prevent multiple decimal points
    const decimalCount = (inputElement.match(/\./g) || []).length;
    if (decimalCount > 1) return;

    // Restrict to 2 decimal places
    if (inputElement.includes(".")) {
      const [integerPart, decimalPart] = inputElement.split(".");
      if (decimalPart.length > 2) {
        inputElement = `${integerPart}.${decimalPart.slice(0, 2)}`;
      }
    }

    // Handle case when input is empty (allows deletion)
    if (inputElement === "") {
      setMemberInfo((prevInfo) => ({
        ...prevInfo,
        calculate_emi: "", // Allow clearing input
      }));
      setMonthlyPaymentErrorMessage(""); // Clear error when input is empty
      setNewDownPayment(0);
      return;
    }

    // Convert savedMonthlyPayment to a float value
    const savedMonthlyPaymentDecimalVal = parseFloat(
      savedMonthlyPayment?.toFixed(2) || "0.00"
    );

    // Ensure valid numeric input before parsing
    if (inputElement === "." || isNaN(parseFloat(inputElement))) {
      setMonthlyPaymentErrorMessage("");
      return;
    }

    // Convert input to float for validation
    const inputValue = parseFloat(inputElement);

    // Validate input against savedMonthlyPayment
    if (inputValue > savedMonthlyPaymentDecimalVal) {
      setMonthlyPaymentErrorMessage(
        `You cannot enter a monthly payment greater than $${savedMonthlyPaymentDecimalVal}`
      );
      return; // Stop execution here, preventing invalid state update
    }

    // If valid, update state and clear error
    setIsMonthlyPaymentChanged(true);
    setMemberInfo((prevInfo) => ({
      ...prevInfo,
      calculate_emi: inputElement, // Store as string
    }));
    setMonthlyPaymentErrorMessage("");
  };

  /**
   * Calculates total amount accrued based on adjusted principal and term.
   * If monthly payment is being edited, calls getNewDownPayment to update down payment.
   * If down payment is being edited, updates monthly payment state.
   * @param {Number} adjustedPrincipal
   * @param {Number} term
   */
  const getTotalAccuredAmount = (adjustedPrincipal, term) => {
    if (adjustedPrincipal == 0) {
      const numericFinanceAmount =
        parseFloat(financeAmount.toString().replace(/,/g, "")) || 0;
      adjustedPrincipal = numericFinanceAmount;
    }
    const annualRateNew = (memberInfo.preapproved_pct || 0) / 100;
    let newPrincipal = 0;
    if (annualRateNew > 0 && term > 0) {
      if (newDownPayment > 0 && !isMonthlyPaymentChanged) {
        // Ensure financeAmount is a valid number
        const numericFinanceAmount =
          parseFloat(financeAmount.toString().replace(/,/g, "")) || 0;
        newPrincipal = Math.max(0, numericFinanceAmount - newDownPayment); // Subtract correct value
      } else {
        newPrincipal = adjustedPrincipal;
      }
      const totalAmountAccrued =
        newPrincipal * (1 + annualRateNew * (term / 12));
      setTotalAmountAccured(totalAmountAccrued);
      if (
        totalAmountAccrued > 0 &&
        term > 0 &&
        memberInfo.calculate_emi > 0 &&
        isMonthlyPaymentChanged
      ) {
        getNewDownPayment(totalAmountAccrued, term);
      }

      if (isDownPaymentChanged && !isMonthlyPaymentChanged && !isDownpaymentCheckboxChecked) {
        const newMonthlyPaymentVal = totalAmountAccrued / term;
        if (newMonthlyPaymentVal != "" && newMonthlyPaymentVal != null) {
          setMemberInfo((prevInfo) => ({
            ...prevInfo,
            calculate_emi: newMonthlyPaymentVal.toFixed(2), // Allow editing
          }));
            setSavedMonthlyPayment(newMonthlyPaymentVal);
        }
      }
    }
  };

  /**
   * Calculates and updates the new down payment based on the total amount accrued
   * and the loan term. If the monthly payment has changed, it computes the remaining
   * amount after subtracting the total monthly payments from the total amount accrued.
   * Updates the new down payment and recalculates the purchase power.
   *
   * @param {number} totalAmountAccrued - The total amount accrued over the loan term.
   * @param {number} term - The loan term in months.
   * @returns {boolean|string} - Returns false if the monthly payment hasn't changed,
   *                             or an empty string if any required value is missing.
   */

  const getNewDownPayment = (totalAmountAccrued, term) => {
    if (!isMonthlyPaymentChanged) {
      setNewDownPayment("");
      return false;
    }

    const userMonthlyPayment = memberInfo.calculate_emi;
    const annualInterestRate = memberInfo.preapproved_pct || 0;
    const numericLoanApprovalValue = Number(
      (
        (adjustedPrincipal && adjustedPrincipal !== 0
          ? adjustedPrincipal
          : financeAmount) || "0"
      )
        .toString()
        .replace(/,/g, "")
    );
    // Check for empty/null/undefined values
    if (
      totalAmountAccrued === "" ||
      totalAmountAccrued == null ||
      userMonthlyPayment === "" ||
      userMonthlyPayment == null ||
      term === "" ||
      term == null
    ) {
      setNewDownPayment("");
      return "";
    }

    // Step 1: Calculate Interest Factor
    const interestFactor = 1 + (annualInterestRate / 100) * (term / 12);
    // Step 2: Multiply Monthly Payment by Loan Term
    const totalPayment = userMonthlyPayment * term;
    // Step 3: Divide by Interest Factor
    const adjustedPayment = totalPayment / interestFactor;
    // Step 4: Subtract from Loan Approval Value (Final Down Payment Calculation)
    const requiredDownPayment = numericLoanApprovalValue - adjustedPayment;
    // Set the final calculated down payment
    setNewDownPayment(requiredDownPayment.toFixed(2));
  };

  const purchsePowerCalculation = (finalPrincipal, finalDownPayment) => {
    // Ensure values are numbers before processing
    finalPrincipal = isNaN(finalPrincipal) ? 0 : Number(finalPrincipal);
    finalDownPayment = isNaN(finalDownPayment) ? 0 : Number(finalDownPayment);

    // Round finalPrincipal and finalDownPayment to two decimal places
    finalPrincipal = parseFloat(finalPrincipal.toFixed(2));
    finalDownPayment = parseFloat(finalDownPayment.toFixed(2));

    // Ensure tax is calculated correctly and rounded
    const taxValue = calculateTotalTax(finalPrincipal, finalDownPayment);
    const roundedTaxValue = parseFloat(taxValue.toFixed(2));

    // Compute total purchase power and round it
    const totalPurchasePower = parseFloat(
      (finalPrincipal + finalDownPayment - roundedTaxValue).toFixed(2)
    );
    // Set the total purchase amount with corrected precision
    setTotalPurchaseAmount(totalPurchasePower);
    setBaseTotalPurchaseAmount(totalPurchasePower);
  };

  /**
   * Handles the change event of the "Add down payment to total price" checkbox.
   * @param {ChangeEvent<HTMLInputElement>} event The change event.
   */
  const handleDownPaymentCheckboxChange = (event) => {
    const isChecked = event.target.checked;
    setIsDownPaymentAddedToPrice(isChecked);
    setIsDownpaymentCheckboxChecked(isChecked);
    const numericDownPayment = Number(newDownPayment) || 0;
    if (isChecked) {
      setNewDownPayment(0); // Reset down payment to 0 when checked
      setBaseTotalPurchaseAmount(totalPurchaseAmount); // Store original before adding down payment
      setTotalPurchaseAmount(totalPurchaseAmount);
      setPreviousDownPayment(numericDownPayment);
      setMemberInfo((prevInfo) => ({
        ...prevInfo,
        calculate_emi: initialMonthlyPayment, // Store as string
      }));
    } else {
      setTotalPurchaseAmount(baseTotalPurchaseAmount); // Revert to base when unchecked
      setPreviousDownPayment(0);
      setNewDownPayment(0); // Reset down payment to 0 when unchecked
      setMemberInfo((prevInfo) => ({
        ...prevInfo,
        calculate_emi: initialMonthlyPayment, // Store as string
      }));
    }
  };

  return {
    loading,
    term,
    termOptions,
    handleTermChange,
    handleZipCodeChange,
    preAprvAmount,
    memberInfo,
    handleInputChange,
    inputError,
    inputValue,
    totalPurchaseAmount,
    handleBlur,
    inputRef,
    componentMetaData,
    downPayment,
    downPaymentErrorMessage,
    handleDownPaymentBlur,
    handleDownPaymentChange,
    isSearchButtonLoader,
    handleSearchButton,
    financeAmount,
    handleFinanceAmountChange,
    handleShopLocalButton,
    isShopLocalLoader,
    handleMonthlyPaymentChange,
    principal,
    annualRate,
    totalPayments,
    newDownPayment,
    adjustedPrincipal,
    totalAmountAccured,
    newMonthlyPayment,
    monthlyPaymentErrorMessage,
    isDownPaymentAddedToPrice,
    handleDownPaymentCheckboxChange,
  };
};

export default useCommandCenterApi;
