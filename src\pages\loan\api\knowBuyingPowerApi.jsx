import { useState, useEffect, useRef } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  getloggedInUserDetails,
  updateUserLoanDetails,
  getUser
} from "../../../store/apps/user";
import { toast } from "react-toastify";
import { validateInput } from "../../../util/inputFieldNumberValidation";
import { pageMetaData } from "../../../../data/pageMetaData";
import { pageComponentData } from "../../../../data/componentMetaData";
import { callComponentActionApi } from "../../../util/callComponenetActionApi";
export const useKnowYourBuyingPowerApi = () => {
  const carLookingOptions = [
    { label: "New or Used", value: "new or used" },
    { label: "New", value: "new" },
    { label: "Used", value: "used" },
  ];
  const token = localStorage.getItem("token");
  const [downPayment, setDownPayment] = useState("");
  const [userWorth, setUserWorth] = useState(0);
  const [userOwe, setUserOwe] = useState(0);
  const [userCarLookingFor, setUserCarLookingFor] = useState("new or used");
  const [isIncludeTrade, setIsIncludeTrade] = useState(false);
  const [totalTax, setTotalTax] = useState(0);
  const [totalPurchasePower, setTotalPurchasePower] = useState(0);
  const [maxApprovalAmount, setMaxApprovalAmount] = useState();
  const [errorMessage, setErrorMessage] = useState("");
  const [downPaymentErrorMessage, setDownPaymentErrorMessage] = useState("");
  const [worthErrorMessage, setWorthErrorMessage] = useState("");
  const [isSearchDisabled, setIsSearchDisabled] = useState(false);
  const [isPurchasePowerLoading, setIsPurchasePowerLoading] = useState(false);
  const [isSearchButtonLoader, setisSearchButtonLoader] = useState(false);

  const inputRef = useRef(null);

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const number = localStorage.getItem("ph_number");
  const [pageId, setPageId] = useState(null); // State to store the page_id
  const [componentMetaData, setComponentMetaData] = useState(null); // State to store the componentMetaData
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setIsPurchasePowerLoading(true);
  
        // Fetch logged-in user details
        const { payload: { data: userDetails = [] } = {} } = await dispatch(
          getloggedInUserDetails(token)
        );
  
        // Fetch additional user data only once
        const fetchUserData = async () => {
          const response = await dispatch(
            getUser({ phone_number: number, authToken: token })
          );
          if (
            response &&
            response.meta &&
            response.meta.requestStatus === "fulfilled"
          ) {
            const userData = response.payload;
            setMaxApprovalAmount(userData.preapproved_max_amt);
          }
          setIsPurchasePowerLoading(false);
        };
  
        await fetchUserData();
  
        // Process `userDetails` if available
        if (userDetails.length > 0) {
          const downPaymentValue = Number(userDetails[0].user_down_payment ?? 0);
          const userWorthValue = Number(
            userDetails[0].user_worth ?? 0
          ).toLocaleString();
          const userOweValue = Number(
            userDetails[0].user_owe ?? 0
          ).toLocaleString();
          const userCarLooking = userDetails[0].user_car_looking_for ?? "";
          const includeTradeVal = userDetails[0].is_include_trade ?? false;
        
          // Format downPaymentValue in U.S. currency format
          setDownPayment(
            new Intl.NumberFormat("en-US", {
              minimumFractionDigits: 0, // No decimal places
              maximumFractionDigits: 0, // No decimal places
            }).format(Number(downPaymentValue))
          );
        
          setUserWorth(userWorthValue);
          setUserOwe(userOweValue);
          setUserCarLookingFor(userCarLooking || "new or used");
          setIsIncludeTrade(includeTradeVal);
        }
      } catch (error) {
        console.error("Error fetching initial data:", error);
        setIsPurchasePowerLoading(false);
      }
    };
  
    fetchInitialData();
  }, [dispatch, token]);

  useEffect(() => {
    calculateTotalTax();
  }, [maxApprovalAmount, downPayment, userWorth, userOwe]);

  useEffect(() => {
    calculateTotalPurchasePower();
  }, [maxApprovalAmount, downPayment, userWorth, userOwe, totalTax]);

  useEffect(() => {
    validateFields();
  }, [userWorth, userOwe]);

  const handleSwitchChange = (event) => {
    setIsIncludeTrade(event.target.checked);
    if (event.target.checked === false) {
      setUserWorth(0);
      setUserOwe(0);
    }
  };

  const handleDownPaymentChange = (event) => {
    const input = event.target;
    let value = input.value.replace(/[^0-9.]/g, ""); // Remove all non-numeric characters except the decimal point
  
    // Ensure only one decimal point is present
    if ((value.match(/\./g) || []).length > 1) {
      setDownPaymentErrorMessage("Invalid input. Please enter a valid amount.");
      return;
    }
  
    // Validate input using the utility function
    const { isValid } = validateInput(value);
  
    if (!isValid) {
      setDownPaymentErrorMessage("Amount cannot exceed 15 digits, including up to 2 decimals.");
      return;
    }
  
    setDownPaymentErrorMessage(""); // Clear error message
    setDownPayment(value); // Set the raw value without formatting
  };
  
  const handleDownPaymentBlur = () => {
    const valueWithoutCommas = downPayment.replace(/,/g, ""); // Remove commas
    const numericValue = parseFloat(valueWithoutCommas); // Convert to number

    if (!isNaN(numericValue)) {
        // Apply U.S. number formatting with up to 2 decimal places
        setDownPayment(
            new Intl.NumberFormat("en-US", {
                minimumFractionDigits: 0, // Minimum decimals
                maximumFractionDigits: 2, // Up to 2 decimals
            }).format(numericValue)
        );
    }
  };
  
  const handleDownPaymentClick = (e) => {
    const inputElement = e.target;
    const cursorPosition = inputElement.selectionStart; // Get the initial cursor position
  
    // Remove formatting (commas) to show raw value
    const rawValue = downPayment.replace(/,/g, '');
    setDownPayment(rawValue);
  
    // Calculate the cursor position relative to the raw value (before reformatting)
    let positionInRawValue = 0;
    for (let i = 0; i < cursorPosition; i++) {
      if (downPayment[i] !== ',') {
        positionInRawValue++;
      }
    }
  
    // Restore cursor position after updating the value
    requestAnimationFrame(() => {
      inputElement.setSelectionRange(positionInRawValue, positionInRawValue);
    });
  };

  const validateFields = () => {
    // Convert to strings and remove commas before converting to number
    const worthValue = Number((userWorth || "").replace(/,/g, ""));
    const oweValue = Number((userOwe || "").replace(/,/g, ""));

    if (worthValue === 0 || oweValue === 0) {
      setErrorMessage("");
      setIsSearchDisabled(false);
      return;
    }
  };

  const handleUserWorthChange = (event) => {
    const enteredValue = event.target.value.replace(/,/g, ""); // Remove commas for processing

    if (enteredValue.length > 15) {
      setWorthErrorMessage("Amount cannot exceed 15 digits");
      return;
    } else {
      setWorthErrorMessage("");
      if (/^\d*\.?\d*$/.test(enteredValue)) {
        setUserWorth(enteredValue); // Set raw value without formatting
      }
    }
  };

  const handleUserWorthBlur = () => {
    const valueWithoutCommas = userWorth.replace(/,/g, ""); // Remove commas
    const numericValue = parseFloat(valueWithoutCommas); // Convert to number

    if (!isNaN(numericValue)) {
      setUserWorth(
        numericValue.toLocaleString(undefined, {
          minimumFractionDigits: 0,
          maximumFractionDigits: 2,
        })
      );
    }
  };

  const handleUserOweChange = (event) => {
    const enteredValue = event.target.value.replace(/,/g, ""); // Remove commas for processing

    // Check if the length of entered value exceeds 15 digits
    if (enteredValue.length > 15) {
      setErrorMessage("Amount cannot exceed 15 digits");
      return;
    } else {
      setErrorMessage("");
      if (/^\d*\.?\d*$/.test(enteredValue)) {
        setUserOwe(enteredValue); // Set raw value without formatting
      }
    }
  };

  const handleUserOweBlur = () => {
    const valueWithoutCommas = userOwe.replace(/,/g, ""); // Remove commas
    const numericValue = parseFloat(valueWithoutCommas); // Convert to number

    if (!isNaN(numericValue)) {
      setUserOwe(
        numericValue.toLocaleString(undefined, {
          minimumFractionDigits: 0,
          maximumFractionDigits: 2,
        })
      );
    }
  };

  const calculateTotalTax = () => {
    const replacedWorth = String(userWorth).replace(/,/g, "");
    const replacedOwe = String(userOwe).replace(/,/g, "");
    const trade = Number(replacedWorth) - Number(replacedOwe);

    const totalTaxValue =
      Number(maxApprovalAmount) * 0.12 +
      Number(String(downPayment).replace(/,/g, "")) * 0.12 +
      trade * 0.12;

    setTotalTax(totalTaxValue);
  };

  const calculateTotalPurchasePower = () => {
    const userWorthValue = Number(String(userWorth).replace(/,/g, "")) || 0;
    const userOweValue = Number(String(userOwe).replace(/,/g, "")) || 0;
    const downpaymentValue = Number(String(downPayment).replace(/,/g, "")) || 0;
    const trade = userWorthValue - userOweValue;
    const totalTaxValue = Number(totalTax) || 0;
  
    let totalPurchasePrice =
      Number(maxApprovalAmount) + downpaymentValue + trade - totalTaxValue;
    totalPurchasePrice =
      isNaN(totalPurchasePrice) || totalPurchasePrice === undefined
        ? 0
        : totalPurchasePrice;
  
    // Round to two decimal places
    totalPurchasePrice = parseFloat(totalPurchasePrice.toFixed(2));
  
    setTotalPurchasePower(totalPurchasePrice);
  };

  const handleSearchButton = async () => {
    // Ensure that totalPurchasePower is checked before proceeding
    if (totalPurchasePower < 0) {
      toast.error("Total Purchase Power cannot be negative!", {
        className: "bg-danger text-white fw-600 fw-bolder",
        bodyClassName: "text-white",
        icon: <i className="bi bi-exclamation-circle"></i>,
      });
      return; // Stop further execution
    }
  
    const userLoanDetails = {
      car_looking_for: userCarLookingFor,
      down_payment: downPayment === 0 ? 0 : Number(downPayment.replace(/,/g, "")),
      is_include_trade: isIncludeTrade,
      user_worth: userWorth === 0 ? 0 : Number(userWorth.replace(/,/g, "")),
      user_owe: userOwe === 0 ? 0 : Number(userOwe.replace(/,/g, "")),
      user_total_purchase_power: totalPurchasePower === 0 ? 0 : Number(totalPurchasePower),
    };
  
    try {
      setisSearchButtonLoader(true);
      // Dispatch the action and wait for the response
      const response = await dispatch(updateUserLoanDetails({ token, userLoanDetails }));
      // Check if the update was successful (based on response or success field)
      if (response.meta.requestStatus === "fulfilled") {
        setisSearchButtonLoader(false);
        navigate("/specific-search");
      } else {
        setisSearchButtonLoader(false);
        toast.error("Failed to update loan details. Please try again.", {
          className: "bg-danger text-white fw-600 fw-bolder",
          bodyClassName: "text-white",
          icon: <i className="bi bi-exclamation-circle"></i>,
        });
      }
    } catch (error) {
      setisSearchButtonLoader(false);
      // Handle any errors that occurred during the dispatch
      toast.error("An error occurred while updating loan details.", {
        className: "bg-danger text-white fw-600 fw-bolder",
        bodyClassName: "text-white",
        icon: <i className="bi bi-exclamation-circle"></i>,
      });
    }
    try {
      // Find the component by slug
      const component = componentMetaData.find((comp) => comp.slug === 'search' && comp.type === 'button');
      if (!component) {
        console.error(`Component with slug "search" not found.`);
        return;
      }

      // Call the API with the component ID
      await callComponentActionApi(dispatch, component.id);
    } catch (error) {
      console.error("Error handling button click:", error);
    }
  };
  

  const handleCarLookingChange = (event, newValue) => {
    setUserCarLookingFor(newValue?.value ?? "");
  };
  
  useEffect(() => {
    // Get the page_id from pageMetaData based on slug
    const slug = "know-your-buying-power"; // Replace this with the desired slug
    const pageData = pageMetaData.find((page) => page.slug === slug);
    if (pageData) {
      setPageId(pageData.id);
    }

    // Get Page Componenet Meta Data
    const matchingComponents = pageComponentData.filter(
      (pageComponent) => pageComponent.page_id === pageId
    );

    if (matchingComponents.length > 0) {
      setComponentMetaData(matchingComponents);
    }
  }, [pageId]);

  return {
    carLookingOptions,
    handleSwitchChange,
    isIncludeTrade,
    downPayment,
    handleDownPaymentChange,
    userWorth,
    userOwe,
    userCarLookingFor,
    totalPurchasePower,
    handleUserWorthChange,
    handleUserOweChange,
    errorMessage,
    handleSearchButton,
    handleCarLookingChange,
    isSearchDisabled,
    inputRef,
    downPaymentErrorMessage,
    worthErrorMessage,
    handleDownPaymentBlur,
    handleUserWorthBlur,
    handleUserOweBlur,
    handleDownPaymentClick,
    isPurchasePowerLoading,
    isSearchButtonLoader,
    componentMetaData
  };
};

export default useKnowYourBuyingPowerApi;
