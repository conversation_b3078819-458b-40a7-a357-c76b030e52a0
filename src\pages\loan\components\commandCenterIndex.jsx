import { useMediaQuery } from 'react-responsive'
import CommandCenterMobile from './commandCenterMobile';
import CommandCenterWeb from './commandCenterWeb';
import MasterLayout from '../../../components/layouts/masterLayout';

function CommandCenterIndex() {
    const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' })
    return (
        <MasterLayout>
            {isTabletOrMobile ? <CommandCenterMobile /> : <CommandCenterWeb />}
        </MasterLayout>
    );
}

export default CommandCenterIndex;