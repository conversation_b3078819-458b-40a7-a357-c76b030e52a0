/* eslint-disable no-unused-vars */
import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import { useMediaQuery } from "react-responsive";
import { useCommandCenterApi } from "../api/commandCenterApi";
import {
  Autocomplete,
  IconButton,
  Tooltip,
  Checkbox,
  FormControlLabel,
} from "@mui/material";
import CircularProgress from "@mui/material/CircularProgress";
import InputAdornment from "@mui/material/InputAdornment";
import Stack from "@mui/material/Stack";
import loaderGif from "../../../assets/gifs/loader.gif";
import Icon from "../../../icon";
import { useNavigate } from "react-router-dom";
import SearchConfirmationModal from "../../landing/modals/searchConfirmationModal";
import { useEffect, useState } from "react";
import { callComponentActionApi } from "../../../util/callComponenetActionApi";
import { useDispatch } from "react-redux";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { ImageBaseURL } from "../../../config";
import { useGlobalTutorialModal } from "../../../context/TutorialModalContext";
import { hasSeenTutorial, shouldShowTutorial } from "../../../context/TutorialStorage";

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const labelStyle = {
  color: "#4891FF",
};

const CustomTextField = styled(TextField)({
  "& .MuiInputLabel-root": labelStyle, // Apply label style
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "bold", // Bold the input field value
    },
  },
});

const CustomAutocomplete = styled(Autocomplete)({
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "bold", // Bold the selected option value
    },
  },
  "& .MuiAutocomplete-option": {
    fontWeight: "bold", // Bold the dropdown options
  },
});

const StyledButton = styled(({ isMobile, ...other }) => <Button {...other} />)(
  ({ isMobile }) => ({
    borderRadius: "10px",
    width: isMobile ? "80%" : "50%",
    fontSize: "14px",
    fontWeight: "600",
    padding: "10px",
    backgroundColor: "#4891FF",
    "&:hover": {
      backgroundColor: "primary",
    },
    "&.Mui-disabled": {
      backgroundColor: "#4891FF",
      color: "#fff",
      opacity: 0.8,
    },
  })
);

const SpecificSearchCarMobile = () => {
  const isMobile = useMediaQuery({ maxWidth: 767 });
  const token = localStorage.getItem("token");
  const isGenericSearchShow = import.meta.env.VITE_IS_GENERIC_SEARCH_SHOW;
  if (!token) {
    navigate("/");
  }
  const {
    loading,
    termOptions,
    term,
    handleTermChange,
    memberInfo,
    inputValue,
    inputError,
    totalPurchaseAmount,
    handleBlur,
    inputRef,
    componentMetaData,
    downPayment,
    downPaymentErrorMessage,
    handleDownPaymentBlur,
    handleDownPaymentChange,
    financeAmount,
    handleFinanceAmountChange,
    handleShopLocalButton,
    isShopLocalLoader,
    handleMonthlyPaymentChange,
    newDownPayment,
    adjustedPrincipal,
    monthlyPaymentErrorMessage,
    isDownPaymentAddedToPrice,
    handleDownPaymentCheckboxChange
  } = useCommandCenterApi(token);
  const navigate = useNavigate();
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const handleConfirmationModalShow = () => setShowConfirmationModal(true);
  const handleConfirmationModalClose = () => setShowConfirmationModal(false);
  const dispatch = useDispatch();
  useEffect(() => {
    let isMounted = true; // Add a flag to check if the component is still mounted

    if (
      !loading &&
      componentMetaData &&
      componentMetaData.length > 0 &&
      isMounted
    ) {
      try {
        const component = componentMetaData.find(
          (comp) => comp.slug === "command-center" && comp.type === "url"
        );
        if (!component) {
          console.error(`Component with slug "my-garage" not found.`);
          return;
        }
        // Call the API with the component ID
        callComponentActionApi(dispatch, component.id);
      } catch (error) {
        console.error("Error handling button click:", error);
      }
    }

    // Cleanup function to reset the flag when the component is unmounted
    return () => {
      isMounted = false;
    };
  }, [loading, componentMetaData]);

  const handleTooltipToggle = () => {
    setIconOpen((prev) => !prev);
  };
  const [iconOpen, setIconOpen] = useState(false);


  // for tutorial
  const { openTutorialModal } = useGlobalTutorialModal();
  useEffect(() => {
    const checkAndShowTutorial = async () => {
      if (loading) return; // Don't proceed if loading

      const localFlag = hasSeenTutorial("commandCenter_shopInventory");
      if (localFlag) return; // Already seen locally, skip

      const shouldShow = await shouldShowTutorial({
        tutorialKey: "commandCenter_shopInventory",
        dispatch,
      });

      if (shouldShow) {
        openTutorialModal({
          bodyImageSrc: ImageBaseURL + "tutorials/images/command-center-tutorial-feature-image.png",
          additionalTopText: (
            <span>Calculate your</span>
          ),
          mainText: (
            <span>Monthly Payment</span>
          ),
          subText: (
            <span>Next Click on Shop Local Inventory</span>
          ),
          audioSrc: ImageBaseURL + "tutorials/audios/command-center-audio.mp3"
        });
      }
    };

    checkAndShowTutorial();
  }, [loading]);

  return (
    <Box sx={{ flexGrow: 1 }}>
      {loading ? (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Stack alignItems="center" justifyContent="center" mt={24}>
            <img
              className="page_bg"
              src={loaderGif}
              alt="Loading..."
              style={{ width: "200px", height: "200px" }}
            />
            <span style={{ fontWeight: "600" }}>Loading Details...</span>
          </Stack>
        </Box>
      ) : (
        <Grid container spacing={2} sx={{ padding: "80px 10px" }}>
          <Grid item xs={12}>
            <Item className="page_bg">
              {" "}
              {/* Adjust margin to move text upwards */}
              <Typography variant="h5" component="h5">
                <b>COMMAND CENTER</b>
              </Typography>
            </Item>
          </Grid>
          <Grid item xs={12} style={{ paddingTop: "0px" }}>
            <Item className="page_bg">
              <Typography
                variant="h6"
                component="h6"
                sx={{ padding: "15px 0px" }}
              >
                <b>BUY WITH CONFIDENCE</b>
              </Typography>
            </Item>
          </Grid>
          <Grid item xs={12} style={{ paddingTop: "0px" }}>
            <Item
              className="page_bg"
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Box sx={{ width: isMobile ? "95%" : "55%" }}>
                <Item className="page_bg">
                  <Typography>
                    Your Approval Loan Amount is:{" "}
                    {loading ? (
                      <CircularProgress size={20} /> // Show Spinner
                    ) : (
                      <b>${inputValue}</b> // Show Result
                    )}
                  </Typography>
                </Item>
              </Box>
            </Item>
            <Item
              style={{ display: "flex", justifyContent: "center" }}
              className="page_bg"
            >
              <CustomAutocomplete
                value={
                  termOptions.find((option) => option.value == term) || null
                }
                id="distance-select"
                variant="outlined"
                sx={{ width: isMobile ? "80%" : "55%" }}
                disableClearable
                onChange={handleTermChange}
                options={termOptions}
                autoHighlight
                getOptionLabel={(option) => option.label}
                style={{ borderColor: "#4891FF" }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="What Term Are You Interested In?"
                    InputLabelProps={{
                      classes: {
                        root: "input_fonts",
                      },
                      sx: {
                        fontWeight: "bold",
                        color: "#4891FF",
                      },
                    }}
                  />
                )}
              />
            </Item>
            <Item
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
              }}
              className="page_bg"
            >
              <div style={{ width: isMobile ? "80%" : "55%" }}>
                <CustomTextField
                  id="outlined-basic"
                  label="Vehicle Purchase Price"
                  variant="outlined"
                  fullWidth
                  autoComplete="off"
                  value={adjustedPrincipal || financeAmount}
                  onChange={handleFinanceAmountChange}
                  onBlur={handleBlur}
                  inputRef={inputRef}
                  InputProps={{
                    sx: {
                      fontSize: "16px",
                      fontWeight: "semibold", // Bold the input field value
                    },
                    startAdornment: (
                      <InputAdornment position="start">$</InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {loading && (
                          <CircularProgress
                            sx={{ color: "#4891FF" }}
                            size={20}
                          />
                        )}
                      </InputAdornment>
                    ),
                  }}
                />
                {inputError && (
                  <div style={{ color: "red", marginTop: "5px" }}>
                    {inputError}
                  </div>
                )}
              </div>
            </Item>
            <Item>
              <CustomTextField
                id="outlined-basic"
                label="Your Monthly Payment"
                variant="outlined"
                sx={{ width: isMobile ? "80%" : "55%" }}
                autoComplete="off"
                value={memberInfo.calculate_emi}
                onChange={handleMonthlyPaymentChange}
                InputProps={{
                  sx: {
                    fontSize: "16px",
                    fontWeight: "semibold", // Bold the input field value
                  },
                  startAdornment: (
                    <InputAdornment position="start">$</InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      {loading && (
                        <CircularProgress sx={{ color: "#4891FF" }} size={20} />
                      )}
                    </InputAdornment>
                  ),
                }}
              />
              {monthlyPaymentErrorMessage && (
                <div
                  style={{
                    color: "red",
                    fontSize: "14px",
                    marginTop: "8px",
                  }}
                >
                  {monthlyPaymentErrorMessage}
                </div>
              )}
            </Item>
            <Item
              className="page_bg"
              style={{ display: "flex", justifyContent: "center" }}
            >
              <Box
                sx={{
                  width: isMobile ? "80%" : "55%",
                  position: "relative",
                }}
              >
                <CustomTextField
                  id="outlined-basic"
                  label="Down Payment / Trade In Amount"
                  variant="outlined"
                  sx={{
                    width: "100%",
                    background: "#ffffff",
                  }}
                  value={newDownPayment}
                  onChange={handleDownPaymentChange}
                  onBlur={handleDownPaymentBlur}
                  autoComplete="off"
                  InputProps={{
                    sx: {
                      fontSize: "16px",
                      fontWeight: "semibold",
                    },
                    placeholder: "Enter Your Down Payment",
                    startAdornment: (
                      <InputAdornment
                        position="start"
                        sx={{
                          fontSize: "16px",
                        }}
                      >
                        $
                      </InputAdornment>
                    ),
                  }}
                />

                {/* Info Icon positioned absolutely to the right of the input */}
                <Tooltip
                  title="Down Payment will be subtracted from your loan lowering your monthly payment, unless box below is checked."
                  open={iconOpen}
                  onClose={() => setIconOpen(false)}
                  disableFocusListener
                  disableHoverListener
                  disableTouchListener
                  arrow
                >
                  <IconButton
                    onClick={handleTooltipToggle}
                    sx={{
                      position: "absolute",
                      top: "50%",
                      transform: "translateY(-50%)",
                      color: "#212121"
                    }}
                  >
                    <InfoOutlinedIcon />
                  </IconButton>
                </Tooltip>

                {downPaymentErrorMessage && (
                  <div
                    style={{
                      color: "red",
                      fontSize: "14px",
                      marginTop: "8px",
                    }}
                  >
                    {downPaymentErrorMessage}
                  </div>
                )}
              </Box>
            </Item>
            <Item
              className="page_bg"
              style={{ display: "flex", justifyContent: "center", color: "#212121" }}
            >
              <Box
                sx={{
                  width: isMobile ? "80%" : "55%",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  textAlign: "center",
                }}
              >
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={isDownPaymentAddedToPrice}
                      onChange={handleDownPaymentCheckboxChange}
                      sx={{ padding: "4px" }}
                    />
                  }
                  label={
                    <Typography sx={{ fontSize: "16px", fontWeight: "500" }}>
                      Add Down Payment to Estimated Purchase Price
                    </Typography>
                  }
                  sx={{ marginLeft: 0 }}
                />
              </Box>
            </Item>
          </Grid>
          <Grid item xs={12} style={{ paddingTop: "0px" }}>
            <Item
              style={{ display: "flex", justifyContent: "center" }}
              className="page_bg"
            >
              {loading ? (
                <CircularProgress sx={{ color: "#4891FF" }} size={20} />
              ) : (
                <Typography>
                  <span style={{ fontWeight: "500" }}>
                    Estimated Purchase Price with Taxes <br /> and Fees (12%) :{" "}
                    <span style={{ fontSize: "16px", fontWeight: "bold" }}>
                      {" "}
                      ${" "}
                      {Number(totalPurchaseAmount.toFixed(2)).toLocaleString()}
                    </span>
                  </span>
                </Typography>
              )}
            </Item>
            <Item>
              <Typography variant="body1" style={{ fontWeight: 500 }}>
                This will be the Amount used to Search
                <br />
                for your Vehicle
              </Typography>
            </Item>
            <Item
              style={{ display: "flex", justifyContent: "center" }}
              className="page_bg"
            >
              <StyledButton
                isMobile={isMobile}
                variant="contained"
                onClick={
                  isGenericSearchShow === "true"
                    ? handleConfirmationModalShow
                    : handleShopLocalButton
                }
              >
                <Icon icon="phone-in-hand" size={40} />
                <span className="btn_class_landing_mobile">
                  SHOP LOCAL INVENTORY
                </span>
              </StyledButton>
            </Item>
          </Grid>
        </Grid>
      )}
      <SearchConfirmationModal
        show={showConfirmationModal}
        onHide={handleConfirmationModalClose}
      />
    </Box>
  );
};
export default SpecificSearchCarMobile;
