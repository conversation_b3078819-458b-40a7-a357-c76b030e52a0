import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import { useMediaQuery } from "react-responsive";
import { useCommandCenterApi } from "../api/commandCenterApi";
import {
  Autocomplete,
  Checkbox,
  FormControlLabel,
  IconButton,
} from "@mui/material";
import CircularProgress from "@mui/material/CircularProgress";
import InputAdornment from "@mui/material/InputAdornment";
import Tooltip from "@mui/material/Tooltip";
import Zoom from "@mui/material/Zoom";
import Icon from "../../../icon";
import { useNavigate } from "react-router-dom";
import Stack from "@mui/material/Stack";
import loaderGif from "../../../assets/gifs/loader.gif";
import SearchConfirmationModal from "../../landing/modals/searchConfirmationModal";
import { useEffect, useState } from "react";
import { callComponentActionApi } from "../../../util/callComponenetActionApi";
import { useDispatch } from "react-redux";
import { formatAmount } from "../../../util/formatNumber";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { useGlobalTutorialModal } from "../../../context/TutorialModalContext";
import { ImageBaseURL } from "../../../config";
import { hasSeenTutorial, shouldShowTutorial } from "../../../context/TutorialStorage";

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const labelStyle = {
  color: "#4891FF",
};

const CustomTextField = styled(TextField)({
  "& .MuiInputLabel-root": labelStyle, // Apply label style
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "bold", // Bold the input field value
    },
  },
});

const CustomAutocomplete = styled(Autocomplete)({
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "bold", // Bold the selected option value
    },
  },
  "& .MuiAutocomplete-option": {
    fontWeight: "bold", // Bold the dropdown options
  },
});

const StyledButton = styled(Button)({
  borderRadius: "10px",
  width: "50%",
  fontWeight: "600",
  padding: "14px",
  margin: "3px 0",
  backgroundColor: "#4891FF",
  "&:hover": {
    backgroundColor: "#4891FF",
  },
  "&.Mui-disabled": {
    backgroundColor: "#4891FF",
    color: "#fff",
    opacity: 0.8,
  },
});

const CommandCenterWeb = () => {
  const token = localStorage.getItem("token");
  const isBigScreen = useMediaQuery({ query: "(min-width: 2560px)" });
  const isGenericSearchShow = import.meta.env.VITE_IS_GENERIC_SEARCH_SHOW;
  const navigate = useNavigate();

  if (!token) {
    navigate("/");
  }
  const {
    loading,
    termOptions,
    term,
    handleTermChange,
    memberInfo,
    inputValue,
    inputError,
    totalPurchaseAmount,
    handleBlur,
    inputRef,
    componentMetaData,
    downPayment,
    downPaymentErrorMessage,
    handleDownPaymentBlur,
    handleDownPaymentChange,
    financeAmount,
    handleFinanceAmountChange,
    handleShopLocalButton,
    isShopLocalLoader,
    handleMonthlyPaymentChange,
    newDownPayment,
    adjustedPrincipal,
    monthlyPaymentErrorMessage,
    isDownPaymentAddedToPrice,
    handleDownPaymentCheckboxChange
  } = useCommandCenterApi(token);

  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const handleConfirmationModalShow = () => setShowConfirmationModal(true);
  const handleConfirmationModalClose = () => setShowConfirmationModal(false);
  const dispatch = useDispatch();
  useEffect(() => {
    let isMounted = true; // Add a flag to check if the component is still mounted

    if (!loading && isMounted) {
      try {
        const component = componentMetaData.find(
          (comp) => comp.slug === "command-center" && comp.type === "url"
        );
        if (!component) {
          console.error(`Component with slug "command-center" not found.`);
          return;
        }
        // Call the API with the component ID
        callComponentActionApi(dispatch, component.id);
      } catch (error) {
        console.error("Error handling button click:", error);
      }
    }

    // Cleanup function to reset the flag when the component is unmounted
    return () => {
      isMounted = false;
    };
  }, [loading, componentMetaData]);

  // for tutorial
  const { openTutorialModal } = useGlobalTutorialModal();
  useEffect(() => {
    const checkAndShowTutorial = async () => {
      if (loading) return; // Don't proceed if loading

      const localFlag = hasSeenTutorial("commandCenter_shopInventory");
      if (localFlag) return; // Already seen locally, skip

      const shouldShow = await shouldShowTutorial({
        tutorialKey: "commandCenter_shopInventory",
        dispatch,
      });

      if (shouldShow) {
        openTutorialModal({
          bodyImageSrc: ImageBaseURL + "tutorials/images/command-center-tutorial-feature-image.png",
          additionalTopText: (
            <span>Calculate your</span>
          ),
          mainText: (
            <span>Monthly Payment</span>
          ),
          subText: (
            <span>Next Click on Shop Local Inventory</span>
          ),
          audioSrc: ImageBaseURL + "tutorials/audios/command-center-audio.mp3"
        });
      }
    };

    checkAndShowTutorial();
  }, [loading]);


  return (
    <Box sx={{ flexGrow: 1, mt: isBigScreen ? 40 : 10 }}>
      {loading ? (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Stack alignItems="center" justifyContent="center" mt={24}>
            <img
              className="page_bg"
              src={loaderGif}
              alt="Loading..."
              style={{ width: "200px", height: "200px" }}
            />
            <span style={{ fontWeight: "600" }}>Loading Details...</span>
          </Stack>
        </Box>
      ) : (
        <Grid container spacing={1} sx={{ padding: "100px 0px" }}>
          <Grid item sm={12}>
            <Item
              className="page_bg"
              sx={{ marginTop: isBigScreen ? -20 : -10 }}
            >
              <Typography variant="h5" component="h5">
                <b>COMMAND CENTER</b>
              </Typography>
            </Item>
            <Item className="page_bg">
              <Typography variant="subtitle1" component="subtitle1">
                <span style={{ fontWeight: "600" }}>BUY WITH CONFIDENCE</span>
              </Typography>
            </Item>
          </Grid>
          <Grid
            container
            item
            xs={12}
            md={12}
            direction="column"
            alignItems="center"
            justifyContent="center"
            mt={2}
          >
            <Grid container spacing={4} direction="row" alignItems="center">
              <Grid item xs={12} style={{ width: "100%" }}>
                <Item
                  className="page_bg"
                  style={{ display: "flex", justifyContent: "center" }}
                >
                  <Box sx={{ width: isBigScreen ? "20%" : "30%" }}>
                    <Item className="page_bg">
                      <Typography>
                        Your Approval Loan Amount is:{" "}
                        {loading ? (
                          <CircularProgress size={20} /> // Show Spinner
                        ) : (
                          <b>${inputValue}</b> // Show Result
                        )}
                      </Typography>
                    </Item>
                  </Box>
                </Item>
                <Item
                  style={{ display: "flex", justifyContent: "center" }}
                  className="page_bg"
                >
                  <Box sx={{ width: isBigScreen ? "20%" : "30%" }}>
                    <CustomAutocomplete
                      options={termOptions}
                      sx={{
                        width: "100%",
                        background: "#ffffff",
                      }}
                      value={
                        termOptions.find((option) => option.value == term) ||
                        null
                      }
                      onChange={handleTermChange}
                      getOptionLabel={(option) => option.label}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="What Term Are You Interested In?"
                          InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                              <>
                                {params.InputProps.endAdornment}
                                {loading && (
                                  <CircularProgress
                                    sx={{ color: "#4891FF" }}
                                    size={20}
                                  />
                                )}
                              </>
                            ),
                          }}
                          InputLabelProps={{
                            sx: {
                              fontWeight: "",
                              color: "#4891FF",
                            },
                          }}
                        />
                      )}
                    />
                  </Box>
                </Item>
                <Item
                  className="page_bg"
                  style={{ display: "flex", justifyContent: "center" }}
                >
                  <Box sx={{ width: isBigScreen ? "20%" : "30%" }}>
                    <CustomTextField
                      id="outlined-basic"
                      label="Vehicle Purchase Price"
                      variant="outlined"
                      sx={{
                        width: "100%",
                        background: "#ffffff",
                      }}
                      autoComplete="off"
                      value={adjustedPrincipal || financeAmount}
                      onChange={handleFinanceAmountChange}
                      onBlur={handleBlur}
                      inputRef={inputRef}
                      InputProps={{
                        sx: {
                          fontSize: "16px",
                        },
                        startAdornment: (
                          <InputAdornment
                            position="start"
                            sx={{
                              fontSize: "16px",
                            }}
                          >
                            $
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                </Item>
                {inputError && (
                  <span
                    style={{
                      display: "flex",
                      justifyContent: "center",
                      color: "red",
                    }}
                  >
                    {inputError}
                  </span>
                )}
                <Item
                  className="page_bg"
                  style={{ display: "flex", justifyContent: "center" }}
                >
                  <Box sx={{ width: isBigScreen ? "20%" : "30%" }}>
                    <CustomTextField
                      id="outlined-basic"
                      label="Your Monthly Payment"
                      variant="outlined"
                      sx={{
                        width: "100%",
                        background: "#ffffff",
                      }}
                      autoComplete="off"
                      value={memberInfo.calculate_emi}
                      onChange={handleMonthlyPaymentChange}
                      InputProps={{
                        sx: {
                          fontSize: "16px",
                          fontWeight: "semibold", // Bold the input field value
                        },
                        startAdornment: (
                          <InputAdornment position="start">$</InputAdornment>
                        ),
                      }}
                    />
                    {monthlyPaymentErrorMessage && (
                      <div
                        style={{
                          color: "red",
                          fontSize: "14px",
                          marginTop: "8px",
                        }}
                      >
                        {monthlyPaymentErrorMessage}
                      </div>
                    )}
                  </Box>
                </Item>
                <Item
                  className="page_bg"
                  style={{ display: "flex", justifyContent: "center" }}
                >
                  <Box
                    sx={{
                      width: isBigScreen ? "20%" : "30%",
                      position: "relative",
                    }}
                  >
                    <CustomTextField
                      id="outlined-basic"
                      label="Down Payment / Trade In Amount"
                      variant="outlined"
                      sx={{
                        width: "100%",
                        background: "#ffffff",
                      }}
                      value={newDownPayment}
                      onChange={handleDownPaymentChange}
                      onBlur={handleDownPaymentBlur}
                      autoComplete="off"
                      InputProps={{
                        sx: {
                          fontSize: "16px",
                          fontWeight: "semibold",
                        },
                        placeholder: "Enter Your Down Payment",
                        startAdornment: (
                          <InputAdornment
                            position="start"
                            sx={{
                              fontSize: "16px",
                            }}
                          >
                            $
                          </InputAdornment>
                        ),
                      }}
                    />

                    {/* Info Icon positioned absolutely to the right of the input */}
                    <Tooltip
                      title="Down Payment will be subtracted from your loan lowering your monthly payment, unless box below is checked"
                      arrow
                    >
                      <IconButton
                        sx={{
                          position: "absolute",
                          top: "50%",
                          transform: "translateY(-50%)",
                          color: "#212121"
                        }}
                      >
                        <InfoOutlinedIcon />
                      </IconButton>
                    </Tooltip>

                    {downPaymentErrorMessage && (
                      <div
                        style={{
                          color: "red",
                          fontSize: "14px",
                          marginTop: "8px",
                        }}
                      >
                        {downPaymentErrorMessage}
                      </div>
                    )}
                  </Box>
                </Item>
                <Item
                  className="page_bg"
                  style={{ display: "flex", justifyContent: "center" }}
                >
                  <Box sx={{ width: isBigScreen ? "20%" : "30%", color: "#212121" }}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={isDownPaymentAddedToPrice}
                          onChange={handleDownPaymentCheckboxChange}
                        />
                      }
                      label="Add Down Payment to Estimated Purchase Price"
                    />
                  </Box>
                </Item>
                <Item className="page_bg">
                  <Typography>
                    Estimated Purchase Price with Taxes and Fees (12%):{" "}
                    {loading ? (
                      <CircularProgress size={20} /> // Show Spinner
                    ) : (
                      <b>{formatAmount(totalPurchaseAmount)}</b> // Show Result
                    )}
                  </Typography>
                  <Typography>
                    This will be the Amount used to Search for your Vehicle
                  </Typography>
                </Item>
                <Item
                  className="page_bg"
                  style={{ display: "flex", justifyContent: "center" }}
                >
                  <Box sx={{ width: isBigScreen ? "40%" : "60%" }}>
                    <Tooltip
                      TransitionComponent={Zoom}
                      title="Shop Local Inventory"
                      arrow
                    >
                      <StyledButton
                        variant="contained"
                        onClick={
                          isGenericSearchShow === "true"
                            ? handleConfirmationModalShow
                            : handleShopLocalButton
                        }
                      >
                        {isShopLocalLoader ? (
                          <CircularProgress size={24} color="inherit" />
                        ) : (
                          <>
                            <Icon
                              icon="phone-in-hand"
                              size={40}
                              style={{ marginRight: "20px" }}
                            />
                            <span className="btn_class_landing">
                              SHOP LOCAL INVENTORY
                            </span>
                          </>
                        )}
                      </StyledButton>
                    </Tooltip>
                  </Box>
                </Item>
              </Grid>
              <Grid className="row" md={12}></Grid>
            </Grid>
          </Grid>
        </Grid>
      )}
      <SearchConfirmationModal
        show={showConfirmationModal}
        onHide={handleConfirmationModalClose}
      />
    </Box>
  );
};

export default CommandCenterWeb;
