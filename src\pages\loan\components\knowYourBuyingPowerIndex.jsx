import { useMediaQuery } from 'react-responsive'
import KnowYourBuyingPowerMobile from './knowYourBuyingPowerMobile';
import KnowYourBuyingPowerWeb from './knowYourBuyingPowerWeb';
import MasterLayout from '../../../components/layouts/masterLayout';

function CommandCenterIndex() {
    const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' })
    return (
        <MasterLayout>
            {isTabletOrMobile ? <KnowYourBuyingPowerMobile /> : <KnowYourBuyingPowerWeb />}
        </MasterLayout>
    );
}

export default CommandCenterIndex;