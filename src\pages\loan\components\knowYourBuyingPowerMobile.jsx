import { styled } from "@mui/material/styles";
import { useMediaQuery } from "react-responsive";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import Stack from "@mui/material/Stack";
import { ImageBaseURL } from "../../../config";
import useLandingApi from "../../landing/api/landingApi";
import loaderGif from "../../../assets/gifs/loader.gif";
import { useNavigate } from "react-router-dom";
import TextField from "@mui/material/TextField";
import { Typography, Autocomplete } from "@mui/material";
import Switch from "@mui/material/Switch";
import SearchIcon from "@mui/icons-material/Search";
import useKnowYourBuyingPowerApi from "../api/knowBuyingPowerApi";
import InputAdornment from "@mui/material/InputAdornment";
import { formatAmount } from '../../../util/formatNumber';
import CircularProgress from "@mui/material/CircularProgress"; // Import Spinner from Material-UI
import { useDispatch } from "react-redux";
import { useEffect } from "react";
import { callComponentActionApi } from "../../../util/callComponenetActionApi";

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const labelStyle = {
  color: "#4891FF",
};

const StyledButton = styled(({ ...other }) => <Button {...other} />)(
  ({ isMobile }) => ({
    borderRadius: "10px",
    width: isMobile ? "50%" : "25%",
    fontSize: "15px",
    fontWeight: "600",
    padding: "15px",
    backgroundColor: "#4891FF",
    // Hover state
    "&:hover": {
      backgroundColor: "#357AE8",
    },

    // Disabled state
    "&.Mui-disabled": {
      backgroundColor: "#4891FF",
      color: "#4891FF",
      opacity: 0.6,
      cursor: "not-allowed",
    },
  })
);

const CustomAutocomplete = styled(Autocomplete)({
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "bold", // Bold the selected option value
    },
  },
  "& .MuiAutocomplete-option": {
    fontWeight: "bold", // Bold the dropdown options
  },
});

const CustomTextField = styled(TextField)({
  "& .MuiInputLabel-root": labelStyle, // Apply label style
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "bold", // Bold the input field value
    },
  },
});

const KnowYourBuyingPowerMobile = () => {
  const isMobile = useMediaQuery({ maxWidth: 767 });
  const { memberInfo, loading } = useLandingApi();
  const navigate = useNavigate();
  const token = !!localStorage.getItem("token");
  const {
    carLookingOptions,
    handleSwitchChange,
    isIncludeTrade,
    downPayment,
    handleDownPaymentChange,
    totalPurchasePower,
    userWorth,
    userOwe,
    handleUserWorthChange,
    handleUserOweChange,
    errorMessage,
    handleSearchButton,
    userCarLookingFor,
    handleCarLookingChange,
    inputRef,
    downPaymentErrorMessage,
    worthErrorMessage,
    handleDownPaymentBlur,
    handleUserWorthBlur,
    handleUserOweBlur,
    handleDownPaymentClick,
    isPurchasePowerLoading,
    isSearchButtonLoader,
    componentMetaData
  } = useKnowYourBuyingPowerApi();
  const selectedCarLookingOption =
    carLookingOptions.find((option) => option.value === userCarLookingFor) ||
    null;
  if (!token) {
    navigate("/");
  }
  const dispatch = useDispatch();
  useEffect(() => {
    let isMounted = true; // Add a flag to check if the component is still mounted
  
    if (!loading && componentMetaData && componentMetaData.length > 0 && isMounted) {
      try {
        const component = componentMetaData.find((comp) => comp.slug === "know-your-buying-power" && comp.type === "url");
        if (!component) {
          console.error(`Component with slug "know-your-buying-power" not found.`);
          return;
        }
        // Call the API with the component ID
        callComponentActionApi(dispatch, component.id);
      } catch (error) {
        console.error("Error handling button click:", error);
      }
    }
  
    // Cleanup function to reset the flag when the component is unmounted
    return () => {
      isMounted = false;
    };
  }, [loading, componentMetaData]);
  return (
    <Box sx={{ flexGrow: 1, margin: "8vh 0vh 5vh 0vh" }}>
      {loading ? (
        <Box
          sx={{
            flexGrow: 1,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Stack alignItems="center" justifyContent="center" mt={16}>
            <img
              className="page_bg"
              src={loaderGif}
              alt="Loading..."
              style={{ width: "200px", height: "200px" }}
            />
            <span style={{ fontWeight: "600" }}>Loading Details...</span>
          </Stack>
        </Box>
      ) : (
        <Box sx={{ flexGrow: 1, margin: "8vh 0vh 5vh 0vh" }}>
          {isMobile ? (
            <Grid container spacing={2} sx={{ padding: "5px 0px 0px 0px" }}>
              <Grid item xs={12}>
                <Item className="page_bg">
                  <img
                    alt="cu Logo"
                    src={memberInfo.cuLogo}
                    style={
                      isMobile
                        ? { maxWidth: "50%", maxHeight: "50%" }
                        : { maxWidth: "35%", maxHeight: "35%" }
                    }
                  />
                </Item>
              </Grid>
              <Grid item xs={12} style={{ paddingTop: "10px" }}>
                <Grid item sm={6}>
                  <Item className="page_bg">
                    <div className="card-main-mobile page_bg">
                      <div className="card-inner">
                        <div className="front_mobile">
                          <img
                            src={ImageBaseURL + "card_image.png"}
                            className="map-img"
                            alt="Card"
                          />
                          <div
                            className="card-row card_font_mobile"
                            style={{ marginTop: "30%" }}
                          >
                            <div className="loan-ids-grid-mb">
                              <span>{memberInfo.loan_id[0]}</span>
                              <span>{memberInfo.loan_id[1]}</span>
                              <span>{memberInfo.loan_id[2]}</span>
                              <span>{memberInfo.loan_id[3]}</span>
                            </div>
                          </div>
                          <div
                            className="row text-start card-holder-mobile fw-400"
                            style={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                            }}
                          >
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                flexGrow: 1,
                              }}
                            >
                              <span>
                                {memberInfo.member_first_name +
                                  " " +
                                  memberInfo.member_last_name}
                              </span>
                            </div>
                            <div
                              style={{
                                display: "flex",
                                flexDirection: "column",
                                textAlign: "center",
                                marginTop: "-15px",
                                marginLeft: "25px",
                              }}
                            >
                              <span>VALID THRU:</span>
                              <span>{memberInfo.preapproved_expire}</span>
                            </div>
                            <div
                              style={{
                                display: "flex",
                                flexDirection: "column",
                                textAlign: "left",
                              }}
                            >
                              <span>
                                AMOUNT:&nbsp;$ {memberInfo.preapproved_amount}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Item>
                </Grid>
              </Grid>
              <Grid item xs={12} style={{ paddingTop: "0px" }}>
                <Grid item xs={12}>
                  <Item className="page_bg">
                    <Typography variant="h6" component="h6">
                      <b>Want to Know Your Buying Power?</b>
                    </Typography>
                  </Item>
                  <Item className="page_bg">
                    <Typography>
                      <span style={{ fontWeight: "500" }}>
                        Locate the Best Vehicles Matching{" "}
                        <span style={{ fontSize: "16px" }}>Your Budget</span>
                      </span>
                    </Typography>
                  </Item>
                </Grid>
                <Grid item xs={12} style={{ paddingTop: "0px" }}>
                  <Item
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                    }}
                    className="page_bg"
                  >
                    <CustomAutocomplete
                      options={carLookingOptions}
                      sx={{
                        width: "90%",
                        background: "#ffffff",
                      }}
                      value={selectedCarLookingOption}
                      onChange={handleCarLookingChange}
                      getOptionLabel={(option) => option.label}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Looking For"
                          InputProps={{
                            ...params.InputProps,
                            readOnly: true, // Make the input field read-only
                            onFocus: (e) => e.target.blur(), // Prevent input from gaining focus
                          }}
                          sx={{
                            cursor: "pointer", // Apply pointer cursor style to the entire TextField
                          }}
                          InputLabelProps={{
                            sx: {
                              fontWeight: "",
                              color: "#4891FF",
                            },
                          }}
                        />
                      )}
                      disableClearable
                      openOnFocus
                    />
                  </Item>
                  <Item
                    style={{ display: "flex", justifyContent: "center" }}
                    className="page_bg"
                  >
                    <CustomTextField
                      id="outlined-basic"
                      label="Current Loan Approval Amount"
                      variant="outlined"
                      sx={{
                        width: "90%",
                        background: "#ffffff",
                      }}
                      autoComplete="off"
                      value={memberInfo.preapproved_amount.toLocaleString()}
                      InputProps={{
                        readOnly: true,
                        sx: {
                          fontSize: "16px",
                          fontWeight: "semibold",
                        },
                        startAdornment: (
                          <InputAdornment
                            position="start"
                            sx={{
                              fontSize: "16px",
                            }}
                          >
                            $
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Item>
                  <Item
                    style={{ display: "flex", justifyContent: "center" }}
                    className="page_bg"
                  >
                    <CustomTextField
                      id="outlined-basic"
                      label="Down Payment"
                      variant="outlined"
                      sx={{
                        width: "90%",
                        background: "#ffffff",
                      }}
                      value={downPayment}
                      onChange={handleDownPaymentChange}
                      onBlur={handleDownPaymentBlur}
                      onClick={handleDownPaymentClick}
                      inputRef={inputRef}
                      autoComplete="off"
                      InputProps={{
                        sx: {
                          fontSize: "16px",
                          fontWeight: "semibold",
                        },
                        startAdornment: (
                          <InputAdornment
                            position="start"
                            sx={{
                              fontSize: "16px",
                            }}
                          >
                            $
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Item>
                  {downPaymentErrorMessage && (
                    <Item
                      style={{
                        display: "flex",
                        justifyContent: "center",
                        color: "red",
                      }}
                    >
                      {downPaymentErrorMessage}
                    </Item>
                  )}
                </Grid>
                <Grid
                  className="row"
                  xs={12}
                  style={{ paddingTop: "0px", display: "none" }}
                >
                  <Grid item xs={6}>
                    <Item className="page_bg">
                      <Typography style={{ marginTop: "8px" }}>
                        <b>Include Trade?</b>
                      </Typography>
                    </Item>
                  </Grid>
                  <Grid item xs={6}>
                    <Item className="page_bg">
                      <Switch
                        checked={isIncludeTrade}
                        onChange={handleSwitchChange}
                      />
                    </Item>
                  </Grid>
                </Grid>
                {isIncludeTrade && (
                  <Grid
                    item
                    xs={12}
                    style={{ paddingTop: "0px", display: "none" }}
                  >
                    <Item
                      style={{ display: "flex", justifyContent: "center" }}
                      className="page_bg"
                    >
                      <CustomTextField
                        id="outlined-basic"
                        label="What is Worth"
                        variant="outlined"
                        sx={{
                          width: "90%",
                          background: "#ffffff",
                        }}
                        autoComplete="off"
                        value={userWorth}
                        onChange={handleUserWorthChange}
                        onBlur={handleUserWorthBlur}
                        InputProps={{
                          sx: {
                            fontSize: "16px",
                            fontWeight: "semibold",
                          },
                          startAdornment: (
                            <InputAdornment
                              position="start"
                              sx={{
                                fontSize: "16px",
                              }}
                            >
                              $
                            </InputAdornment>
                          ),
                        }}
                      />
                      {worthErrorMessage && (
                        <div style={{ color: "red", marginTop: "5px" }}>
                          {worthErrorMessage}
                        </div>
                      )}
                    </Item>
                    <Item
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                      }}
                      className="page_bg"
                    >
                      <CustomTextField
                        id="outlined-basic"
                        label="What I Owe"
                        variant="outlined"
                        sx={{
                          width: "90%",
                          background: "#ffffff",
                        }}
                        value={userOwe}
                        onChange={handleUserOweChange}
                        onBlur={handleUserOweBlur}
                        autoComplete="off"
                        InputProps={{
                          sx: {
                            fontSize: "16px",
                            fontWeight: "semibold",
                          },
                          startAdornment: (
                            <InputAdornment
                              position="start"
                              sx={{
                                fontSize: "16px",
                              }}
                            >
                              $
                            </InputAdornment>
                          ),
                        }}
                      />
                      {errorMessage && (
                        <div style={{ color: "red", marginTop: "5px" }}>
                          {errorMessage}
                        </div>
                      )}
                    </Item>
                  </Grid>
                )}
                <Grid item xs={12} style={{ paddingTop: "0px" }}>
                  <Item className="page_bg">
                    <Typography>
                      Your Calculated Estimated Purchase Power
                      <br />
                      is {isPurchasePowerLoading ? (
                        <CircularProgress size={20} /> // Show Spinner
                      ) : (
                        <b>{formatAmount(totalPurchasePower)}</b> // Show Result
                      )}
                    </Typography>
                    <Typography>
                      Estimated Purchase Price with 12% Tax and Fees
                    </Typography>
                  </Item>
                </Grid>
                <Grid item xs={12} style={{ paddingTop: "0px" }}>
                  <Item className="page_bg">
                    <StyledButton
                      variant="contained"
                      isMobile={isMobile}
                      onClick={handleSearchButton}
                      startIcon={isSearchButtonLoader ? null : <SearchIcon />}
                    >
                    {isSearchButtonLoader ? (
                      <CircularProgress size={24} color="inherit" />
                    ) : (
                      "Search"
                    )}
                    </StyledButton>
                  </Item>
                </Grid>
              </Grid>
            </Grid>
          ) : (
            <Grid container spacing={2} sx={{ padding: "10px 0px 0px 0px" }}>
              <Grid item xs={12}>
                <Item className="page_bg">
                  <img
                    alt="cu Logo"
                    src={memberInfo.cuLogo}
                    style={{ maxWidth: "35%", maxHeight: "35%" }}
                  />
                </Item>
              </Grid>
              <Grid
                item
                xs={12}
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Grid item sm={6}>
                  <Item className="page_bg">
                    <div className="card-main-tab page_bg">
                      <div className="card-inner-tab">
                        <div className="front_mobile">
                          <img
                            src={ImageBaseURL + "card_image.png"}
                            className="map-img"
                            alt="Card"
                          />
                          <div
                            className="card-row card_font_tab"
                            style={{ marginTop: "32%" }}
                          >
                            <div className="loan-ids-grid">
                              <span>{memberInfo.loan_id[0]}</span>
                              <span>{memberInfo.loan_id[1]}</span>
                              <span>{memberInfo.loan_id[2]}</span>
                              <span>{memberInfo.loan_id[3]}</span>
                            </div>
                          </div>
                          <div
                            className="row text-start card-holder-mobile fw-400 mt-2"
                            style={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                            }}
                          >
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                flexGrow: 1,
                              }}
                            >
                              {memberInfo.member_first_name +
                                " " +
                                memberInfo.member_last_name}
                            </div>
                            <div
                              style={{
                                display: "flex",
                                flexDirection: "column",
                                textAlign: "center",
                                marginTop: "-15px",
                                marginLeft: "10px",
                              }}
                            >
                              <span>VALID THRU:</span>
                              <span>{memberInfo.preapproved_expire}</span>
                            </div>
                            <div
                              style={{
                                display: "flex",
                                flexDirection: "column",
                                textAlign: "left",
                              }}
                            >
                              <span>
                                AMOUNT:&nbsp;$ {memberInfo.preapproved_amount}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Item>
                </Grid>
              </Grid>
              <Grid item xs={12}>
                <Grid item sm={12}>
                  <Item className="page_bg">
                    {" "}
                    <Typography variant="h6" component="h6">
                      <b>Want to Know Your Buying Power?</b>
                    </Typography>
                  </Item>
                  <Item className="page_bg">
                    {" "}
                    <Typography>
                      <span style={{ fontWeight: "500" }}>
                        Locate the Best Vehicles Matching{" "}
                        <span style={{ fontSize: "16px" }}>Your Budget</span>
                      </span>
                    </Typography>
                  </Item>
                </Grid>
                <Grid item xs={12} style={{ paddingTop: "0px" }}>
                  <Item
                    style={{ display: "flex", justifyContent: "center" }}
                    className="page_bg"
                  >
                    <CustomAutocomplete
                      options={carLookingOptions}
                      sx={{
                        width: "55%",
                        background: "#ffffff",
                      }}
                      value={selectedCarLookingOption}
                      onChange={handleCarLookingChange}
                      getOptionLabel={(option) => option.label}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Looking For"
                          InputProps={{
                            ...params.InputProps,
                            readOnly: true, // Make the input field read-only
                            onFocus: (e) => e.target.blur(), // Prevent input from gaining focus
                          }}
                          sx={{
                            cursor: "pointer", // Apply pointer cursor style to the entire TextField
                          }}
                          InputLabelProps={{
                            sx: {
                              fontWeight: "",
                              color: "#4891FF",
                            },
                          }}
                        />
                      )}
                      disableClearable
                      openOnFocus
                    />
                  </Item>
                  <Item
                    style={{ display: "flex", justifyContent: "center" }}
                    className="page_bg"
                  >
                    <CustomTextField
                      id="outlined-basic"
                      label="Current Loan Approval Amount"
                      variant="outlined"
                      sx={{
                        width: "55%",
                        background: "#ffffff",
                      }}
                      autoComplete="off"
                      value={memberInfo.preapproved_amount.toLocaleString()}
                      InputProps={{
                        readOnly: true,
                        sx: {
                          fontSize: "16px",
                          fontWeight: "semibold",
                        },
                        startAdornment: (
                          <InputAdornment
                            position="start"
                            sx={{
                              fontSize: "16px",
                            }}
                          >
                            $
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Item>
                  <Item
                    style={{ display: "flex", justifyContent: "center" }}
                    className="page_bg"
                  >
                    <CustomTextField
                      id="outlined-basic"
                      label="Down Payment"
                      variant="outlined"
                      sx={{
                        width: "55%",
                        background: "#ffffff",
                      }}
                      value={downPayment}
                      onChange={handleDownPaymentChange}
                      onBlur={handleDownPaymentBlur}
                      onClick={handleDownPaymentClick}
                      inputRef={inputRef}
                      autoComplete="off"
                      type="tel" // Triggers numeric keyboard on mobile
                      inputMode="numeric" // Improves numeric input compatibility
                      InputProps={{
                        sx: {
                          fontSize: "16px",
                          fontWeight: "semibold",
                        },
                        startAdornment: (
                          <InputAdornment
                            position="start"
                            sx={{
                              fontSize: "16px",
                            }}
                          >
                            $
                          </InputAdornment>
                        ),
                      }}
                    />
                    {downPaymentErrorMessage && (
                      <div style={{ color: "red", marginTop: "5px" }}>
                        {downPaymentErrorMessage}
                      </div>
                    )}
                  </Item>
                </Grid>
                <Grid
                  className="row"
                  xs={12}
                  style={{ paddingTop: "0px", display: "none" }}
                >
                  <Grid item xs={6}>
                    <Item className="page_bg">
                      <Typography
                        style={{ marginTop: "8px", marginLeft: "116px" }}
                      >
                        <b>Include Trade?</b>
                      </Typography>
                    </Item>
                  </Grid>
                  <Grid item xs={6}>
                    <Item className="page_bg">
                      <Switch
                        checked={isIncludeTrade}
                        onChange={handleSwitchChange}
                      />
                    </Item>
                  </Grid>
                </Grid>
                {isIncludeTrade && (
                  <Grid item xs={12} style={{ paddingTop: "0px" }}>
                    <Item
                      style={{ display: "flex", justifyContent: "center" }}
                      className="page_bg"
                    >
                      <CustomTextField
                        id="outlined-basic"
                        label="What is Worth"
                        variant="outlined"
                        sx={{
                          width: "55%",
                          background: "#ffffff",
                        }}
                        autoComplete="off"
                        value={userWorth}
                        onChange={handleUserWorthChange}
                        onBlur={handleUserWorthBlur}
                        InputProps={{
                          sx: {
                            fontSize: "16px",
                            fontWeight: "semibold",
                          },
                          startAdornment: (
                            <InputAdornment
                              position="start"
                              sx={{
                                fontSize: "16px",
                              }}
                            >
                              $
                            </InputAdornment>
                          ),
                        }}
                      />
                      {worthErrorMessage && (
                        <div style={{ color: "red", marginTop: "5px" }}>
                          {worthErrorMessage}
                        </div>
                      )}
                    </Item>
                    <Item
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                      }}
                      className="page_bg"
                    >
                      <CustomTextField
                        id="outlined-basic"
                        label="What I Owe"
                        variant="outlined"
                        sx={{
                          width: "55%",
                          background: "#ffffff",
                        }}
                        value={userOwe}
                        onChange={handleUserOweChange}
                        onBlur={handleUserOweBlur}
                        autoComplete="off"
                        InputProps={{
                          sx: {
                            fontSize: "16px",
                            fontWeight: "semibold",
                          },
                          startAdornment: (
                            <InputAdornment
                              position="start"
                              sx={{
                                fontSize: "16px",
                              }}
                            >
                              $
                            </InputAdornment>
                          ),
                        }}
                      />
                      {errorMessage && (
                        <div style={{ color: "red", marginTop: "5px" }}>
                          {errorMessage}
                        </div>
                      )}
                    </Item>
                  </Grid>
                )}
                <Grid item xs={12} style={{ paddingTop: "0px" }}>
                  <Item className="page_bg">
                    <Typography>
                      Your Calculated Estimated Purchase Power
                      <br />
                      is {isPurchasePowerLoading ? (
                        <CircularProgress size={20} /> // Show Spinner
                      ) : (
                        <b>{formatAmount(totalPurchasePower)}</b> // Show Result
                      )}
                    </Typography>
                    <Typography>
                      Estimated Purchase Price with 12% Tax and Fees
                    </Typography>
                  </Item>
                </Grid>
                <Grid item xs={12} style={{ paddingTop: "0px" }}>
                  <Item className="page_bg">
                    <StyledButton
                      variant="contained"
                      isMobile={isMobile}
                      startIcon={isSearchButtonLoader ? null : <SearchIcon />}
                      onClick={handleSearchButton}
                    >
                    {isSearchButtonLoader ? (
                      <CircularProgress size={24} color="inherit" />
                    ) : (
                      "Search"
                    )}
                    </StyledButton>
                  </Item>
                </Grid>
              </Grid>
            </Grid>
          )}
        </Box>
      )}
    </Box>
  );
};

export default KnowYourBuyingPowerMobile;
