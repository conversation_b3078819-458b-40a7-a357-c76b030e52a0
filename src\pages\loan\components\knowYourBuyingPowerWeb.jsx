import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import { ImageBaseURL } from "../../../config";
import MaserLayout from "../../../components/layouts/masterLayout";
import Stack from "@mui/material/Stack";
import useLandingApi from "../../landing/api/landingApi";
import useKnowYourBuyingPowerApi from "../api/knowBuyingPowerApi";
import { useMediaQuery } from "react-responsive";
import { useNavigate } from "react-router-dom";
import loaderGif from "../../../assets/gifs/loader.gif";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import Switch from "@mui/material/Switch";
import { Autocomplete } from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import InputAdornment from "@mui/material/InputAdornment";
import { formatAmount } from '../../../util/formatNumber';
import CircularProgress from "@mui/material/CircularProgress"; // Import Spinner from Material-UI
import { callComponentActionApi } from "../../../util/callComponenetActionApi";
import { useDispatch } from "react-redux";
import { useEffect } from "react";

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const labelStyle = {
  color: "#4891FF",
};

const StyledButtonSearch = styled(Button)({
  borderRadius: "10px",
  width: "20%",
  fontWeight: "600",
  padding: "14px",
  margin: "3px 0",
  backgroundColor: "#4891FF",
  "&:hover": {
    backgroundColor: "primary",
  },
  "&.Mui-disabled": {
    backgroundColor: "#4891FF",
    color: "#fff",
    opacity: 0.8,
  },
});

const StyledButton4K = styled(Button)({
  borderRadius: "10px",
  width: "50%",
  fontWeight: "600",
  padding: "14px",
  margin: "3px 0",
  backgroundColor: "#4891FF",
  "&:hover": {
    backgroundColor: "primary",
  },
  "&.Mui-disabled": {
    backgroundColor: "#4891FF",
    color: "#fff",
    opacity: 0.8,
  },
});

const CustomTextField = styled(TextField)({
  "& .MuiInputLabel-root": labelStyle, // Apply label style
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "bold", // Bold the input field value
    },
  },
});

const CustomAutocomplete = styled(Autocomplete)({
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "bold", // Bold the selected option value
    },
  },
  "& .MuiAutocomplete-option": {
    fontWeight: "bold", // Bold the dropdown options
  },
});

const KnowYouPowerWeb = () => {
  const { memberInfo, loading } = useLandingApi();
  const {
    carLookingOptions,
    handleSwitchChange,
    isIncludeTrade,
    downPayment,
    handleDownPaymentChange,
    totalPurchasePower,
    userWorth,
    userOwe,
    handleUserWorthChange,
    handleUserOweChange,
    errorMessage,
    handleSearchButton,
    userCarLookingFor,
    handleCarLookingChange,
    isSearchDisabled,
    inputRef,
    downPaymentErrorMessage,
    worthErrorMessage,
    handleDownPaymentBlur,
    handleUserWorthBlur,
    handleUserOweBlur,
    handleDownPaymentClick,
    isPurchasePowerLoading,
    isSearchButtonLoader,
    componentMetaData
  } = useKnowYourBuyingPowerApi();
  const isBigScreen = useMediaQuery({ query: "(min-width: 1824px)" });
  const navigate = useNavigate();
  const selectedCarLookingOption =
    carLookingOptions.find((option) => option.value === userCarLookingFor) ||
    null;
  const token = localStorage.getItem("token") ? true : false;
  if (!token) {
    navigate("/");
  }

  const dispatch = useDispatch();
    useEffect(() => {
        let isMounted = true; // Add a flag to check if the component is still mounted
      
        if (!loading && isMounted) {
          try {
            const component = componentMetaData.find((comp) => comp.slug === "know-your-buying-power" && comp.type === "url");
            if (!component) {
              console.error(`Component with slug "know-your-buying-power" not found.`);
              return;
            }
            // Call the API with the component ID
            callComponentActionApi(dispatch, component.id);
          } catch (error) {
            console.error("Error handling button click:", error);
          }
        }
      
        // Cleanup function to reset the flag when the component is unmounted
        return () => {
          isMounted = false;
        };
      }, [loading, componentMetaData]);
      
  return (
    <MaserLayout>
      {loading ? (
        <Box
          sx={{
            flexGrow: 1,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Stack
            alignItems="center"
            justifyContent="center"
            mt={isBigScreen ? 64 : 24}
          >
            <img
              className="page_bg"
              src={loaderGif}
              alt="Loading..."
              style={{ width: "200px", height: "200px" }}
            />
            <span style={{ fontWeight: "600" }}>Loading Details...</span>
          </Stack>
        </Box>
      ) : (
        <Box sx={{ flexGrow: 1, marginTop: "15vh" }}>
          {isBigScreen ? (
            <Grid container spacing={1}>
              <Grid item md={12}>
                <Item className="page_bg">
                  <img
                    alt="Logo"
                    src={memberInfo.cuLogo}
                    style={{ maxWidth: "20%", maxHeight: "20%" }}
                  />
                </Item>
              </Grid>
              <Grid item md={12} className="row" my={12}>
                <Grid item md={6} mt={5}>
                  <Item className="page_bg">
                    <div className="card-main-4k page_bg">
                      <div className="card-inner">
                        <div className="front_4k">
                          <img
                            src={ImageBaseURL + "card_image.png"}
                            className="map-img"
                            alt="Card"
                          />
                          <div
                            className="card-row card_font_4k"
                            style={{ marginTop: "30%" }}
                          >
                            <div className="loan-ids-grid-4k">
                              <span>{memberInfo.loan_id[0]}</span>
                              <span>{memberInfo.loan_id[1]}</span>
                              <span>{memberInfo.loan_id[2]}</span>
                              <span>{memberInfo.loan_id[3]}</span>
                            </div>
                          </div>
                          <div
                            className="row text-start card-holder-4k fw-400"
                            style={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                            }}
                          >
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                flexGrow: 1,
                                marginTop: "20px",
                              }}
                            >
                              <span>
                                {memberInfo.member_first_name +
                                  " " +
                                  memberInfo.member_last_name}
                              </span>
                            </div>
                            <div
                              style={{
                                display: "flex",
                                flexDirection: "column",
                                textAlign: "center",
                                marginTop: "-30px",
                              }}
                            >
                              <span>VALID THRU:</span>
                              <span>{memberInfo.preapproved_expire}</span>
                            </div>
                            <div
                              style={{
                                display: "flex",
                                flexDirection: "column",
                                textAlign: "left",
                              }}
                            >
                              <span>
                                AMOUNT:&nbsp;$ {memberInfo.preapproved_amount}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Item>
                </Grid>
                <Grid item md={6} className="content_alignment" mt={5}>
                  <Grid item md={12}>
                    <Item className="page_bg">
                      <Typography variant="h5" component="h5">
                        <b>Want to Know your Buying power?</b>
                      </Typography>
                    </Item>
                    <Item className="page_bg">
                      <Typography variant="subtitle1" component="subtitle1">
                        <span style={{ fontWeight: "600" }}>
                          Locate the best vehicles matching your budget
                        </span>
                      </Typography>
                    </Item>
                  </Grid>
                  <Grid item md={12}>
                    <Item className="page_bg">
                      <CustomAutocomplete
                        options={carLookingOptions}
                        sx={{
                          width: "98%",
                          background: "#ffffff",
                        }}
                        value={selectedCarLookingOption}
                        onChange={handleCarLookingChange}
                        getOptionLabel={(option) => option.label}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Looking For"
                            InputProps={{
                              ...params.InputProps,
                              readOnly: true, // Make the input field read-only
                              onFocus: (e) => e.target.blur(), // Prevent input from gaining focus
                            }}
                            sx={{
                              cursor: "pointer", // Apply pointer cursor style to the entire TextField
                            }}
                            InputLabelProps={{
                              sx: {
                                fontWeight: "",
                                color: "#4891FF",
                              },
                            }}
                          />
                        )}
                        disableClearable
                        openOnFocus
                      />
                    </Item>
                  </Grid>
                  <Grid className="row" md={12}>
                    <Grid item md={6}>
                      <Item className="page_bg">
                        <CustomTextField
                          id="outlined-basic"
                          label="Current Loan Approval Amount"
                          variant="outlined"
                          sx={{
                            width: "100%",
                            background: "#ffffff",
                          }}
                          autoComplete="off"
                          value={memberInfo.preapproved_amount.toLocaleString()}
                          InputProps={{
                            readOnly: true,
                            sx: {
                              fontSize: "16px",
                              fontWeight: "semibold",
                            },
                            startAdornment: (
                              <InputAdornment
                                position="start"
                                sx={{
                                  fontSize: "16px",
                                }}
                              >
                                $
                              </InputAdornment>
                            ),
                          }}
                        />
                      </Item>
                    </Grid>
                    <Grid item md={6}>
                      <Item className="page_bg">
                        <CustomTextField
                          id="outlined-basic"
                          label="Down Payment"
                          variant="outlined"
                          sx={{
                            width: "100%",
                            background: "#ffffff",
                          }}
                          value={downPayment}
                          onChange={handleDownPaymentChange}
                          onBlur={handleDownPaymentBlur}
                          onClick={handleDownPaymentClick}
                          inputRef={inputRef}
                          autoComplete="off"
                          InputProps={{
                            sx: {
                              fontSize: "16px",
                              fontWeight: "semibold",
                            },
                            startAdornment: (
                              <InputAdornment
                                position="start"
                                sx={{
                                  fontSize: "16px",
                                }}
                              >
                                $
                              </InputAdornment>
                            ),
                          }}
                        />
                        {downPaymentErrorMessage && (
                          <div
                            style={{
                              color: "red",
                              fontSize: "14px",
                              marginTop: "8px",
                            }}
                          >
                            {downPaymentErrorMessage}
                          </div>
                        )}
                      </Item>
                    </Grid>
                  </Grid>
                  <Grid item md={12} mt={2} style={{ display: "none" }}>
                    <Item className="page_bg">
                      <Grid
                        container
                        spacing={2}
                        direction="row"
                        justifyContent="center"
                      >
                        <Grid item>
                          <Item className="page_bg">
                            <Grid
                              container
                              spacing={2}
                              direction="row"
                              alignItems="center"
                              justifyContent="center"
                            >
                              <Grid item>
                                <Typography>
                                  <b>Include Trade?</b>
                                </Typography>
                              </Grid>
                              <Grid item>
                                <Switch
                                  checked={isIncludeTrade}
                                  onChange={handleSwitchChange}
                                />
                              </Grid>
                            </Grid>
                          </Item>
                        </Grid>
                      </Grid>
                    </Item>
                  </Grid>

                  {isIncludeTrade && (
                    <Grid className="row" md={12} style={{ display: "none" }}>
                      <Grid item md={6}>
                        <Item className="page_bg">
                          <CustomTextField
                            id="outlined-basic"
                            label="What is Worth"
                            variant="outlined"
                            sx={{
                              width: "100%",
                              background: "#ffffff",
                            }}
                            value={userWorth}
                            onChange={handleUserWorthChange}
                            onBlur={handleUserWorthBlur}
                            autoComplete="off"
                            InputProps={{
                              sx: {
                                fontSize: "16px",
                                fontWeight: "semibold",
                              },
                              startAdornment: (
                                <InputAdornment
                                  position="start"
                                  sx={{
                                    fontSize: "16px",
                                  }}
                                >
                                  $
                                </InputAdornment>
                              ),
                            }}
                          />
                          {worthErrorMessage && (
                            <div
                              style={{
                                color: "red",
                                fontSize: "14px",
                                marginTop: "8px",
                              }}
                            >
                              {worthErrorMessage}
                            </div>
                          )}
                        </Item>
                      </Grid>
                      <Grid item md={6}>
                        <Item className="page_bg">
                          <CustomTextField
                            id="outlined-basic"
                            label="What I Owe"
                            variant="outlined"
                            sx={{
                              width: "100%",
                              background: "#ffffff",
                            }}
                            autoComplete="off"
                            value={userOwe}
                            onChange={handleUserOweChange}
                            onBlur={handleUserOweBlur}
                            InputProps={{
                              sx: {
                                fontSize: "16px",
                                fontWeight: "semibold",
                              },
                              startAdornment: (
                                <InputAdornment
                                  position="start"
                                  sx={{
                                    fontSize: "16px",
                                  }}
                                >
                                  $
                                </InputAdornment>
                              ),
                            }}
                          />
                          {errorMessage && (
                            <div
                              style={{
                                color: "red",
                                fontSize: "14px",
                                marginTop: "8px",
                              }}
                            >
                              {errorMessage}
                            </div>
                          )}
                        </Item>
                      </Grid>
                    </Grid>
                  )}
                  <Item className="page_bg">
                    <Typography>
                      Your Calculated Estimated Purchase Power is{" "}
                      {isPurchasePowerLoading ? (
                        <CircularProgress size={20} /> // Show Spinner
                      ) : (
                        <b>{formatAmount(totalPurchasePower)}</b> // Show Result
                      )}
                    </Typography>
                    <Typography>
                      Estimated Purchase Price with 12% Tax and Fees
                    </Typography>
                  </Item>
                  <Grid className="row" md={12}>
                    <Item className="page_bg">
                      <StyledButton4K
                        variant="contained"
                        isBigScreen={isBigScreen}
                        onClick={handleSearchButton}
                        disabled={isSearchDisabled}
                        startIcon={isSearchButtonLoader ? null : <SearchIcon fontSize="x-large" />}
                        >
                          {isSearchButtonLoader ? (
                            <CircularProgress size={24} color="inherit" />
                          ) : (
                            "Search"
                          )}
                      </StyledButton4K>
                    </Item>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          ) : (
            <Grid container spacing={1}>
              <Grid item md={12}>
                <Item className="page_bg">
                  <img
                    src={memberInfo.cuLogo}
                    style={{ maxWidth: "20%", maxHeight: "20%" }}
                  />
                </Item>
              </Grid>
              {/* Card */}
              <Grid item md={6} mt={12}>
                <Item className="page_bg">
                  <div className="card-main page_bg">
                    <div className="card-inner">
                      <div className="front">
                        <img
                          src={ImageBaseURL + "card_image.png"}
                          className="map-img"
                          alt="Card"
                        />
                        <div
                          className="card-row card_font"
                          style={{ marginTop: "32%", marginBottom: "2%" }}
                        >
                          <div className="loan-ids-grid">
                            <span>{memberInfo.loan_id[0]}</span>
                            <span>{memberInfo.loan_id[1]}</span>
                            <span>{memberInfo.loan_id[2]}</span>
                            <span>{memberInfo.loan_id[3]}</span>
                          </div>
                        </div>
                        <div
                          className="row text-start card-holder fw-400"
                          style={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                          }}
                        >
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              flexGrow: 1,
                            }}
                          >
                            <span>
                              {memberInfo.member_first_name +
                                " " +
                                memberInfo.member_last_name}
                            </span>
                          </div>
                          <div
                            style={{
                              display: "flex",
                              flexDirection: "column",
                              textAlign: "center",
                              marginTop: "-17px",
                              marginLeft: "5%",
                            }}
                          >
                            <span>VALID THRU:</span>
                            <span>{memberInfo.preapproved_expire}</span>
                          </div>
                          <div
                            style={{
                              display: "flex",
                              flexDirection: "column",
                              textAlign: "left",
                            }}
                          >
                            <span>
                              AMOUNT:&nbsp;$ {memberInfo.preapproved_amount}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Item>
              </Grid>
              {/* Input field */}
              <Grid item md={6} mt={5}>
                <Grid item md={12}>
                  <Item className="page_bg">
                    <Typography variant="h5" component="h5">
                      <b>Want to Know your Buying power?</b>
                    </Typography>
                  </Item>
                  <Item className="page_bg">
                    <Typography variant="subtitle1" component="subtitle1">
                      <span style={{ fontWeight: "600" }}>
                        Locate the best vehicles matching your budget
                      </span>
                    </Typography>
                  </Item>
                </Grid>
                <Grid item md={12}>
                  <Item className="page_bg">
                    <CustomAutocomplete
                      options={carLookingOptions}
                      sx={{
                        width: "97%",
                        background: "#ffffff",
                      }}
                      value={selectedCarLookingOption}
                      onChange={handleCarLookingChange}
                      getOptionLabel={(option) => option.label}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Looking For"
                          InputProps={{
                            ...params.InputProps,
                            readOnly: true, // Make the input field read-only
                            onFocus: (e) => e.target.blur(), // Prevent input from gaining focus
                          }}
                          sx={{
                            cursor: "pointer", // Apply pointer cursor style to the entire TextField
                          }}
                          InputLabelProps={{
                            sx: {
                              fontWeight: "",
                              color: "#4891FF",
                            },
                          }}
                        />
                      )}
                      disableClearable
                      openOnFocus
                    />
                  </Item>
                </Grid>
                <Grid className="row" md={12}>
                  <Grid item md={6}>
                    <Item className="page_bg">
                      <CustomTextField
                        id="outlined-basic"
                        label="Current Loan Approval Amount"
                        variant="outlined"
                        value={memberInfo.preapproved_amount.toLocaleString()}
                        sx={{
                          width: "100%",
                          background: "#ffffff",
                        }}
                        autoComplete="off"
                        InputProps={{
                          readOnly: true,
                          sx: {
                            fontSize: "16px",
                            fontWeight: "semibold",
                          },
                          startAdornment: (
                            <InputAdornment
                              position="start"
                              sx={{
                                fontSize: "16px",
                              }}
                            >
                              $
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Item>
                  </Grid>
                  <Grid item md={6}>
                    <Item className="page_bg">
                      <CustomTextField
                        id="outlined-basic"
                        label="Down Payment"
                        variant="outlined"
                        sx={{
                          width: "100%",
                          background: "#ffffff",
                        }}
                        autoComplete="off"
                        value={downPayment}
                        onChange={handleDownPaymentChange}
                        onBlur={handleDownPaymentBlur}
                        onClick={handleDownPaymentClick}
                        inputRef={inputRef}
                        InputProps={{
                          sx: {
                            fontSize: "16px",
                          },
                          startAdornment: (
                            <InputAdornment
                              position="start"
                              sx={{
                                fontSize: "16px",
                              }}
                            >
                              $
                            </InputAdornment>
                          ),
                        }}
                      />
                      {downPaymentErrorMessage && (
                        <div
                          style={{
                            color: "red",
                            fontSize: "14px",
                            marginTop: "8px",
                          }}
                        >
                          {downPaymentErrorMessage}
                        </div>
                      )}
                    </Item>
                  </Grid>
                </Grid>
                <Grid item md={12} mt={2} style={{ display: "none" }}>
                  <Item className="page_bg">
                    <Grid
                      container
                      spacing={2}
                      direction="row"
                      justifyContent="center"
                    >
                      <Grid item>
                        <Item className="page_bg">
                          <Grid
                            container
                            spacing={2}
                            direction="row"
                            alignItems="center"
                            justifyContent="center"
                          >
                            <Grid item>
                              <Typography>
                                <b>Include Trade?</b>
                              </Typography>
                            </Grid>
                            <Grid item>
                              <Switch
                                checked={isIncludeTrade}
                                onChange={handleSwitchChange}
                              />
                            </Grid>
                          </Grid>
                        </Item>
                      </Grid>
                    </Grid>
                  </Item>
                </Grid>
                {isIncludeTrade && (
                  <Grid className="row" md={12}>
                    <Grid item md={6}>
                      <Item className="page_bg">
                        <CustomTextField
                          id="outlined-basic"
                          label="What is Worth"
                          variant="outlined"
                          value={userWorth}
                          onChange={handleUserWorthChange}
                          onBlur={handleUserWorthBlur}
                          sx={{
                            width: "100%",
                            background: "#ffffff",
                          }}
                          autoComplete="off"
                          InputProps={{
                            sx: {
                              fontSize: "16px",
                            },
                            startAdornment: (
                              <InputAdornment
                                position="start"
                                sx={{
                                  fontSize: "16px",
                                }}
                              >
                                $
                              </InputAdornment>
                            ),
                          }}
                        />
                        {worthErrorMessage && (
                          <div
                            style={{
                              color: "red",
                              fontSize: "14px",
                              marginTop: "8px",
                            }}
                          >
                            {worthErrorMessage}
                          </div>
                        )}
                      </Item>
                    </Grid>
                    <Grid item md={6}>
                      <Item className="page_bg">
                        <CustomTextField
                          id="outlined-basic"
                          label="What I Owe"
                          variant="outlined"
                          value={userOwe}
                          onChange={handleUserOweChange}
                          onBlur={handleUserOweBlur}
                          sx={{
                            width: "100%",
                            background: "#ffffff",
                          }}
                          autoComplete="off"
                          InputProps={{
                            sx: {
                              fontSize: "16px",
                            },
                            startAdornment: (
                              <InputAdornment
                                position="start"
                                sx={{
                                  fontSize: "16px",
                                }}
                              >
                                $
                              </InputAdornment>
                            ),
                          }}
                        />
                        {errorMessage && (
                          <div
                            style={{
                              color: "red",
                              fontSize: "14px",
                              marginTop: "8px",
                            }}
                          >
                            {errorMessage}
                          </div>
                        )}
                      </Item>
                    </Grid>
                  </Grid>
                )}
                <Item className="page_bg">
                  <Typography>
                    Your Calculated Estimated Purchase Power is{" "}
                    {isPurchasePowerLoading ? (
                        <CircularProgress size={20} /> // Show Spinner
                      ) : (
                        <b>{formatAmount(totalPurchasePower)}</b> // Show Result
                      )}
                  </Typography>
                  <Typography>
                    Estimated Purchase Price with 12% Tax and Fees
                  </Typography>
                </Item>
                <Grid className="row" md={12}>
                  <Item className="page_bg">
                  <StyledButtonSearch
                    variant="contained"
                    onClick={handleSearchButton}
                    isBigScreen={isBigScreen}
                    disabled={isSearchDisabled || isSearchButtonLoader} // Disable button when loading
                    startIcon={isSearchButtonLoader ? null : <SearchIcon fontSize="x-large" />} // Remove icon when loading
                  >
                    {isSearchButtonLoader ? (
                      <CircularProgress size={24} color="inherit" />
                    ) : (
                      "Search"
                    )}
                  </StyledButtonSearch>
                  </Item>
                </Grid>
              </Grid>
            </Grid>
          )}
        </Box>
      )}
    </MaserLayout>
  );
};

export default KnowYouPowerWeb;
