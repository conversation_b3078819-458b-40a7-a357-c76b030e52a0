import { useEffect, useState, useMemo } from "react";
import { useDispatch } from "react-redux";
import {
  fetchMsrOrAdvisorMsgResponse,
  sendMsgToMsrOrAdvisor,
  getloggedInUserDetails,
} from "../../../store/apps/user";
import { toast } from "react-toastify";
import { useNotification } from "../../../context/NotificationContext"; // Import the custom hook

const ChatApi = () => {
  const dispatch = useDispatch();
  const [listItems, setListItems] = useState([]);
  const [searchText, setSearchText] = useState("");
  const [loading, setLoading] = useState(true);
  const [showChatComponent, setShowChatComponent] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [messages, setMessages] = useState([]);
  const [userDetails, setUserDetails] = useState(null);
  const [userRequestMsg, setUserRequestMsg] = useState("");
  const [userMsgSendLoader, setUserMsgSendLoader] = useState(false);

  const { unreadMessages } = useNotification(); // Get unread messages from context

  const token = useMemo(() => localStorage.getItem("token"), []);

  // Fetch User Details - Only once
  useEffect(() => {
    const fetchUserDetails = async () => {
      if (!token) return;

      try {
        const { payload: { data: userDetails = [] } = {} } = await dispatch(
          getloggedInUserDetails(token)
        );

        if (userDetails.length) {
          setUserDetails({
            loan_id: userDetails[0].loan_id,
            user_first_name: userDetails[0].member_first_name,
            user_last_name: userDetails[0].member_last_name,
          });
        }
      } catch (error) {
        console.error("Error fetching user details:", error);
      }
    };

    fetchUserDetails();
  }, [dispatch, token]);

  // Fetch Messages from API
  const fetchMsrResponseFromSf = async () => {
    if (!userDetails || !token) return;

    try {
      if(unreadMessages.length > 0){
        setLoading(false);
      }else{
        setLoading(true);
      }

      const response = await dispatch(
        fetchMsrOrAdvisorMsgResponse({
          token,
          loan_id: userDetails.loan_id,
          req_type: "askMSRConversation",
        })
      );

      const rawData = response?.payload?.salesForceResponse?.[0]?.success?.data;

      const defaultMessage = {
        id: 1,
        text: "Hello, how can I help you today?",
        fromDealer: true,
      };

      if (!rawData || (Array.isArray(rawData) && rawData.length === 0)) {
        setMessages([defaultMessage]);
        return;
      }

      if (Array.isArray(rawData)) {
        const formattedMessages = rawData
          .slice()
          .sort((a, b) => new Date(a.CreatedDate) - new Date(b.CreatedDate))
          .map((msg, index) => ({
            id: index + 2,
            text: msg.EmailMessage,
            fromDealer: msg.Sender === "MSR",
            timestamp: new Date(msg.CreatedDate).toLocaleString(),
          }));

        setMessages([defaultMessage, ...formattedMessages]);
      }
    } catch (error) {
      console.error("Error while fetching dealer response:", error);
      toast.error("An error occurred while fetching messages.");
    } finally {
      setLoading(false);
    }
  };

  // Initial message fetch when userDetails is available
  useEffect(() => {
    if (userDetails) fetchMsrResponseFromSf();
  }, [userDetails]);

  // Re-fetch messages when unreadMessages updates (new message arrives)
  useEffect(() => {
    if (unreadMessages.length > 0) {
      fetchMsrResponseFromSf();
    }
  }, [unreadMessages]);

  // Send User Message
  const sendAskDealerMessage = async (userMsg) => {
    if (!userMsg) return;

    setUserMsgSendLoader(true);

    try {
      const response = await dispatch(
        sendMsgToMsrOrAdvisor({
          userMsg,
          loan_id: userDetails.loan_id,
          req_type: "askMSR",
          token,
        })
      );

      const msrResponse = response?.payload?.SalesForceResponse?.[0];
      const success = msrResponse?.success;
      const errorMsg = msrResponse?.error?.Message || "Failed to send message.";

      if (success.message) {
        const tempMessage = {
          id: messages.length + 1,
          text: userMsg,
          fromDealer: false,
          timestamp: new Date().toLocaleString(),
        };

        setMessages((prevMessages) => [...prevMessages, tempMessage]);
        toast.success("Message sent successfully!");
      } else {
        toast.error(errorMsg);
      }
    } catch (error) {
      console.error("Error while sending message:", error);
      toast.error("An unexpected error occurred. Please try again.");
    } finally {
      setUserMsgSendLoader(false);
      setUserRequestMsg("");
    }
  };

  return {
    listItems,
    searchText,
    setSearchText,
    loading,
    showChatComponent,
    setShowChatComponent,
    setListItems,
    selectedItem,
    setSelectedItem,
    messages,
    sendAskDealerMessage,
    userRequestMsg,
    userMsgSendLoader,
    setUserRequestMsg,
    userDetails,
  };
};

export default ChatApi;
