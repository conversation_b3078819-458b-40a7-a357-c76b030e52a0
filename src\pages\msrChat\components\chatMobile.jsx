import {
  Box,
  Paper,
  Typography,
  TextField,
  CircularProgress,
  Stack,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import React, { useEffect, useRef } from "react";
import SendIcon from "@mui/icons-material/Send";
import MasterLayout from "../../../components/layouts/masterLayout";
import ChatApi from "../api/chatApi";
import loaderGif from "../../../assets/gifs/loader.gif";
import { Avatar } from "@mui/material"; // Import Avatar
import { InputAdornment, IconButton } from "@mui/material"; // Import InputAdornment and IconButton

const ChatMessage = styled(Paper)(({ theme, alignRight }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  maxWidth: "70%",
  marginBottom: theme.spacing(2),
  backgroundColor: alignRight ? "#4891FF" : "#61C454",
  color: "#fff",
  textAlign: "left",
  alignSelf: alignRight ? "flex-end" : "flex-start",
  borderRadius: alignRight ? "10px 0px 10px 10px" : "0px 10px 10px 10px",
  wordBreak: "break-word",
}));

const Timestamp = styled(Typography)(({ theme }) => ({
  fontSize: "0.75rem",
  color: "#fff",
  marginTop: theme.spacing(0.5),
}));

const CustomTextField = styled(TextField)({
  "& .MuiInputLabel-root": { color: "#4891FF" },
  "& .MuiOutlinedInput-root": {
    "& fieldset": { borderColor: "#4891FF", borderWidth: "2px" },
    "&:hover fieldset": { borderColor: "#4891FF" },
    "&.Mui-focused fieldset": { borderColor: "#4891FF" },
  },
});

const ChatComponentMobile = () => {
  const {
    messages,
    sendAskDealerMessage,
    userRequestMsg,
    userMsgSendLoader,
    setUserRequestMsg,
    loading,
    userDetails,
  } = ChatApi();

  const messageEndRef = useRef(null);

  const scrollToBottom = () => {
    messageEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  if (loading) {
    return (
      <Stack
        alignItems="center"
        justifyContent="center"
        sx={{ height: "100vh" }}
      >
        <img
          className="page_bg"
          src={loaderGif}
          alt="Loading..."
          style={{ width: "200px", height: "200px" }}
        />
        <span style={{ fontWeight: "600" }}>Loading Your Conversations...</span>
      </Stack>
    );
  }

  return (
    <MasterLayout>
      <Box
        sx={{
          padding: { xs: 1, sm: 2 },
          mb: 4,
          mt: 2,
          position: "relative",
          display: "flex",
          flexDirection: "column",
        }}
      >
        {/* Header Section */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            borderBottom: "1px solid #4981ff",
            padding: 1,
            backgroundColor: "#ffffff",
            position: "sticky",
            top: 55,
            left: 0,
            right: 0,
            zIndex: 1,
          }}
        >
          <Typography
            className="mt-2"
            variant="body1"
            gutterBottom
            sx={{
              fontWeight: "bold",
              flexGrow: 1,
              textAlign: "center",
            }}
          >
            Chat with Credit Union
          </Typography>
        </Box>

        {/* Main Content (Chat) */}
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            padding: { xs: 1, sm: 2 },
            position: "relative",
            flexGrow: 1,
            marginTop: "50px",
            marginBottom: "85px",
            overflowY: "auto",
          }}
        >
          {messages.map((msg) => (
            <Box
              key={msg.id}
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: msg.fromDealer ? "flex-start" : "flex-end",
                mb: 2,
              }}
            >
              {/* Avatar */}
              <Avatar
                sx={{
                  bgcolor: msg.fromDealer ? "#61C454" : "#4891FF",
                  width: 32,
                  height: 32,
                  fontSize: "14px",
                  mb: 0.5,
                }}
              >
                {msg.fromDealer
                  ? "CU"
                  : `${
                      userDetails.user_first_name
                        ? userDetails.user_first_name.charAt(0).toUpperCase()
                        : "U"
                    }${
                      userDetails.user_last_name
                        ? userDetails.user_last_name.charAt(0).toUpperCase()
                        : ""
                    }`}
              </Avatar>

              {/* Chat Message */}
              <ChatMessage alignRight={!msg.fromDealer}>
                {msg.text.split("\n").map((line, index) => (
                  <React.Fragment key={index}>
                    {line}
                    <br />
                  </React.Fragment>
                ))}
                <Timestamp alignRight={!msg.fromDealer}>
                  {msg.timestamp}
                </Timestamp>
              </ChatMessage>
            </Box>
          ))}
          <div ref={messageEndRef} />
        </Box>

        {/* Footer Section */}
        {/* Footer Section */}
        <Box
          sx={{
            position: "fixed",
            bottom: 80,
            left: 0,
            right: 0,
            backgroundColor: "#ffffff",
            borderTop: "1px solid #4981ff",
            padding: 2,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            zIndex: 1,
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              width: "100%",
            }}
          >
            <Box sx={{ flexGrow: 1, marginX: 2 }}>
              <CustomTextField
                id="outlined-multiline-flexible"
                label="Type Your Query Here..."
                variant="outlined"
                multiline
                fullWidth
                autoComplete="off"
                value={userRequestMsg}
                onChange={(e) => setUserRequestMsg(e.target.value)}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {userMsgSendLoader ? (
                        <CircularProgress size={30} sx={{ color: "#4891FF" }} />
                      ) : (
                        <IconButton
                          onClick={() => sendAskDealerMessage(userRequestMsg)}
                          edge="end"
                        >
                          <SendIcon
                            sx={{ color: "#4891FF", fontSize: "30px" }}
                          />
                        </IconButton>
                      )}
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
          </Box>
        </Box>
      </Box>
    </MasterLayout>
  );
};

export default ChatComponentMobile;
