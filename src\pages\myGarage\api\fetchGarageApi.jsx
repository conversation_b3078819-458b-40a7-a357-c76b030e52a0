import { useEffect, useRef, useState, useCallback } from "react";
import { useDispatch } from "react-redux";
import {
  getCars,
  deleteCarFromMyGarage,
  getVehicleDetails,
  fetchDealerResponse,
  sendDealerTextRequest,
  undoDeletedCarFromMyGarage,
  generateAiNegotiation,
} from "../../../store/apps/car/index";
import { toast } from "react-toastify";
import { getloggedInUserDetails, getUser } from "../../../store/apps/user";
import { Button } from "@mui/material";
import { pageMetaData } from "../../../../data/pageMetaData";
import { pageComponentData } from "../../../../data/componentMetaData";
import { useNavigate, useLocation } from "react-router-dom";
import { memberBasedLoanOfferActionUtil } from "../../../util/memberBasedLoanOfferActionUtil";

export const useGarageListApi = () => {
  const [loading, setLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [undoLoading, setUndoLoading] = useState(false);
  const [negotiationLoading, setNegotiationLoading] = useState(null);
  const [nonNegotiationLoading, setNonNegotiationLoading] = useState(null);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSizeVal] = useState(
    Number(import.meta.env.VITE_GARAGE_LIST_PAGE_SIZE)
  );
  const [carsInfo, setCarsInfo] = useState({
    count: 0,
    carsinfo: [],
  });
  const [carCount, setCarCount] = useState(0);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation(); // Get current URL
  const [allCarsFetched, setAllCarsFetched] = useState(false);
  const [memberName, setMemberName] = useState("");
  const [loanId, setLoanId] = useState("");
  const deletedCarIndexRef = useRef(null); // Reference to the deleted car index
  const [offset, setOffset] = useState(0);
  const [pageId, setPageId] = useState(null); // State to store the page_id
  const [componentMetaData, setComponentMetaData] = useState(null); // State to store the componentMetaData
  const [urlMemberId, setUrlMemberId] = useState(null);
  const [userDataMemberId, setUserDataMemberId] = useState(null);
  const [
    aiNegotiationConfirmationModalOpen,
    setAiNegotiationConfirmationModalOpen,
  ] = useState(false);
  const [openNegotionConfirmation, setOpenNegotionConfirmation] = useState(false);
  const [memberInfo, setMemberInfo] = useState({
    selectedTerm: "",
    preapproved_pct: "",
    calculate_emi: "",
  });
  const [downpaymentValue, setDownpaymentValue] = useState(0);
  // Check box //
  const label = { inputProps: { "aria-label": "Checkbox demo" } };
  // End //
  // Speed Dial Floating button //
  const [open, setOpen] = useState(false);

  const fetchUserDetails = useCallback(
    async (token) => {
      try {
        const response = await dispatch(getloggedInUserDetails(token));
        const { payload: { data: userDetails = [] } = {} } = response;

        if (userDetails.length > 0) {
          // If URL contains a redirection parameter, validate the member_id
          if (urlMemberId && userDetails[0].member_id !== urlMemberId) {
            navigate("/access-denied"); // Redirect if IDs do not match
            return;
          }
          setMemberName(userDetails[0]?.member_first_name ?? "");
          setUserDataMemberId(userDetails[0]?.member_id ?? null);
          setLoanId(userDetails[0]?.loan_id ?? "");
          localStorage.setItem("is_loan_offer_accepted", userDetails[0]?.is_loan_offer_accepted ?? null);
        }
      } catch (error) {
        console.error("Error while fetching user details:", error);
      }
    }, [dispatch, urlMemberId]);

  const fetchInitialCars = useCallback(async (token) => {
    try {
      const response = await dispatch(
        getCars({ page: 1, page_size: pageSizeVal, token, offset: offset })
      );

      setCarsInfo({
        count: response.payload.recordCount,
        carsinfo: response.payload.data,
      });
      setCarCount(response.payload.data.length);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.error("Error while Fetching Cars:", error);
    } finally {
      setLoading(false);
    }
  }, [dispatch, pageSizeVal, offset]);

  // Fetch user data on page load to check member type
  useEffect(() => {
    const fetchUserData = async () => {
      const number = localStorage.getItem("ph_number");
      const token = localStorage.getItem("token");
      if (!number || !token) {
        navigate("/");
        return;
      }

      try {
        dispatch(getUser({ phone_number: number, authToken: token })).then(
          (response) => {
            if (
              response &&
              response.meta &&
              response.meta.requestStatus === "fulfilled"
            ) {
              const userData = response.payload;
              localStorage.setItem("member_type", userData?.member_type ?? null);
            }
          }
        );
      } catch (error) {
        console.error("Error fetching user data:", error);
      }
    };

    fetchUserData();
  }, [dispatch]);

  useEffect(() => {
    // Extract and decode member_id from URL (only if query parameters exist)
    const getQueryParam = (param) => {
      const urlParams = new URLSearchParams(location.search);
      return urlParams.get(param);
    };

    const encodedData = getQueryParam("data");

    if (encodedData) {
      try {
        const decodedData = JSON.parse(atob(decodeURIComponent(encodedData)));
        if (decodedData.member_id) {
          setUrlMemberId(decodedData.member_id); // Store extracted member_id
        } else {
          throw new Error("member_id not found");
        }
      } catch (error) {
        console.error("Invalid encoded data", error);
        navigate("/access-denied"); // Redirect if decoding fails
      }
    }
  }, [location.search, navigate]); // Runs only when URL changes

  // Effect for fetching user details once when the token is available
  useEffect(() => {
    const token = localStorage.getItem("token");
    if (token && !memberName) {
      // Only fetch if memberName is empty
      fetchUserDetails(token);
    }
  }, [fetchUserDetails, memberName]); // Only rerun if memberName is not set

  // Effect for fetching initial cars, if not already fetched
  useEffect(() => {
    const token = localStorage.getItem("token");
    if (token && loading) {
      // Ensure it doesn't fetch repeatedly
      fetchInitialCars(token);
    }
  }, [fetchInitialCars, loading]); // Only rerun if loading is true

  /**
   * Asynchronously loads more cars for the user's garage
   */
  const loadMoreCars = async () => {
    if (loading || loadingMore || allCarsFetched) return;

    setLoadingMore(true);
    const token = localStorage.getItem("token");

    try {
      // Adjust the offset based on deleted vehicles
      let offsetVal = offset;
      // If there are deleted vehicles, adjust the offset
      // Increase the offset by page size
      offsetVal = offsetVal + pageSizeVal;
      const response = await dispatch(
        getCars({
          page: page + 1,
          page_size: pageSizeVal,
          token: token,
          offset: offsetVal,
        })
      );

      const newCars = response.payload.data;
      setOffset(offsetVal);

      // Update car information
      setCarsInfo((prev) => ({
        count: response.payload.recordCount, // Total available cars excluding deletions
        carsinfo: [...prev.carsinfo, ...newCars], // Append new cars to the list
      }));

      setCarCount((prev) => prev + newCars.length); // Increment the count of displayed cars
      setPage((prev) => prev + 1); // Move to next page

      // If less cars are returned than the page size, mark that all cars are fetched
      if (newCars.length < pageSizeVal) {
        setAllCarsFetched(true);
      }
    } catch (error) {
      console.error("Error while Fetching More Cars:", error);
    } finally {
      setLoadingMore(false);
    }
  };

  /**
   * Deletes a car from the user's garage.
   *
   * @param {string} vin - The VIN of the vehicle to delete.
   * @param {string} heading - The heading of the car (e.g. "Toyota Camry").
   */
  const deleteCarFromGarage = async (vin, heading, mc_row_id) => {
    const token = localStorage.getItem("token");
    setDeleteLoading(true);
    const indexToDelete = carsInfo.carsinfo.findIndex((car) => car.vin === vin);

    // Store the index in the ref
    deletedCarIndexRef.current = indexToDelete;

    await dispatch(
      deleteCarFromMyGarage({ vin: vin, token: token, mc_row_id: mc_row_id })
    )
      .then(() => {
        const car = carsInfo.carsinfo.find(car => car.mc_row_id === mc_row_id);
        const deletedCarOutdoorPrice = car.outdoor_price;
        const deletedVehicleNegotiaion = car.is_negotiation;
        // Update local state after successful deletion
        setCarCount((prevCount) => {
          const newCount = prevCount - 1;

          // If there are no more cars to load, reload the page
          if (newCount === 0 && carsInfo.count > 0) {
            window.location.reload();
          }
          return newCount;
        });

        setCarsInfo((prevInfo) => {
          const newCount = prevInfo.count - 1;
          return {
            ...prevInfo,
            count: newCount,
            carsinfo: prevInfo.carsinfo.filter((car) => car.vin !== vin),
          };
        });

        setDeleteLoading(false);
        setOffset((prevOffset) => prevOffset - 1);

        // Show toast with Undo button
        toast.dismiss();
        toast.success(
          <div style={{ display: "flex", alignItems: "center" }}>
            {`${heading || "Vehicle"} Successfully Deleted From My Garage`}
            <br />
            <Button
              onClick={() => undoDelete(vin, mc_row_id, deletedCarOutdoorPrice, deletedVehicleNegotiaion)}
              size="small"
              style={{ color: "#4891ff", fontWeight: "600" }}
            >
              UNDO
            </Button>
          </div>,
          { autoClose: 10000 }
        );
      })
      .catch((error) => {
        console.error("Error while deleting the car:", error);
        setDeleteLoading(false);
      });
  };

  const undoDelete = (vin, mc_row_id, deletedCarOutdoorPrice, deletedVehicleNegotiaion) => {
    toast.dismiss(); // Dismiss any existing toast
    if (!mc_row_id) {
      toast.error("Something went wrong. Please try again later");
      return;
    }
    setUndoLoading(true);
    dispatch(
      undoDeletedCarFromMyGarage({
        vin: vin,
        token: localStorage.getItem("token"),
        mc_row_id: mc_row_id,
        otd_price: deletedCarOutdoorPrice,
        is_negotiation: deletedVehicleNegotiaion,
      })
    )
      .then((response) => {
        const restoredCar = response.payload.data[0];
        // Check if the car was successfully restored
        if (!restoredCar) {
          toast.error("Failed to restore the vehicle. Please try again later.");
          return;
        }

        // Update the car count
        setCarCount((prevCount) => prevCount + 1);

        // Restore the car at the original index
        setCarsInfo((prevInfo) => {
          const newCount = prevInfo.count + 1;

          // Get the original index from the state
          const indexToRestore = deletedCarIndexRef.current;

          // Create a new carsinfo array
          const newCarsInfo = [...prevInfo.carsinfo];

          // Insert the restored car at the original index
          newCarsInfo.splice(indexToRestore, 0, restoredCar);

          return {
            ...prevInfo,
            count: newCount,
            carsinfo: newCarsInfo,
          };
        });
        setOffset((prevOffset) => prevOffset + 1);
        setUndoLoading(false);
        toast.success("Vehicle Successfully Restored");
        fetchInitialCars(localStorage.getItem("token"));
      })
      .catch((error) => {
        setUndoLoading(false);
        console.error("Error while restoring the car:", error);
        toast.error("Error while restoring the car. Please try again.");
      });
  };

  /**
   * Fetches the vehicle details for a given VIN.
   *
   * @param {string} vin - The VIN of the vehicle.
   * @return {Promise<Object|null>} The vehicle details, or null if an error occurred.
   */
  const fetchVehicleDetails = async (vin) => {
    const token = localStorage.getItem("token");
    try {
      // Dispatch the async action to fetch the vehicle details
      const response = await dispatch(getVehicleDetails({ vin, token }));
      // Return the payload of the response
      return response.payload;
    } catch (error) {
      // Log the error and return null
      console.error("Error while fetching vehicle details:", error);
      return null;
    }
  };

  const fetchDealerResponseData = async (car) => {
    const token = localStorage.getItem("token");
    try {
      // Dispatch the async action to fetch the vehicle details
      const response = await dispatch(fetchDealerResponse({ car, token }));
      // Return the payload of the response
      return response.payload.data;
    } catch (error) {
      // Log the error and return null
      console.error("Error while fetching dealer response:", error);
      return null;
    }
  };

  const sendDealerRequestMsg = async (
    carDetails,
    userMsg,
    value,
    testDriveMessage
  ) => {
    const token = localStorage.getItem("token");
    try {
      // Dispatch the async action to fetch the vehicle details
      const response = await dispatch(
        sendDealerTextRequest({
          carDetails,
          userMsg,
          value,
          testDriveMessage,
          token,
        })
      );
      // Return the payload of the response
      return response.payload;
    } catch (error) {
      // Log the error and return null
      console.error("Error while fetching dealer response:", error);
      return null;
    }
  };

  const formatTimestamp = (timestamp, timeZone = "") => {
    if (!timestamp) return "";

    return new Date(timestamp).toLocaleString("en-US", {
      timeZone: timeZone || Intl.DateTimeFormat().resolvedOptions().timeZone,
      year: "numeric",
      month: "short",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
      timeZoneName: "short", // Display timezone abbreviation (e.g., CST)
    });
  };

  useEffect(() => {
    // Get the page_id from pageMetaData based on slug
    const slug = "my-garage"; // Replace this with the desired slug
    const pageData = pageMetaData.find((page) => page.slug === slug);
    if (pageData) {
      setPageId(pageData.id);
    }

    // Get Page Componenet Meta Data
    const matchingComponents = pageComponentData.filter(
      (pageComponent) => pageComponent.page_id === pageId
    );

    if (matchingComponents.length > 0) {
      setComponentMetaData(matchingComponents);
    }
  }, [pageId]);

  const [checkedState, setCheckedState] = useState({});
  const [selectedCars, setSelectedCars] = useState([]); // Store selected car objects
  const updatedSelectedCarsRef = useRef([]); // Store updated selected car objects
  const [carToUncheck, setCarToUncheck] = useState({});
  const uncheckedCarsRef = useRef([]);
  const [uncheckConfirmationModalOpen, setUncheckConfirmationModalOpen] =
    useState(false);
  const [monthlyPaymentErrorMessage, setMonthlyPaymentErrorMessage] = useState('');
  const [downPaymentErrorMessage, setDownPaymentErrorMessage] = useState('');
  const handleConfirmCheckout = () => {
    if (carToUncheck) {
      setSelectedCars((prevSelected) =>
        prevSelected.filter(
          (selectedCar) => selectedCar.mc_row_id !== carToUncheck.mc_row_id
        )
      );

      // Update the uncheckedCarsRef.current instead of setState
      uncheckedCarsRef.current = [
        ...uncheckedCarsRef.current,
        {
          fp_dealer_id: carToUncheck.fp_dealer_id,
          vin: carToUncheck.vin,
          heading: carToUncheck.heading,
          mc_row_id: carToUncheck.mc_row_id,
          loan_id: loanId,
          req_type: "topFiveList",
          process_type: "remove",
        },
      ];

      setCheckedState((prevState) => ({
        ...prevState,
        [carToUncheck.mc_row_id]: false,
      }));

      setCarToUncheck(null);
    }
    removeAiNegotiationDetails(uncheckedCarsRef.current);
    setUncheckConfirmationModalOpen(false);
  };

  const handleCheckbox = (event, car) => {
    const isChecked = event.target.checked;

    // First, calculate the current number of checked items (before state update)
    const checkedCount = Object.values(checkedState).filter(value => value).length;

    if (!isChecked) {
      setCarToUncheck(car);

      if (car.is_negotiation !== 0) {
        setUncheckConfirmationModalOpen(true);
        return; // Stop further execution until confirmation is received
      }

      // Remove the car from selectedCars list only if no confirmation is needed
      setSelectedCars((prevSelected) =>
        prevSelected.filter((selectedCar) => selectedCar.mc_row_id !== car.mc_row_id)
      );
    }

    // Check if the user can select more cars or not
    if (isChecked && Number(checkedCount + nonZeroNegotiations) > 5) {
      toast.error("You can AI Negotiate up to 5 vehicles. Please uncheck atleast one vehicle to continue AI Negotiation.");
      return; // Prevent update
    }

    if (isChecked && checkedCount >= 5) {
      toast.error("You can only select up to 5 vehicles.");
      return; // Prevent update
    }

    // Update checked state
    setCheckedState((prevState) => {
      return {
        ...prevState,
        [car.mc_row_id]: isChecked,
      };
    });

    // Update selectedCars only if it's safe to add the car
    if (isChecked) {
      setSelectedCars((prevSelected = []) => [
        ...prevSelected,
        {
          fp_dealer_id: car.fp_dealer_id,
          vin: car.vin,
          heading: car.heading,
          mc_row_id: car.mc_row_id,
          loan_id: loanId,
          req_type: "topFiveList",
          process_type: "add",
        },
      ]);
    }
  };
  const [nonZeroNegotiations, setNonZeroNegotiations] = useState(0);
  useEffect(() => {
    // Count how many is_negotiation values are not 0
    setNonZeroNegotiations(carsInfo.carsinfo.filter(car => car.is_negotiation !== 0).length);
  }, [carsInfo.carsinfo]);
  const negotiationChecking = () => {
    // Show error if more than 5
    if (nonZeroNegotiations > 5) {
      toast.error("You can AI Negotiate up to 5 vehicles. Please uncheck atleast one vehicle to continue AI Negotiation.");
      return;
    }
  };
  // Function to handle AI Negotiation button click
  const handleAINegotiation = (
    status = null,
    selectedVehicleData = selectedCars
  ) => {
    switch (true) {
      case !selectedVehicleData.length && nonZeroNegotiations === 0:
        toast.error("Please select at least one vehicle.");
        return;

      case !selectedVehicleData.length && nonZeroNegotiations < 5:
        toast.error("Please select at least one vehicle");
        return;

      case !selectedVehicleData.length && nonZeroNegotiations >= 5:
        toast.error(
          "You can AI Negotiate up to 5 vehicles. Please uncheck at least one vehicle to continue AI Negotiation."
        );
        setAiNegotiationConfirmationModalOpen(false);
        return;

      case selectedVehicleData.length > 5:
        toast.error("You can select only up to 5 vehicles.");
        return;

      case selectedVehicleData.length + nonZeroNegotiations > 5:
        toast.error(
          "You can AI Negotiate up to 5 vehicles. Please uncheck at least one vehicle to continue AI Negotiation."
        );
        return;

      case selectedVehicleData.length < 5 &&
        selectedVehicleData.length + nonZeroNegotiations < 5 &&
        status !== "yes":
        setAiNegotiationConfirmationModalOpen(true);
        return;

      default:
        submitAiNegotiationDetails(selectedVehicleData);
    }
  };

  /**
   * Handles individual AI negotiation button click for a particular car.
   * Submits the AI negotiation details for the selected car.
   * @param {Object} car - Car object containing the vehicle details.
   * @param {Object} checkedState - The checked state of the car.
   */
  const [checkedRow, setCheckedRow] = useState(false);
  const individualAINegotiation = (car, checkedState) => {
    const token = localStorage.getItem("token");
    const memberType = localStorage.getItem("member_type");
    // if (memberType === '2') {
    //   let forceUnderProcess = false;
    //   try {
    //     const isAcceptedRaw = localStorage.getItem('is_loan_offer_accepted');
    //     // Accepts both boolean true and string 'true'
    //     if (isAcceptedRaw === 'true' || isAcceptedRaw === true) {
    //       forceUnderProcess = true;
    //     }
    //   } catch (e) {
    //     console.log('Error while calling util:', e);
    //   }
    //   memberBasedLoanOfferActionUtil(dispatch, token, forceUnderProcess);
    //   return;
    // }
    negotiationChecking();
    if (selectedCars.length > 5) {
      toast.error("You can select only up to 5 vehicles.");
      return;
    }

    if (!selectedCars.length && nonZeroNegotiations >= 5) {
      toast.error("You can AI Negotiate up to 5 vehicles. Please uncheck atleast one vehicle to continue AI Negotiation.");
      return;
    }

    if (selectedCars.length > 0 && Number(selectedCars.length && selectedCars.length + nonZeroNegotiations > 5)) {
      toast.error("You can AI Negotiate up to 5 vehicles. Please uncheck atleast one vehicle to continue AI Negotiation.");
      return;
    }
    // Check if the particular car's mc_row_id exists in checkedState and its value
    const isCheckedIndividual = checkedState[car.mc_row_id] || false;
    if (isCheckedIndividual) {
      // If the car is already checked, submit the AI negotiation details
      submitAiNegotiationDetails(selectedCars);
    } else {
      // If the car is not checked, update the selectedCars without checking isChecked
      const updatedSelected = [
        {
          fp_dealer_id: car.fp_dealer_id,
          vin: car.vin,
          heading: car.heading,
          mc_row_id: car.mc_row_id,
          loan_id: loanId,
          req_type: "topFiveList",
          process_type: "add",
        },
      ];
      updatedSelectedCarsRef.current = updatedSelected;

      submitAiNegotiationDetails(updatedSelectedCarsRef.current);
    }
    updatedSelectedCarsRef.current = [];  // Reset the updatedSelectedCarsRef
  };

  const confirmAINegotiation = () => {
    submitAiNegotiationDetails(checkedRow ? selectedCars : updatedSelectedCarsRef.current);
    updatedSelectedCarsRef.current = [];  // Reset the updatedSelectedCarsRef
    selectedCars([]);
    setOpenNegotionConfirmation(false);
  };

  /**
   * Submits AI negotiation details for the selected vehicles.
   * @param {Array} vehicles - List of selected vehicle objects.
   */
  const submitAiNegotiationDetails = (vehicles) => {
    vehicles.forEach(vehicle => setNegotiationLoading(vehicle.mc_row_id));
    // Dispatch the generateAiNegotiation action with vehicle data and token
    dispatch(
      generateAiNegotiation({
        vehicles_data: vehicles,
        token: localStorage.getItem("token"),
      })
    )
      .then((response) => {
        // Destructure success and error responses from the payload
        const { success, error } = response.payload.SalesForceResponse || {};

        /**
         * Retrieves the vehicle heading based on the fastpass ID.
         * @param {string} fastpassId - Fastpass vehicle identifier.
         * @returns {string} - Vehicle heading or a default message.
         */
        const getVehicleHeading = (fastpassId) => {
          const matchedVehicle = vehicles.find(
            (v) => String(v.mc_row_id) === String(fastpassId)
          );
          return matchedVehicle
            ? matchedVehicle.heading
            : `FastpassVehicleId ${fastpassId}`;
        };

        // Map success and error responses to vehicle headings
        const successHeadings = success
          ?.map((item) => getVehicleHeading(item.FastpassVehicleId))
          .filter(Boolean);

        const errorHeadings = error
          ?.map((item) =>
            getVehicleHeading(item.FastpassVehicleId || "Unknown")
          )
          .filter(Boolean);

        // Display appropriate toast notifications based on the response
        if (successHeadings.length > 0) {
          toast.success(
            `AI Negotiation has been started for ${successHeadings.join(" & ")}`
          );
        }

        if (errorHeadings.length > 0) {
          toast.error(
            `Unable to start AI negotiation for ${errorHeadings.join(" & ")}`
          );
        }

        if (!successHeadings.length && !errorHeadings.length) {
          toast.error("Unexpected response. Please try again later.");
        }
        fetchInitialCars(localStorage.getItem("token"));
      })
      .catch((error) => {
        // Handle any errors during the dispatch process
        console.error("Error while restoring the car:", error);
        toast.error("Unable to start AI negotiation. Please try again.");
        setNegotiationLoading(null);
      })
      .finally(() => {
        // Reset the states
        setCheckedState({});
        setNegotiationLoading(null);
        setOpenNegotionConfirmation(false);
        setSelectedCars([]);
        updatedSelectedCarsRef.current = [];  // Reset the updatedSelectedCarsRef
        uncheckedCarsRef.current = [];  // Reset the uncheckedCarsRef
      });
  };

  const removeAiNegotiationDetails = (vehicles) => {
    vehicles.forEach(vehicle => setNonNegotiationLoading(vehicle.mc_row_id));
    // Dispatch the generateAiNegotiation action with vehicle data and token
    dispatch(
      generateAiNegotiation({
        vehicles_data: vehicles,
        token: localStorage.getItem("token"),
      })
    )
      .then((response) => {
        // Destructure success and error responses from the payload
        const { success, error } = response.payload.SalesForceResponse || {};

        /**
         * Retrieves the vehicle heading based on the fastpass ID.
         * @param {string} fastpassId - Fastpass vehicle identifier.
         * @returns {string} - Vehicle heading or a default message.
         */
        const getVehicleHeading = (fastpassId) => {
          const matchedVehicle = vehicles.find(
            (v) => String(v.mc_row_id) === String(fastpassId)
          );
          return matchedVehicle
            ? matchedVehicle.heading
            : `FastpassVehicleId ${fastpassId}`;
        };

        // Map success and error responses to vehicle headings
        const successHeadings = success
          ?.map((item) => getVehicleHeading(item.FastpassVehicleId))
          .filter(Boolean);

        const errorHeadings = error
          ?.map((item) =>
            getVehicleHeading(item.FastpassVehicleId || "Unknown")
          )
          .filter(Boolean);

        // Display appropriate toast notifications based on the response
        if (successHeadings.length > 0) {
          toast.success(
            `AI Negotiation has been stopped for ${successHeadings.join(" & ")}`
          );
        }

        if (errorHeadings.length > 0) {
          toast.error(
            `Unable to stop AI negotiation for ${errorHeadings.join(" & ")}`
          );
        }

        if (!successHeadings.length && !errorHeadings.length) {
          toast.error("Unexpected response. Please try again later.");
        }
        fetchInitialCars(localStorage.getItem("token"));
      })
      .catch((error) => {
        // Handle any errors during the dispatch process
        console.error("Error while restoring the car:", error);
        toast.error("Unable to stop AI negotiation. Please try again.");
        setNonNegotiationLoading(null);
      })
      .finally(() => {
        // Reset the states
        setCheckedState({});
        setUndoLoading(false);
        setNonNegotiationLoading(null);
        setSelectedCars([]);
        updatedSelectedCarsRef.current = [];  // Reset the updatedSelectedCarsRef
        uncheckedCarsRef.current = [];  // Reset the uncheckedCarsRef
      });
  };

  // Determine environment and URL
  const isDev = import.meta.env.MODE === 'development';
  const dealerPortalUrl = isDev
      ? import.meta.env.VITE_DEALER_PORTAL_DEV_URL
      : import.meta.env.VITE_DEALER_PORTAL_PROD_URL;
  // HTML-formatted message with styled link
  const shareMessage = `The member's loan details have been shared on the Dealer Portal.<br /> Please sign in to <a href="${dealerPortalUrl}" target="_blank" style="color: #1434A4; text-decoration: underline; font-weight: bold;">${dealerPortalUrl}</a> and login using your email.`;

  return {
    carsInfo,
    loading,
    loadingMore,
    loadMoreCars,
    deleteCarFromGarage,
    fetchVehicleDetails,
    carCount,
    deleteLoading,
    allCarsFetched,
    fetchDealerResponseData,
    sendDealerRequestMsg,
    formatTimestamp,
    memberName,
    undoLoading,
    componentMetaData,
    fetchInitialCars,
    userDataMemberId,
    handleAINegotiation,
    label,
    open,
    setOpen,
    aiNegotiationConfirmationModalOpen,
    setAiNegotiationConfirmationModalOpen,
    submitAiNegotiationDetails,
    checkedState,
    handleCheckbox,
    selectedCars,
    uncheckConfirmationModalOpen,
    setUncheckConfirmationModalOpen,
    handleConfirmCheckout,
    individualAINegotiation,
    openNegotionConfirmation,
    setOpenNegotionConfirmation,
    memberInfo,
    setMemberInfo,
    downpaymentValue,
    setDownpaymentValue,
    monthlyPaymentErrorMessage,
    setMonthlyPaymentErrorMessage,
    downPaymentErrorMessage,
    setDownPaymentErrorMessage,
    updatedSelectedCarsRef,
    confirmAINegotiation,
    checkedRow,
    negotiationLoading,
    nonNegotiationLoading,
    nonZeroNegotiations,
    shareMessage,
  };
};

export default useGarageListApi;
