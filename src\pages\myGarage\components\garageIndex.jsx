import { useMediaQuery } from 'react-responsive'
import GarageMobile from './garageMobile';
import GarageWeb from './garageWeb';
import MasterLayout from '../../../components/layouts/masterLayout';

function GarageIndex() {
    const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' })
    return (
        <MasterLayout>
            {isTabletOrMobile ? <GarageMobile /> : <GarageWeb />}
        </MasterLayout>
    );
}

export default GarageIndex;