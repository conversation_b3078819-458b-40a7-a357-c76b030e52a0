import { useState, useEffect, useRef } from "react";
import { styled } from "@mui/material/styles";
import {
  Box,
  Grid,
  Paper,
  Button,
  Typography,
  CircularProgress,
  Stack,
  Fab,
  Tooltip,
  IconButton,
} from "@mui/material";
import {
  addVehicleToGarageRedirection,
  sendShareFastpassDetails
} from "../../../store/apps/car/index";
import MasterLayout from "../../../components/layouts/masterLayout";
import Icon from "../../../icon";
import { ImageBaseURL } from "../../../config";
import useGarageListApi from "../api/fetchGarageApi";
import VehicleDetailsModal from "../modal/vehicleDetails";
import CloseIcon from "@mui/icons-material/Close";
import NoDataFound from "./noDataFoundMobile";
import loaderGif from "../../../assets/gifs/loader.gif";
import AskDealerChatModalMobile from "../modal/askDealerChatModalMobile";
import dayjs from "dayjs";
import { toast } from "react-toastify";
import DeletionConfirmationDialog from "../modal/deletionConfirmationDialog";
import { executeComponentAction } from "../../../util/componentActionUtil";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { callComponentActionApi } from "../../../util/callComponenetActionApi";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import Checkbox from "@mui/material/Checkbox";
import AiNegotiationConfirmationModal from "../modal/aiNegotiationConfirmationModal";
import DealSheetModal from "../modal/dealSheetModal";
import UncheckConfirmationModal from "../modal/uncheckConfirmationModal";
import NegotiationConfirmationModal from "../modal/negotiationConfirmationModal";
import { useGlobalTutorialModal } from "../../../context/TutorialModalContext";
import { hasSeenTutorial, shouldShowTutorial } from "../../../context/TutorialStorage";
// import { memberBasedLoanOfferActionUtil } from "../../../util/memberBasedLoanOfferActionUtil";
import FpShareConfirmationModal from "../modal/fpShareConfirmationModal";

function GarageMobile() {
  const fallbackImg = `${ImageBaseURL}fallback_image.png`;
  const navigate = useNavigate();
  const [modalOpen, setModalOpen] = useState(false);
  const [sheetDetailsModalOpen, setSheetDetailsModalOpen] = useState(0);
  const [vehicleDetails, setVehicleDetails] = useState(null);
  const [selectedCar, setSelectedCar] = useState(null);
  const [loadingDetails, setLoadingDetails] = useState(null); // New state for loading details
  const [dealDetails, setDealDetails] = useState(null); // New state for loading deal details
  const [chatModalOpen, setChatModalOpen] = useState(false);
  const [messages, setMessages] = useState([]);
  const [deletionDialogOpen, setDeletionDialogOpen] = useState(false);
  const [carToDelete, setCarToDelete] = useState(null);
  const [urlMemberId, setUrlMemberId] = useState(null);
  const [urlRowID, setUrlRowID] = useState(null);
  const [loadingRightSwipe, setLoadingRightSwipe] = useState(false); // Define loadingRightSwipe state
  const [askDealerLoading, setaskDealerLoading] = useState(null);
  const [askDealerLoadingDeal, setAskDealerLoadingDeal] = useState(null);
  const [testDriveLoadingDeal, setTestDriveLoadingDeal] = useState(null);
  const [testDriveLoadingDealParent, setTestDriveLoadingDealParent] = useState(null);
  const testDriveLoadingDealParentFlag = useRef(false);
  const [opened, setOpened] = useState(false);
  const [openedTestDrive, setOpenedTestDrive] = useState(false);
  const [fpShareModalOpen, setFpShareModalOpen] = useState(false);
  const [sharedVehicle, setSharedVehicle] = useState(null);
  const [shareFpLoading, setShareFpLoading] = useState(null);

  const Item = styled(Paper)(({ theme }) => ({
    ...theme.typography.body2,
    padding: theme.spacing(1),
    textAlign: "center",
    borderRadius: "none",
    boxShadow: "none",
  }));

  const StyledButtonGreen = styled(Button)({
    margin: "0 10px",
    borderRadius: "50%",
    width: "40px",
    height: "40px",
    minWidth: "auto",
    fontWeight: "600",
    padding: "5px",
    backgroundColor: "#4CAF50",
    boxShadow:
      "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.2)",
    "&:hover": {
      backgroundColor: "#1976d2",
    },
  });

  const StyledButtonRed = styled(Button)({
    margin: "0 10px",
    borderRadius: "50%",
    width: "40px",
    height: "40px",
    minWidth: "auto",
    fontWeight: "600",
    padding: "5px",
    backgroundColor: "#DC2727",
    boxShadow:
      "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.2)",
    "&:hover": {
      backgroundColor: "#B21E1E",
    },
  });

  const {
    carsInfo,
    loading,
    loadingMore,
    loadMoreCars,
    deleteCarFromGarage,
    fetchVehicleDetails,
    deleteLoading,
    carCount,
    allCarsFetched,
    fetchDealerResponseData,
    formatTimestamp,
    undoLoading,
    componentMetaData,
    fetchInitialCars,
    userDataMemberId,
    aiNegotiationConfirmationModalOpen,
    setAiNegotiationConfirmationModalOpen,
    handleAINegotiation,
    checkedState,
    selectedCars,
    uncheckConfirmationModalOpen,
    setUncheckConfirmationModalOpen,
    handleConfirmCheckout,
    memberInfo,
    setMemberInfo,
    downpaymentValue,
    setDownpaymentValue,
    monthlyPaymentErrorMessage,
    setMonthlyPaymentErrorMessage,
    downPaymentErrorMessage,
    setDownPaymentErrorMessage,
    nonNegotiationLoading,
    handleCheckbox,
    individualAINegotiation,
    negotiationLoading,
    open,
    setOpen,
    openNegotionConfirmation,
    confirmAINegotiation,
    setOpenNegotionConfirmation,
    updatedSelectedCarsRef,
    checkedRow,
    sendDealerRequestMsg,
  } = useGarageListApi();

  const dispatch = useDispatch();
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  useEffect(() => {
    setShowDateTimePicker(openedTestDrive);
  }, [openedTestDrive]);
  const [dateTimeValue, setDateTimeValue] = useState(dayjs(""));
  const [outDoorPrice, setOutDoorPrice] = useState(0);

  const handleTooltipToggle = () => {
    setIconOpen((prev) => !prev);
  };
  const [iconOpen, setIconOpen] = useState(false);
  useEffect(() => {
    let isMounted = true; // Add a flag to check if the component is still mounted

    if (!loading && isMounted) {
      const component = componentMetaData.find(
        (comp) => comp.slug === "my-garage"
      );
      // Example usage in your page/component
      executeComponentAction(dispatch, component.id, "");
    }

    // Cleanup function to reset the flag when the component is unmounted
    return () => {
      isMounted = false;
    };
  }, [loading, componentMetaData]);


  // for tutorial
  const { openTutorialModal } = useGlobalTutorialModal();
  useEffect(() => {
    const checkAndShowTutorial = async () => {
      if (loading) return; // Don't proceed if loading

      const localFlag = hasSeenTutorial("garage_testDrive");
      if (localFlag) return; // Already seen locally, skip

      const shouldShow = await shouldShowTutorial({
        tutorialKey: "garage_testDrive",
        dispatch,
      });

      if (shouldShow) {
        openTutorialModal({
          additionalTopText: "SMS offers from dealers",
          mainText: "Click on Test Drive",
          subText: "Good deals go fast",
          bodyImageSrc: ImageBaseURL + "tutorials/images/garage-test-drive-feature-image.png",
          audioSrc: ImageBaseURL + "tutorials/audios/test-drive-audio.mp3"
        });
      }
    };

    checkAndShowTutorial();
  }, [loading]);

  useEffect(() => {
    // Extract and decode member_id from URL (only if query parameters exist)
    const getQueryParam = (param) => {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get(param);
    };

    const encodedData = getQueryParam("data");

    if (encodedData) {
      try {
        const decodedData = JSON.parse(atob(decodeURIComponent(encodedData)));
        if (decodedData.member_id) {
          setUrlMemberId(decodedData.member_id); // Store extracted member_id
          setUrlRowID(decodedData.row_id); // Store extracted chat_id
        } else {
          throw new Error("member_id not found");
        }
      } catch (error) {
        console.error("Invalid encoded data", error);
        // Handle error (e.g., navigate to an error page)
      }
    }
  }, []); // Run only once on mount

  useEffect(() => {
    if (urlMemberId && userDataMemberId === urlMemberId && urlRowID) {
      // Call onAddVehicleToGarageRedirection only once
      onAddVehicleToGarageRedirection({
        member_id: urlMemberId,
        row_id: urlRowID,
      });
    }
  }, [urlMemberId, userDataMemberId]); // Dependencies to trigger the effect

  // Function to handle adding vehicle to garage
  const onAddVehicleToGarageRedirection = async (swipedData) => {
    const token = localStorage.getItem("token");
    if (!swipedData.row_id || !swipedData.member_id) {
      toast.error(
        "Cannot add vehicle to garage as required info is missing. Please try later."
      );
      return false;
    }

    setLoadingRightSwipe(true);
    try {
      const response = await dispatch(
        addVehicleToGarageRedirection({
          row_id: swipedData.row_id,
          member_id: swipedData.member_id, // Ensure member_id is passed
          token,
        })
      );
      // Navigate to /my-garage after adding the vehicle
      navigate("/my-garage");

      if (response.error) {
        toast.error(
          `Cannot add the vehicle to garage as ${response.payload.message}. Please try later.`
        );
        setLoadingRightSwipe(false);
        return;
      }

      // Success toast
      toast.success(
        `${swipedData.heading ? swipedData.heading : "Vehicle"
        } added to My Garage`
      );
      fetchInitialCars(token);

      // Call the component action API if applicable
      const component = componentMetaData.find(
        (comp) => comp.slug === "add-vehicle-to-garage"
      );
      if (component) {
        // Run this function without waiting for it to complete
        (async () => {
          try {
            await callComponentActionApi(
              dispatch,
              component.id,
              swipedData.row_id
            );
          } catch (error) {
            console.error("Error while calling callComponentActionApi:", error);
          }
        })();
      }
      setLoadingRightSwipe(false);
    } catch (error) {
      console.error("Error while adding vehicle to garage:", error);
      setLoadingRightSwipe(false);
    }
  };

  const handleOpenDeletionDialog = (car) => {
    setCarToDelete(car);
    setDeletionDialogOpen(true);
  };

  const handleConfirmDeletion = () => {
    if (carToDelete) {
      deleteCarFromGarage(
        carToDelete.vin,
        carToDelete.heading,
        carToDelete.mc_row_id
      );
      setCarToDelete(null);
      setDeletionDialogOpen(false);
    }
  };

  const handleFetchVehicleDetails = async (car) => {
    setLoadingDetails(car.vin); // Set loading state
    const details = await fetchVehicleDetails(car.vin);
    setVehicleDetails(details.data[0]);
    setSelectedCar(car);
    setModalOpen(true);
    setSheetDetailsModalOpen(0);
    setLoadingDetails(null); // Clear loading state
  };
  const handleTestDriveDealModal = async (car) => {
    testDriveLoadingDealParentFlag.current = true;
    handleDealerChatOpenModal(car);
    setOpenedTestDrive(true);
  }

  // see your deal
  const handleFetchDealDetails = async (car) => {
    const token = localStorage.getItem("token");
    const memberType = localStorage.getItem("member_type");
    // if (memberType === '2') {
    //   // Check localStorage for is_loan_offer_accepted
    //   let forceUnderProcess = false;
    //   try {
    //     const isAcceptedRaw = localStorage.getItem('is_loan_offer_accepted');
    //     // Accepts both boolean true and string 'true'
    //     if (isAcceptedRaw === 'true' || isAcceptedRaw === true) {
    //       forceUnderProcess = true;
    //     }
    //   } catch (e) {
    //     console.log('Error while calling util:', e);
    //   }
    //   memberBasedLoanOfferActionUtil(dispatch, token, forceUnderProcess);
    //   return;
    // }
    setDealDetails(car.vin); // Set loading state
    const details = await fetchVehicleDetails(car.vin);
    setVehicleDetails(details.data[0]);
    setSelectedCar(car);
    setOutDoorPrice(car?.outdoor_price);
    setMemberInfo(() => ({
      selectedTerm: "",
      preapproved_pct: "",
      calculate_emi: "",
    }));
    setDownpaymentValue(0);
    setMonthlyPaymentErrorMessage("");
    setDownPaymentErrorMessage("");
    setSheetDetailsModalOpen(1);
    setDealDetails(null); // Clear loading state
  };


  const handleDealerChatCloseModal = () => {
    setChatModalOpen(false);
    setOpenedTestDrive(false);
    setShowDateTimePicker(false);
    setDateTimeValue(dayjs());
    setSheetDetailsModalOpen(opened ? 1 : 0);
    testDriveLoadingDealParentFlag.current = false;
    setTestDriveLoadingDealParent("");
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    setSheetDetailsModalOpen(opened ? 1 : 0);
    setOpened(false);
  };

  //close deal modal
  const handleDealCloseModal = () => {
    setSheetDetailsModalOpen(0);
    setVehicleDetails(null);
    setSelectedCar(null);
    setOpened(false);
  };

  const handleShareFastpassClick = async (car, customMessage = '') => {
    const token = localStorage.getItem("token");
    const memberType = localStorage.getItem("member_type");
    // if (memberType === '2') {
    //   // Check localStorage for is_loan_offer_accepted
    //   let forceUnderProcess = false;
    //   try {
    //     const isAcceptedRaw = localStorage.getItem('is_loan_offer_accepted');
    //     // Accepts both boolean true and string 'true'
    //     if (isAcceptedRaw === 'true' || isAcceptedRaw === true) {
    //       forceUnderProcess = true;
    //     }
    //   } catch (e) {
    //     console.log('Error while calling util:', e);
    //   }
    //   memberBasedLoanOfferActionUtil(dispatch, token, forceUnderProcess);
    //   return;
    // }
    // 1. Show confirmation modal (use your existing modal logic)
    setSharedVehicle(car);
    setFpShareModalOpen(true);
  };

  // This function is called from the confirmation modal on "Yes"
  const confirmShareFastpass = async (car, customMessage = '') => {
    setFpShareModalOpen(false);
    setShareFpLoading(car.vin);

    // 2. Send the message
    await sendDealerRequestMsg(car, customMessage);
    const token = localStorage.getItem("token");
    await dispatch(sendShareFastpassDetails({ carDetails: car, token }));

    // 3. Fetch updated messages
    const resp = await fetchDealerResponseData(car);
    if (resp && resp.SalesForceResponse) {
      const parsedResponse = JSON.parse(resp.SalesForceResponse);
      if (
        parsedResponse[0] &&
        parsedResponse[0].error &&
        parsedResponse[0].error.Message
      ) {
        toast.error(parsedResponse[0].error.Message);
        setShareFpLoading(null);
        return;
      }
      if (parsedResponse[0] && parsedResponse[0].success.data) {
        const chatMessages = parsedResponse[0].success.data.map(
          (msg, index) => ({
            id: index + 1,
            text: msg.EmailMessage,
            fromDealer: msg.Sender === "Dealer",
            timestamp: new Date(msg.CreatedDate),
          })
        );
        const sortedMessages = chatMessages.sort(
          (a, b) => a.timestamp - b.timestamp
        );
        setMessages(sortedMessages);
        setChatModalOpen(true);
        setSelectedCar(car);
      }
    }
    setShareFpLoading(null);
    setFpShareModalOpen(false);
  };

  const handleDealerChatOpenModal = async (car) => {
    const token = localStorage.getItem("token");
    const memberType = localStorage.getItem("member_type");
    // if (memberType === '2') {
    //   let forceUnderProcess = false;
    //   try {
    //     const isAcceptedRaw = localStorage.getItem('is_loan_offer_accepted');
    //     // Accepts both boolean true and string 'true'
    //     if (isAcceptedRaw === 'true' || isAcceptedRaw === true) {
    //       forceUnderProcess = true;
    //     }
    //   } catch (e) {
    //     console.log('Error while calling util:', e);
    //   }
    //   memberBasedLoanOfferActionUtil(dispatch, token, forceUnderProcess);
    //   return;
    // }
    if (car.is_negotiation === 0) {
      return toast.warning("To start a conversation with the dealer, you need to Start AI Negotiation for the vehicle first.");
    }
    if (testDriveLoadingDealParentFlag.current) {
      setaskDealerLoading("");
      setAskDealerLoadingDeal("");
      setTestDriveLoadingDeal("");
      setTestDriveLoadingDealParent(car.vin);
    } else {
      setaskDealerLoading(car.vin);
      setAskDealerLoadingDeal(car.vin);
      setTestDriveLoadingDeal(openedTestDrive ? "" : car.vin);
      setTestDriveLoadingDealParent("");
      testDriveLoadingDealParentFlag.current = false;
    }
    const resp = await fetchDealerResponseData(car);
    if (resp && resp.SalesForceResponse) {
      const parsedResponse = JSON.parse(resp.SalesForceResponse);
      // Check for errors in the response
      if (
        parsedResponse[0] &&
        parsedResponse[0].error &&
        parsedResponse[0].error.Message
      ) {
        // Show the error message in a toast and stop the loader
        toast.error(parsedResponse[0].error.Message);
        setaskDealerLoading("");
        setAskDealerLoadingDeal("");
        setTestDriveLoadingDeal("");
        return; // Exit the function early
      }
      if (parsedResponse[0] && parsedResponse[0].success.data) {
        const chatMessages = parsedResponse[0].success.data.map(
          (msg, index) => ({
            id: index + 1,
            text: msg.EmailMessage,
            fromDealer: msg.Sender === "Dealer",
            timestamp: new Date(msg.CreatedDate), // Keep as Date object for sorting
          })
        );

        // Sort messages by timestamp (includes both date and time)
        const sortedMessages = chatMessages.sort(
          (a, b) => a.timestamp - b.timestamp
        );

        // Format timestamps for display with the required format
        const formattedMessages = sortedMessages.map((msg) => ({
          ...msg,
          timestamp: msg.timestamp,
        }));

        setMessages(formattedMessages);
        setaskDealerLoading("");
        setAskDealerLoadingDeal("");
        setTestDriveLoadingDeal("");
        setSheetDetailsModalOpen(0);
        setChatModalOpen(true);
        setSelectedCar(car);
      }
    }
  };

  if (carsInfo.count === 0) {
    return loading ? (
      <Stack
        alignItems="center"
        justifyContent="center"
        sx={{ height: "100vh" }}
      >
        <img
          className="page_bg"
          src={loaderGif}
          alt="Loading..."
          style={{ width: "200px", height: "200px" }}
        />
        <span style={{ fontWeight: "600" }}>
          Fetching Your Favourite Vehicles...
        </span>
      </Stack>
    ) : (
      <NoDataFound />
    ); // Render NoDataFound component when there are no cars
  }

  // Function to handle load more button click
  const handleLoadMore = () => {
    if (!loadingMore && !loading) {
      loadMoreCars();
    }
  };

  // Define the content to be rendered based on the conditions
  let content;
  if (loadingMore && !allCarsFetched) {
    content = (
      <Box
        sx={{ display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <CircularProgress size={20} />
        <Typography
          variant="subtitle2"
          color="#4891FF"
          sx={{ marginLeft: 2, fontWeight: "600" }}
        >
          Loading more vehicles...
        </Typography>
      </Box>
    );
  } else if (carsInfo.carsinfo.length < carsInfo.count && !allCarsFetched) {
    content = (
      <Button
        variant="outlined"
        onClick={handleLoadMore}
        className="map-text page_bg"
        style={{
          textDecoration: "underline",
          backgroundColor: "#fff",
          border: "none",
          color: "#4891FF",
          fontWeight: "800",
        }}
      >
        Click to Load More Vehicles
      </Button>
    );
  }

  return (
    <MasterLayout>
      {loading ? ( // Render loader if either loading is true
        <div className="loader-overlay">
          <div className="loader_container">
            <CircularProgress />
          </div>
        </div>
      ) : (
        <Box
          sx={{ flexGrow: 1, display: "flex", justifyContent: "center", mt: 8 }}
        >
          {undoLoading && (
            <div className="loader-overlay">
              <div className="loader_container">
                <CircularProgress />
              </div>
            </div>
          )}
          {deleteLoading && (
            <div className="loader-overlay">
              <div className="loader_container">
                <CircularProgress />
              </div>
            </div>
          )}
          {loadingRightSwipe && (
            <Box sx={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: 'rgba(255, 255, 255, 0.8)', // Optional: semi-transparent background
              zIndex: 1000, // Ensure it appears above other content
            }}>
              <CircularProgress />
            </Box>
          )}
          <header className="header_my_garage_mb">
            <Grid item xs={12} sx={{ boxShadow: "none" }}>
              <Item className="page_bg">
                <Typography variant="subtitle1" component="subtitle1">
                  <b>MY GARAGE</b>
                </Typography>
              </Item>
              <Item className="page_bg">
                <Typography variant="subtitle1" component="subtitle1">
                  Showing <b>{carCount}</b> of <b>{carsInfo.count}</b> Vehicles
                </Typography>
                <Typography
                  variant="subtitle2"
                  component="subtitle2"
                  display="flex"
                  alignItems="center"
                >
                  Select Upto 5 Vehicles for AI Negotiator
                  <Tooltip
                    title="Receive the lowest dealer pricing with AI Negotiator, Select your top 5"
                    open={iconOpen}
                    onClose={() => setIconOpen(false)}
                    disableFocusListener
                    disableHoverListener
                    disableTouchListener
                    arrow
                  >
                    <IconButton
                      onClick={handleTooltipToggle}
                      sx={{ ml: 1, color: "#e07b3c", p: 0 }}
                    >
                      <InfoOutlinedIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Typography>
              </Item>
            </Grid>
          </header>
          <Grid container spacing={1} mt={16}>
            {carsInfo.carsinfo.map((car, index) => (
              <Grid
                item
                xs={12}
                key={index}
                sx={{ borderBottom: "1px solid #4891FF", padding: "15px" }}
              >
                <Grid container spacing={2}>
                  <Grid
                    item
                    xs={2}
                    container
                    direction="column"
                    sx={{
                      alignItems: "end",
                      justifyContent: "center",
                    }}
                  >
                    <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center">
                      {!car.is_delete_mc && (
                        nonNegotiationLoading === car.mc_row_id ? (
                          <CircularProgress
                            size={24}
                            sx={{
                              color: "#F58C4B",
                            }}
                          />
                        ) : (
                          <Checkbox
                            sx={{
                              color: "#F58C4B",
                              "&.Mui-checked": {
                                color: "#F58C4B",
                              },
                              transform: "scale(1.5)",
                            }}
                            checked={
                              checkedState[car.mc_row_id] ??
                              car.is_negotiation !== 0
                            }
                            onChange={(event) => handleCheckbox(event, car)}
                          />
                        )
                      )}
                    </Box>
                  </Grid>

                  <Grid
                    item
                    xs={5}
                    container
                    direction="row"
                    alignItems="center"
                    justifyContent="center"
                    style={{ paddingLeft: 0 }}
                  >

                    {/* Image container */}
                    <div
                      style={{
                        position: "relative",
                        display: "inline-block",
                      }}
                    >
                      {car.is_delete_mc && (
                        <div className="ribbon-mobile">
                          SOLD
                        </div>
                      )}
                      {!car.is_delete_mc && (
                        <Box display="flex" flexDirection="column" alignItems="center" sx={{ mt: 2 }}>
                          <Button
                            variant="contained"
                            className="garage-deal mb-3"
                            fullWidth
                            onClick={() => handleTestDriveDealModal(car)}
                          >
                            <span className="d-flex align-items-center" style={{ gap: "8px" }}>
                              {testDriveLoadingDealParent === car.vin ? (
                                <CircularProgress size={22} className="text-white" />
                              ) : (
                                <svg width="22" height="22" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path fillRule="evenodd" clipRule="evenodd" d="M47.374 23.687C47.374 36.7689 36.7689 47.374 23.687 47.374C10.605 47.374 0 36.7689 0 23.687C0 10.605 10.605 0 23.687 0C36.7689 0 47.374 10.605 47.374 23.687ZM20.7561 42.4113C19.1956 38.5811 14.7351 28.2039 11.8435 27.24C9.82102 26.5659 7.03108 26.6592 5.00268 26.8647C6.35369 34.8663 12.7199 41.1636 20.7561 42.4113ZM5.51775 18.2875C7.84263 10.4525 15.0974 4.7374 23.687 4.7374C32.2766 4.7374 39.5313 10.4525 41.8562 18.2875C38.1165 17.5867 31.4046 16.5809 23.687 16.5809C15.9693 16.5809 9.25747 17.5867 5.51775 18.2875ZM42.3713 26.8647C40.3429 26.6592 37.5529 26.5659 35.5305 27.24C32.6388 28.2039 28.1783 38.5811 26.6178 42.4113C34.654 41.1636 41.0203 34.8663 42.3713 26.8647Z" fill="white" />
                                </svg>
                              )}
                              Test Drive
                            </span>
                          </Button>
                        </Box>
                      )}

                      {/* Car Image */}
                      <img
                        src={car.photo_url || fallbackImg}
                        alt="CarPhoto"
                        style={{
                          height: 100,
                          width: 150,
                          objectFit: "contain",
                        }}
                        onError={(e) => {
                          e.target.src = fallbackImg;
                        }}
                      />
                      {!car.is_delete_mc && (
                        <Box sx={{ mt: 2 }}>
                          {car.is_negotiation === 0 && (
                            <Box className="d-flex justify-content-center">
                              <Button
                                variant="contained"
                                className="garage-ai-negotiation-btn"
                                onClick={() => individualAINegotiation(car, checkedState)}
                                style={{ padding: "5px 8px" }}
                              >
                                {negotiationLoading === car.mc_row_id ? (
                                  <CircularProgress size={22} className="text-white" />
                                ) : (
                                  <span style={{ fontSize: '10px', textAlign: 'left' }}>
                                    <svg
                                      width="22"
                                      height="22"
                                      viewBox="0 0 87 86"
                                      fill="none"
                                      xmlns="http://www.w3.org/2000/svg"
                                      xmlnsXlink="http://www.w3.org/1999/xlink"
                                    >
                                      <rect
                                        x="0.542725"
                                        y="0.186523"
                                        width="85.762"
                                        height="85.762"
                                        fill="url(#pattern0_3071_1792)"
                                      />
                                      <defs>
                                        <pattern
                                          id="pattern0_3071_1792"
                                          patternContentUnits="objectBoundingBox"
                                          width="1"
                                          height="1"
                                        >
                                          <use
                                            xlinkHref="#image0_3071_1792"
                                            transform="scale(0.00347222)"
                                          />
                                        </pattern>
                                        <image
                                          id="image0_3071_1792"
                                          width="288"
                                          height="288"
                                          preserveAspectRatio="none"
                                          xlinkHref="data:image/png;base64,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"
                                        />
                                      </defs>
                                    </svg>&nbsp;&nbsp;
                                    START AI Negotiator
                                  </span>
                                )}
                              </Button>
                            </Box>
                          )}
                          {car.is_negotiation === 1 && (
                            <Box sx={{ backgroundColor: "#FFFACD" }}>
                              <Typography variant="body2" component="span">
                                <span
                                  style={{
                                    color: "#F58C4B",
                                    fontWeight: "600",
                                    display: "flex",
                                    alignItems: "center",
                                    fontSize: "12px",
                                  }}
                                >
                                  <svg
                                    fill="#F58C4B"
                                    height="25px"
                                    width="25px"
                                    version="1.2"
                                    baseProfile="tiny"
                                    id="Layer_1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlnsXlink="http://www.w3.org/1999/xlink"
                                    viewBox="0 0 256 256"
                                    xmlSpace="preserve"
                                  >
                                    <g
                                      id="SVGRepo_bgCarrier"
                                      strokeWidth="0"
                                    />

                                    <g
                                      id="SVGRepo_tracerCarrier"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    />

                                    <g id="SVGRepo_iconCarrier">
                                      {" "}
                                      <g>
                                        {" "}
                                        <path d="M223.8,99.9c-0.4-0.2-1.5-0.2-1.9-0.4c-0.4-0.2-1.9-0.2-2.6-0.2c-4.3,0-7.4,1.7-9.1,4.7l-22.8,37.6l-28.7,0.5 c-4.7,0-8.7,3.8-8.7,8.7c0,1.3,0.3,2.5,0.8,3.6h-46c0.5-1.1,0.8-2.3,0.8-3.6c0-4.9-4-8.7-8.7-8.7H68.2L45.4,104 c-1.7-3-4.9-4.7-9.1-4.7c-0.6,0-2.1,0-2.6,0.2c-0.4,0.2-1.5,0.2-1.9,0.4c-29.2,6.7-29.3,69-29.1,80.9c0,10.9,3.8,15.7,11.7,18.7 c1.9,0.9,4.3,1.3,6.2,1.3l50.2,0v43.4c0,6.4,4.9,11.3,11.3,11.3c6.4,0,11.3-4.9,11.3-11.3v-54.3c0-3-1.3-6.2-3.4-8.1 c-2.3-2.3-5.3-3.6-8.1-3.6H48.4v-25.1L31,123.5c-0.9-1.3-0.4-2.8,0.6-3.4c1.3-0.9,2.8-0.4,3.4,0.6L55.2,155 c0.1,0.2,0.3,0.4,0.4,0.7v15.7H200v-15.7c0.2-0.2,0.3-0.4,0.4-0.7l20.2-34.3c0.6-1.1,2.1-1.5,3.4-0.6c1.1,0.6,1.5,2.1,0.6,3.4 l-17.4,29.6v25.1h-33.4c-2.8,0-5.7,1.3-8.1,3.6c-2.1,1.9-3.4,5.1-3.4,8.1v54.3c0,6.4,4.9,11.3,11.3,11.3c6.4,0,11.3-4.9,11.3-11.3 v-43.4h50.2c1.9,0,4.3-0.4,6.2-1.3c7.9-3,11.7-7.8,11.7-18.7C253.2,169,253.1,106.6,223.8,99.9z" />{" "}
                                        <ellipse
                                          transform="matrix(0.1222 -0.9925 0.9925 0.1222 99.5814 269.4569)"
                                          cx="202.1"
                                          cy="78.4"
                                          rx="20.2"
                                          ry="20.2"
                                        />{" "}
                                        <ellipse
                                          transform="matrix(0.4719 -0.8817 0.8817 0.4719 -40.881 88.6184)"
                                          cx="53.5"
                                          cy="78.4"
                                          rx="20.2"
                                          ry="20.2"
                                        />{" "}
                                        <path d="M128,75.4c20.6,0,37.4-16.8,37.4-37.4S148.6,0.7,128,0.7c-20.6,0-37.4,16.8-37.4,37.4C90.6,58.7,107.4,75.4,128,75.4z M124.6,19.9V15h6.5v5.1c4.1,0.8,7,2.7,9.2,4.3l-4.5,5.2c-1.4-1.1-4-2.6-6.4-3.1c-3.1-0.7-6.5,0.1-6.7,3.2 c-0.1,1.3,0.5,3.7,7.3,5.6c3.6,1,12,3.2,12,11.1c0,5.3-4.6,9.5-10.8,10.2v4.5h-6.5v-4.8c-3.4-0.7-6.6-2.2-9.6-4.6l4.9-4.9 c0,0,3.6,2.8,7,3.3c3.9,0.5,6.2-1.1,6.5-3.3c0.5-4.1-8.5-5.6-11.7-7.2c-3.9-1.9-7.7-4.4-7.7-9.7C114,24.4,118.3,20.4,124.6,19.9z" />{" "}
                                      </g>{" "}
                                    </g>
                                  </svg>
                                  &nbsp;&nbsp; AI is
                                  Negotiating... &nbsp;
                                </span>
                              </Typography>
                            </Box>
                          )}
                          {car.is_negotiation === 2 && (
                            <Button onClick={() => handleFetchDealDetails(car)} className="deal">
                              {dealDetails === car.vin ? (
                                <CircularProgress
                                  size={20}
                                  sx={{ ml: 1, mt: 1 }}
                                />
                              ) : (
                                <>
                                  <svg
                                    fill="#105DC7"
                                    height="35px"
                                    width="35px"
                                    version="1.1"
                                    id="Layer_1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlnsXlink="http://www.w3.org/1999/xlink"
                                    viewBox="0 0 512 512"
                                    xmlSpace="preserve"
                                    stroke="#105DC7"
                                  >
                                    <g
                                      id="SVGRepo_bgCarrier"
                                      strokeWidth="0"
                                    />
                                    <g
                                      id="SVGRepo_tracerCarrier"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    />
                                    <g id="SVGRepo_iconCarrier">
                                      <g>
                                        <g>
                                          <path d="M512,34.88c0.039-9.349-3.569-18.126-10.159-24.716S486.457-0.028,477.126,0.006c-9.322,0.039-18.098,3.702-24.711,10.314 c-2.8,2.8-7.893,8.848-10.679,12.205c-2.926,3.525-2.442,8.755,1.083,11.68c3.524,2.926,8.754,2.442,11.68-1.083 c4.364-5.256,7.971-9.395,9.646-11.071c3.498-3.497,8.132-5.435,13.05-5.456c0.027,0,0.052,0,0.079,0 c4.862,0,9.419,1.88,12.837,5.299c3.437,3.437,5.32,8.024,5.299,12.916c-0.021,4.917-1.959,9.551-5.456,13.05 c-3.131,3.131-13.893,11.668-29.312,22.439c-9.121-15.594-26.037-26.099-45.37-26.099H293.808 c-16.396,0-31.81,6.385-43.405,17.978L7.613,304.971C2.704,309.88,0,316.407,0,323.349c0,6.942,2.704,13.47,7.613,18.378 l162.667,162.667c5.068,5.067,11.722,7.6,18.378,7.6c6.656,0,13.312-2.534,18.378-7.6l242.792-242.791 c11.593-11.594,17.978-27.008,17.978-43.405V96.735c0-3.585-0.363-7.085-1.051-10.47c17.551-12.077,30.435-22.18,34.929-26.674 C508.298,52.979,511.961,44.203,512,34.88z M451.217,218.198c0,11.965-4.66,23.214-13.119,31.673L195.306,492.664 c-3.664,3.665-9.63,3.665-13.295,0L19.344,329.997c-1.775-1.775-2.754-4.136-2.754-6.648s0.978-4.872,2.754-6.647L262.135,73.911 c8.461-8.46,19.709-13.119,31.673-13.119h121.463c13.64,0,25.53,7.637,31.618,18.859c-9.798,6.488-20.769,13.387-32.408,20.171 c-0.363-0.398-0.734-0.792-1.119-1.177c-13.584-13.584-35.686-13.584-49.27,0c-13.584,13.584-13.584,35.686,0,49.27 c6.792,6.792,15.714,10.187,24.635,10.187c8.921,0,17.843-3.395,24.635-10.187c9.067-9.067,12.072-21.926,9.036-33.517 c10.123-5.893,19.844-11.916,28.815-17.743c0.001,0.028,0.003,0.054,0.003,0.081V218.198z M381.319,127.007 c1.457,2.897,4.381,4.569,7.417,4.569c1.253,0,2.526-0.285,3.722-0.887c4.862-2.446,9.707-4.99,14.504-7.596 c0.048,4.735-1.722,9.485-5.328,13.091c-7.116,7.115-18.692,7.115-25.808,0c-7.115-7.116-7.115-18.692,0-25.808 c3.558-3.558,8.231-5.336,12.904-5.336c3.7,0,7.389,1.134,10.536,3.363c-4.695,2.552-9.46,5.051-14.263,7.468 C380.908,117.928,379.259,122.915,381.319,127.007z" />{" "}
                                        </g>{" "}
                                      </g>{" "}
                                      <g>
                                        {" "}
                                        <g>
                                          {" "}
                                          <path d="M247.162,168.962c-4.581,0-8.295,3.713-8.295,8.295v175.179c0,4.581,3.714,8.295,8.295,8.295s8.295-3.714,8.295-8.295 V177.257C255.457,172.675,251.743,168.962,247.162,168.962z" />{" "}
                                        </g>{" "}
                                      </g>{" "}
                                      <g>
                                        {" "}
                                        <g>
                                          {" "}
                                          <path d="M209.231,240.213c-13.583-13.586-35.686-13.585-49.268-0.001c-13.584,13.584-13.584,35.686,0,49.27 c6.793,6.793,15.713,10.188,24.635,10.187c8.92,0,17.843-3.397,24.634-10.187c6.581-6.581,10.205-15.329,10.205-24.635 S215.812,246.792,209.231,240.213z M197.501,277.75c-7.116,7.116-18.692,7.115-25.807,0c-7.115-7.116-7.115-18.692,0-25.808 c3.558-3.558,8.231-5.336,12.904-5.336s9.346,1.778,12.904,5.336c3.447,3.447,5.345,8.029,5.345,12.904 C202.846,269.721,200.948,274.303,197.501,277.75z" />{" "}
                                        </g>{" "}
                                      </g>{" "}
                                      <g>
                                        {" "}
                                        <g>
                                          {" "}
                                          <path d="M334.36,240.212c-13.584-13.585-35.687-13.584-49.268,0c-13.584,13.584-13.584,35.686,0,49.27 c6.792,6.792,15.713,10.187,24.635,10.187c8.921,0,17.843-3.395,24.634-10.187C347.944,275.898,347.944,253.796,334.36,240.212z M322.629,277.75c-7.116,7.116-18.692,7.115-25.807,0c-7.115-7.116-7.115-18.692,0-25.808c3.558-3.558,8.231-5.336,12.904-5.336 s9.346,1.778,12.904,5.336C329.745,259.058,329.745,270.634,322.629,277.75z" />{" "}
                                        </g>{" "}
                                      </g>{" "}
                                    </g>
                                  </svg>
                                  &nbsp;&nbsp;
                                  <u>See Your Deal</u>
                                </>
                              )}
                            </Button>
                          )}
                        </Box>
                      )}
                    </div>

                    {/* Test Drive and See Your Deal Buttons */}

                  </Grid>

                  {/* Details and Actions on the right side */}
                  <Grid
                    item
                    xs={5}
                    container
                    direction="column"
                    sx={{
                      alignItems: "start",
                      justifyContent: "center",
                    }}
                  >
                    {/* Car details */}
                    <Box sx={{ pl: 0 }}>
                      <Typography
                        variant="subtitle2"
                        color={"#4891FF"}
                        fontWeight={"bold"}
                      >
                        {car.year || ""}
                      </Typography>
                      <Typography variant="subtitle1" fontWeight={"bold"}>
                        {car.heading || ""}
                      </Typography>
                      <Typography variant="subtitle2" fontWeight={"500"}>
                        {car.model || ""} {car.version || ""}{" "}
                        {car.engine_block || ""} {car.drivetrain || ""}
                      </Typography>
                      {car.price && (
                        <Typography
                          gutterBottom
                          variant="subtitle1"
                          component="div"
                        >
                          <b>$ {car.price.toLocaleString() || ""}</b>
                        </Typography>
                      )}
                    </Box>

                    {/* More details button */}
                    {!car.is_delete_mc && (
                      <Button
                        onClick={() => handleFetchVehicleDetails(car)}
                        sx={{
                          background: "transparent",
                          border: "none",
                          color: "cornflowerblue",
                          padding: 0,
                          fontWeight: "bold",
                          textDecoration: "underline",
                          textTransform: "none"
                        }}
                      >
                        {loadingDetails === car.vin ? (
                          <CircularProgress size={14} sx={{ ml: 1 }} />
                        ) : (
                          "View More Details"
                        )}
                      </Button>
                    )}
                  </Grid>
                </Grid>
                <Grid
                  className="d-flex justify-content-center align-items-center"
                >
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "flex-start",
                      alignItems: "flex-start",
                      flexWrap: "wrap",
                      gap: 2,
                      mt: 1,
                      pl: 1,
                    }}
                  >
                    {/* Ask Dealer */}
                    {!car.is_delete_mc && (
                      <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center", width: 70 }}>
                        <StyledButtonGreen onClick={() => handleDealerChatOpenModal(car)}>
                          {askDealerLoading === car.vin ? (
                            <CircularProgress size={20} sx={{ color: "white" }} />
                          ) : (
                            <Icon icon="calendar" size={20} />
                          )}
                        </StyledButtonGreen>
                        <Typography variant="caption" sx={{ mt: 0.5, textAlign: "center" }}>
                          Ask <br /> Dealer
                        </Typography>
                      </Box>
                    )}

                    {/* Share FastPass */}
                    {!car.is_delete_mc && car.is_negotiation === 2 && (
                      <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center", width: 70 }}>
                        <Box sx={{ position: "relative" }}>
                          {shareFpLoading === car.vin ? (
                            <CircularProgress size={35} sx={{ color: "#4891FF" }} />
                          ) : (
                            <img
                              onClick={() => handleShareFastpassClick(car)}
                              src={ImageBaseURL + "fp_share_logo_transparent.png"}
                              width={40}
                              height={40}
                              alt="header"
                              style={{ objectFit: "contain" }}
                            />
                          )}
                        </Box>
                        <Typography variant="caption" sx={{ mt: 0.5, textAlign: "center" }}>
                          Share <br /> FastPass
                        </Typography>
                      </Box>
                    )}

                    {/* Delete */}
                    <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center", width: 70 }}>
                      <StyledButtonRed onClick={() => handleOpenDeletionDialog(car)}>
                        <CloseIcon fontSize="small" sx={{ color: "#ffffff" }} />
                      </StyledButtonRed>
                      <Typography variant="caption" sx={{ mt: 0.5, textAlign: "center" }}>
                        Delete <br /> Vehicle
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            ))}
            <Grid item xs={12} sx={{ textAlign: "center", padding: "10px" }}>
              {content}
            </Grid>
          </Grid>
          {/* <Box
            sx={{
              position: "fixed",
              bottom: 100,
              right: 20,
              display: "flex",
              alignItems: "center",
            }}
            onMouseEnter={() => setOpen(true)}
            onMouseLeave={() => setOpen(false)}
          >
            {open && (
              <Button
                variant="contained"
                className="garage-ai-negotiation-btn"
                onClick={handleAINegotiation}
              >
                <svg
                  width="25"
                  height="25"
                  viewBox="0 0 87 86"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlnsXlink="http://www.w3.org/1999/xlink"
                >
                  <rect
                    x="0.542725"
                    y="0.186523"
                    width="85.762"
                    height="85.762"
                    fill="url(#pattern0_3071_1792)"
                  />
                  <defs>
                    <pattern
                      id="pattern0_3071_1792"
                      patternContentUnits="objectBoundingBox"
                      width="1"
                      height="1"
                    >
                      <use
                        xlinkHref="#image0_3071_1792"
                        transform="scale(0.00347222)"
                      />
                    </pattern>
                    <image
                      id="image0_3071_1792"
                      width="288"
                      height="288"
                      preserveAspectRatio="none"
                      xlinkHref="data:image/png;base64,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"
                    />
                  </defs>
                </svg>
                START AI Negotiator
              </Button>
            )}

            <Fab
              color="primary"
              aria-label="AI"
              sx={{
                backgroundColor: "#F58C4B",
                "&:hover": { backgroundColor: "#e07b3c" },
                marginLeft: open ? 1 : 0,
                width: 56, // Adjusted for better visibility
                height: 56,
              }}
              onClick={() => setOpen(!open)}
            >
              {open ? (
                <CloseIcon sx={{ fontSize: 28 }} /> // Show Close Icon when hovered
              ) : (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                  }}
                >
                  <svg
                    width="45"
                    height="45"
                    viewBox="0 0 87 86"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlnsXlink="http://www.w3.org/1999/xlink"
                  >
                    <rect
                      x="0.542725"
                      y="0.186523"
                      width="85.762"
                      height="85.762"
                      fill="url(#pattern0_3071_1792)"
                    />
                    <defs>
                      <pattern
                        id="pattern0_3071_1792"
                        patternContentUnits="objectBoundingBox"
                        width="1"
                        height="1"
                      >
                        <use
                          xlinkHref="#image0_3071_1792"
                          transform="scale(0.00347222)"
                        />
                      </pattern>
                      <image
                        id="image0_3071_1792"
                        width="288"
                        height="288"
                        preserveAspectRatio="none"
                        xlinkHref="data:image/png;base64,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"
                      />
                    </defs>
                  </svg>
                </Box>
              )}
            </Fab>
          </Box> */}
        </Box>
      )}
      <VehicleDetailsModal
        open={modalOpen}
        onClose={handleCloseModal}
        vehicleDetails={vehicleDetails}
        selectedCar={selectedCar}
      />
      <DealSheetModal
        open={sheetDetailsModalOpen}
        onClose={handleDealCloseModal}
        vehicleDetails={vehicleDetails}
        selectedCar={selectedCar}
        loadingDetails={loadingDetails}
        handleFetchVehicleDetails={handleFetchVehicleDetails}
        handleDealerChatOpenModal={handleDealerChatOpenModal}
        askDealerLoadingDeal={askDealerLoadingDeal}
        testDriveLoadingDeal={testDriveLoadingDeal}
        setOpened={setOpened}
        openedTestDrive={openedTestDrive}
        setOpenedTestDrive={setOpenedTestDrive}
        outDoorPrice={outDoorPrice}
        memberInfo={memberInfo}
        setMemberInfo={setMemberInfo}
        downpaymentValue={downpaymentValue}
        setDownpaymentValue={setDownpaymentValue}
        monthlyPaymentErrorMessage={monthlyPaymentErrorMessage}
        setMonthlyPaymentErrorMessage={setMonthlyPaymentErrorMessage}
        downPaymentErrorMessage={downPaymentErrorMessage}
        setDownPaymentErrorMessage={setDownPaymentErrorMessage}
        onShareFastpass={confirmShareFastpass}
        shareFpLoading={shareFpLoading}
      />
      <AskDealerChatModalMobile
        open={chatModalOpen}
        onClose={handleDealerChatCloseModal}
        selectedCar={selectedCar}
        sfmessages={messages}
        formatTimestamp={formatTimestamp}
        refreshMessages={handleDealerChatOpenModal} // Pass the function to refresh messages
        showDateTimePicker={showDateTimePicker}
        dateTimeValue={dateTimeValue}
        setShowDateTimePicker={setShowDateTimePicker}
        setDateTimeValue={setDateTimeValue}
      />
      <DeletionConfirmationDialog
        open={deletionDialogOpen}
        onClose={() => setDeletionDialogOpen(false)}
        onConfirm={handleConfirmDeletion}
        carToDelete={carToDelete}
      />
      <AiNegotiationConfirmationModal
        show={aiNegotiationConfirmationModalOpen}
        onHide={() => setAiNegotiationConfirmationModalOpen(false)}
        selectedCars={selectedCars}
      />
      <UncheckConfirmationModal
        open={uncheckConfirmationModalOpen}
        onClose={() => setUncheckConfirmationModalOpen(false)}
        onConfirm={handleConfirmCheckout}
      />
      <NegotiationConfirmationModal
        open={openNegotionConfirmation}
        onClose={() => setOpenNegotionConfirmation(false)}
        onConfirm={confirmAINegotiation}
        selectedCars={selectedCars}
        updatedSelectedCarsRef={updatedSelectedCarsRef}
        checkedRow={checkedRow}
      />
      <FpShareConfirmationModal
        show={fpShareModalOpen}
        onHide={() => setFpShareModalOpen(false)}
        sharedVehicle={sharedVehicle}
        handleDealerChatOpenModal={confirmShareFastpass}
      />
    </MasterLayout>
  );
}

export default GarageMobile;
