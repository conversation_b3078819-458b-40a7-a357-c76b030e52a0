import { useNavigate } from 'react-router-dom';
import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import { ImageBaseURL } from '../../../config';
import Icon from "../../../icon";

function NoDataFound() {
    const navigate = useNavigate();
    const Item = styled(Paper)(({ theme }) => ({
        ...theme.typography.body2,
        padding: theme.spacing(1),
        textAlign: "center",
        borderRadius: "none",
        boxShadow: "none",
    }));

    const StyledButton = styled(Button)({
        borderRadius: "10px",
        width: "15%",
        fontWeight: "600",
        padding: "15px",
        backgroundColor: "#4891FF",
        "&:hover": {
            backgroundColor: "primary",
        },
    });

    const token = localStorage.getItem('token');
    const handleReload = () => {
        if (token) {
            navigate('/specific-search');
        } else {
            navigate('/');
        }
    };

    return (
        <Box sx={{
            flexGrow: 1,
            height: "100vh",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
        }} >
            <Grid container spacing={2} sx={{ padding: "30px 0px" }}>
                <Grid item xs={12}>
                    <Item className='page_bg'>
                        <img
                            src={ImageBaseURL + "no_data_garage.png"}
                            alt="Not Found"
                            style={{ width: "20%", height: "20%" }}
                        />
                    </Item>
                </Grid>
                <Grid item xs={12}>
                    <Item className='page_bg'>
                        <Typography
                            variant="subtitle2"
                            gutterBottom
                            sx={{ fontWeight: "800" }}
                        >
                            <b>Your Garage is Empty</b>
                        </Typography>
                        <Typography
                            variant="subtitle2"
                            gutterBottom
                            sx={{ fontWeight: "500" }}
                        >
                            Add Vehicles to Get Started
                        </Typography>
                    </Item>
                </Grid>
                <Grid item xs={12}>
                    <Item className='page_bg'>
                        <StyledButton
                            variant="contained"
                            onClick={handleReload}
                        >
                            <Icon icon="searching-car-white" size={35} />
                            <span className="btn_class_landing_mobile">
                                BROWSE VEHICLES
                            </span>
                        </StyledButton>
                    </Item>
                </Grid>
            </Grid>
        </Box>
    );
}

export default NoDataFound;