import { Mo<PERSON>, But<PERSON> } from "react-bootstrap";
import PropTypes from "prop-types";
import { FaCheck } from "react-icons/fa";
import useGarageListApi from "../api/fetchGarageApi";
import AddShoppingCartIcon from "@mui/icons-material/AddShoppingCart";

const AiNegotiationConfirmationModal = ({ show, onHide, selectedCars }) => {
  const { handleAINegotiation } = useGarageListApi();

  /**
   * Handles user action (yes/no) on AI negotiation confirmation modal
   * @param {string} status - yes/no
   */

  const handleAction = (status) => {
    if (status === "no") {
      onHide(); // Close modal if user wants to add more vehicles
      return;
    }
    // Pass checkedVehiclesData explicitly
    handleAINegotiation(status, selectedCars);
    onHide(); // Close modal after confirming selection
  };

  return (
    <Modal show={show} onHide={onHide} centered>
      <Modal.Header
        className="conf-modal-header-fixed"
        closeButton
      ></Modal.Header>
      <Modal.Body className="conf-modal-body-scrollable">
        <div className="conf-modal-question">
          <b>
            You can select up to 5 Vehicles for AI Negotiator. Do you want to
            select more vehicles?
          </b>
        </div>
        <div className="conf-modal-buttons">
          <Button
            className="custom-button no-button"
            style={{ backgroundColor: "#F58C4B", borderColor: "#F58C4B", color: "white" }}
            onClick={() => handleAction("no")}
          >
            <AddShoppingCartIcon /> Add More
          </Button>
          <Button
            className="custom-button yes-button"
            onClick={() => handleAction("yes")}
          >
            <FaCheck className="conf-modal-icon" /> Continue
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
};

AiNegotiationConfirmationModal.propTypes = {
  show: PropTypes.bool.isRequired,
  onHide: PropTypes.func.isRequired,
  selectedCars: PropTypes.array.isRequired,
};

export default AiNegotiationConfirmationModal;
