import PropTypes from "prop-types";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Text<PERSON>ield,
  CircularProgress,
  <PERSON><PERSON> 
} from "@mui/material";
import { Modal } from "react-bootstrap";
import React, { useEffect, useState, useRef } from "react";
import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import SendIcon from "@mui/icons-material/Send";
import HeaderLogo from "../../../../public/logo_fp_blue.png";
import useGarageListApi from "../api/fetchGarageApi";
import { toast } from "react-toastify";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { MobileDateTimePicker } from '@mui/x-date-pickers/MobileDateTimePicker';
import dayjs from "dayjs";
import CloseIcon from "@mui/icons-material/Close";
import { pageMetaData } from "../../../../data/pageMetaData";
import { pageComponentData } from "../../../../data/componentMetaData";
import { executeComponentAction } from "../../../util/componentActionUtil";
import { useDispatch } from "react-redux";

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const labelStyle = {
  color: "#4891FF",
};

const CustomTextField = styled(TextField)({
  "& .MuiInputLabel-root": labelStyle,
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "bold",
    },
  },
});

const ChatMessage = styled(Paper)(({ theme, alignRight }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  maxWidth: "70%",
  marginBottom: theme.spacing(2),
  backgroundColor: alignRight ? "#4891FF" : "#61C454",
  color: "#fff",
  textAlign: "left",
  alignSelf: alignRight ? "flex-end" : "flex-start",
  borderRadius: alignRight ? "10px 0px 10px 10px" : "0px 10px 10px 10px",
  position: "relative",
  display: "flex",
  flexDirection: "column",
  alignItems: alignRight ? "flex-end" : "flex-start",
  wordBreak: "break-word",
}));

const Timestamp = styled(Typography)(({ theme }) => ({
  fontSize: "0.75rem",
  color: "#ccc",
  marginTop: theme.spacing(0.5),
}));

const StyledButtonBlue = styled(Button)({
  borderRadius: "80%",
  width: "40px",
  height: "40px",
  minWidth: "auto",
  fontWeight: "600",
  padding: "5px",
  backgroundColor: "#4891FF",
  boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.2)",
  "&:hover": {
    backgroundColor: "#1976d2",
  },
});

const AskDealerChatModalMobile = ({
  open,
  onClose,
  selectedCar,
  sfmessages,
  formatTimestamp,
  refreshMessages,
  showDateTimePicker,
  dateTimeValue,
  setShowDateTimePicker,
  setDateTimeValue,
}) => {
  const [userRequestMsg, setUserRequestMsg] = useState("");
  const [userMsgSendLoader, setUserMsgSendLoader] = useState(null);
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hello! How can I assist you today?",
      fromDealer: true,
    },
  ]);
  const [pageId, setPageId] = useState(null);
  const [componentMetaData, setComponentMetaData] = useState(null); // State to store the componentMetaData
  const truncateText = (text, maxLength) => {
    if (text.length > maxLength) {
      return text.slice(0, maxLength) + "...";
    }
    return text;
  }; // Truncate text if necessary

  const messageEndRef = useRef(null);
  const scrollToBottom = () => {
    messageEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const { sendDealerRequestMsg, memberName } = useGarageListApi();

  const handleToggleDateTimePicker = () => {
    setShowDateTimePicker((prevState) => !prevState);
    setDateTimeValue(dayjs(""));
  };
  // Code For capture send message button click //
  const dispatch = useDispatch(); // Redux dispatch
  useEffect(() => {
    // Get the page_id from pageMetaData based on slug
    const slug = "my-garage"; // Replace this with the desired slug
    const pageData = pageMetaData.find((page) => page.slug === slug);
    if (pageData) {
      setPageId(pageData.id);
    }

    // Get Page Componenet Meta Data
    const matchingComponents = pageComponentData.filter(
      (pageComponent) => pageComponent.page_id === pageId
    );

    if (matchingComponents.length > 0) {
      setComponentMetaData(matchingComponents);
    }
  }, [pageId]);

  useEffect(() => {
    // Get the page_id from pageMetaData based on slug
    const slug = "ask-dealer-send-message"; // Replace this with the desired slug
    const pageData = pageMetaData.find((page) => page.slug === slug);
    if (pageData) {
      setPageId(pageData.id);
    }

    // Get Page Componenet Meta Data
    const matchingComponents = pageComponentData.filter(
      (pageComponent) => pageComponent.page_id === pageId
    );

    if (matchingComponents.length > 0) {
      setComponentMetaData(matchingComponents);
    }
  }, [pageId]);

  // End //
  
  const sendAskDealerMessage = async (cardetails, userMsg) => {
    const formattedDateTime =
      dateTimeValue && dateTimeValue.isValid()
        ? dateTimeValue.format("YYYY-MM-DD HH:mm:ss")
        : null;
    // Proceed only if userMsg has a value or formattedDateTime is valid
    if (
      !userMsg &&
      (!formattedDateTime ||
        formattedDateTime === null ||
        formattedDateTime === "Invalid date")
    ) {
      return;
    }
    setUserMsgSendLoader(true);
    try {
      await sendDealerRequestMsg(cardetails, userMsg, formattedDateTime, testDriveMessage);
      setUserRequestMsg(""); // Clear the text field if needed
      setDateTimeValue(dayjs(""));
      setShowDateTimePicker(false);
      toast.success(
        `Message Sent Successfully. Waiting for dealer response...`
      );

      await refreshMessages(cardetails);
    } catch (error) {
      console.error("Failed to send message:", error);
    } finally {
      setUserMsgSendLoader(false);
    }
    const component = componentMetaData.find((comp) => comp.slug === "ask-dealer-send-message");
      if (component) {
        executeComponentAction(dispatch, component.id, cardetails.mc_row_id);
      }
  };

  useEffect(() => {
    const defaultMessage = {
      id: 1,
      text: "Hello! How can I assist you today?",
      fromDealer: true,
    };
    if (sfmessages && sfmessages.length > 0) {
      // Process API data
      const formattedMessages = sfmessages.map((msg) => ({
        id: msg.id,
        text: msg.text,
        fromDealer: msg.fromDealer,
        timestamp: msg.timestamp,
      }));
        // Combine default message with API messages and sort by timestamp
        const allMessages = [defaultMessage, ...formattedMessages].sort(
          (a, b) => new Date(a.timestamp) - new Date(b.timestamp)
        );

      setMessages(allMessages);
    }
  }, [sfmessages]);

  let testDriveMessage = "";
  if(selectedCar){
    testDriveMessage = `I'm interested in scheduling a test drive for <b>${selectedCar.heading}</b>.<br />Are you available on`;
  }else{
    testDriveMessage = "";
  }
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
  return (
    <Modal show={open} onHide={onClose} centered size="lg">
      <Modal.Header
        style={{ justifyContent: "center", alignItems: "center" }}
        closeButton
      >
        <Modal.Title>
          <Box sx={{ flexGrow: 1 }}>
            <Grid container spacing={1}>
              <Grid item xs={2}>
                <Item
                  style={{
                    display: "flex",
                    justifyContent: "left",
                    alignItems: "center",
                  }}
                >
                  <img
                    src={HeaderLogo}
                    alt="header"
                    style={{ maxWidth: "100%", maxHeight: "100%" }}
                  />
                </Item>
              </Grid>
              <Grid
                item
                xs={9}
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "end",
                }}
              >
                <Item
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  {selectedCar && (
                    <Typography
                      variant="subtitle2"
                      gutterBottom
                      style={{ fontWeight: "bold" }}
                      title={selectedCar.seller_name.toUpperCase()}
                    >
                      {truncateText(selectedCar.seller_name.toUpperCase(), 25)}
                    </Typography>
                  )}
                </Item>
              </Grid>
            </Grid>
          </Box>
        </Modal.Title>
      </Modal.Header>
      <Modal.Body className="modal-body-scrollable">
        <Paper
          sx={{
            maxWidth: "100%",
            flexGrow: 1,
            backgroundColor: (theme) =>
              theme.palette.mode === "dark" ? "#1A2027" : "#fff",
          }}
        >
          <Box sx={{ display: "flex", flexDirection: "column", padding: 1 }}>
            {selectedCar &&
              messages.map((msg) => (
                <ChatMessage key={msg.id} alignRight={!msg.fromDealer}>
                  {msg.text
                    .replace(/\\n/g, "\n") // Convert escaped new lines to actual new lines
                    .split("\n") // Split by new lines
                    .map((line, index) => (
                      <React.Fragment key={index}>
                        {line.trim() === "" ? (
                          <br style={{ lineHeight: "2em" }} />
                        ) : (
                          <span dangerouslySetInnerHTML={{ __html: line }} /> // Render HTML safely
                        )}
                        <br />
                      </React.Fragment>
                    ))}
                  <Timestamp alignRight={!msg.fromDealer} style={{ color: "white" }}>
                    {formatTimestamp(msg.timestamp)}
                  </Timestamp>
                </ChatMessage>
              ))}
            <div ref={messageEndRef} />
          </Box>
        </Paper>
      </Modal.Body>
      <Modal.Footer className="modal-footer-fixed">
        <Grid container spacing={1} justifyContent="center" alignItems="center" direction="row">
          <Grid
            item
            xs={12} // Adjust width for the icon on mobile devices
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            {showDateTimePicker ? (
              <Typography variant="body1" sx={{ marginBottom: 2, textAlign: "center" }}
                dangerouslySetInnerHTML={{ __html: testDriveMessage }}
              />
            ) : (
              ''
            )}
          </Grid>
        </Grid>
        <Grid container spacing={1} justifyContent="center" alignItems="center" direction="row">
          <Grid
            item
            xs={2} // Adjust width for the icon on mobile devices
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <StyledButtonBlue onClick={handleToggleDateTimePicker}>
              {showDateTimePicker ? (
                // Cross icon when DateTimePicker is active
                <CloseIcon sx={{ color: "white" }} /> 
              ) : (
                // Default SVG icon before DateTimePicker is shown
                <svg
                  width="23"
                  height="23"
                  viewBox="0 0 48 48"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M47.6388 23.8481C47.6388 36.9301 37.0338 47.5351 23.9519 47.5351C10.8699 47.5351 0.264893 36.9301 0.264893 23.8481C0.264893 10.7662 10.8699 0.161133 23.9519 0.161133C37.0338 0.161133 47.6388 10.7662 47.6388 23.8481ZM21.021 42.5724C19.4605 38.7423 15 28.365 12.1084 27.4012C10.0859 26.727 7.29597 26.8203 5.26757 27.0258C6.61858 35.0274 12.9848 41.3247 21.021 42.5724ZM5.78265 18.4486C8.10752 10.6136 15.3623 4.89853 23.9519 4.89853C32.5415 4.89853 39.7962 10.6136 42.1211 18.4486C38.3814 17.7479 31.6695 16.742 23.9519 16.742C16.2342 16.742 9.52236 17.7479 5.78265 18.4486ZM42.6362 27.0258C40.6078 26.8203 37.8178 26.727 35.7954 27.4012C32.9037 28.365 28.4432 38.7423 26.8827 42.5724C34.9189 41.3247 41.2852 35.0274 42.6362 27.0258Z"
                    fill="white"
                  />
                </svg>
              )}
            </StyledButtonBlue>
          </Grid>
          <Grid
            item
            xs={8} // Adjust width for the icon on mobile devices
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            {showDateTimePicker ? (
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <Box sx={{ width: { xs: '100%', sm: 'auto' } }}>
                {isIOS ? (
                    // For iOS, use native input field with visual enhancements
                    <TextField
                      type="datetime-local"
                      label="Select Date Time Slot"
                      value={dateTimeValue ? dayjs(dateTimeValue).format('YYYY-MM-DDTHH:mm') : ''}
                      onChange={(e) => setDateTimeValue(dayjs(e.target.value))}
                      placeholder="Tap to select date/time"
                      InputProps={{
                        style: {
                          cursor: 'pointer', // Add pointer cursor for better indication
                        },
                      }}
                      fullWidth
                      sx={{
                        minWidth: { xs: '100%', sm: '200px' },
                        marginTop: { xs: 2, sm: 0 },
                      }}
                    />
                  ) : (
                    // For non-iOS, use MUI MobileDateTimePicker
                    <MobileDateTimePicker
                      label="Select Date Time Slot"
                      value={dateTimeValue}
                      onChange={(newValue) => setDateTimeValue(newValue)}
                      minDateTime={dayjs()}
                      sx={{
                        minWidth: { xs: '100%', sm: '200px' },
                        marginTop: { xs: 2, sm: 0 },
                      }}
                    />
                  )}
                </Box>
              </LocalizationProvider>
            ) : (
              <CustomTextField
                id="outlined-multiline-flexible"
                label="Type Your Query Here..."
                variant="outlined"
                multiline
                fullWidth
                sx={{
                  width: "100%",
                  background: "#ffffff",
                }}
                autoComplete="off"
                InputProps={{
                  sx: {
                    fontSize: "16px",
                  },
                }}
                value={userRequestMsg}
                onChange={(e) => setUserRequestMsg(e.target.value)}
              />
            )}
          </Grid>
          <Grid
            item
            xs={2} // Adjust width for the icon on mobile devices
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            {userMsgSendLoader ? (
              <CircularProgress size={40} sx={{ color: "#4891FF" }} />
            ) : (
              <SendIcon
                sx={{
                  color: "#4891FF",
                  cursor: "pointer",
                  fontSize: "40px",
                }}
                onClick={() =>
                  sendAskDealerMessage(selectedCar, userRequestMsg)
                }
              />
            )}
          </Grid>
        </Grid>
      </Modal.Footer>
    </Modal>
  );
};

AskDealerChatModalMobile.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  selectedCar: PropTypes.object, // Change this to object type to match your data structure
  sfmessages: PropTypes.arrayOf(PropTypes.object).isRequired,
  formatTimestamp: PropTypes.func.isRequired,
  refreshMessages: PropTypes.func.isRequired,
  showDateTimePicker: PropTypes.bool.isRequired, // Assuming it's a boolean
  dateTimeValue: PropTypes.instanceOf(Date).isRequired, // Assuming it's a Date object
  setShowDateTimePicker: PropTypes.func.isRequired,
  setDateTimeValue: PropTypes.func.isRequired,
};

export default AskDealerChatModalMobile;
