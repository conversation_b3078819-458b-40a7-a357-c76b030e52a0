import PropTypes from "prop-types";
import {
  Typo<PERSON>,
  CircularProgress,
  TextField,
  Autocomplete,
  InputAdornment,
} from "@mui/material";
import { Mo<PERSON>, But<PERSON> } from "react-bootstrap";
import { useEffect, useRef, useState } from "react";
import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import ButtonBase from "@mui/material/ButtonBase";
import "react-image-gallery/styles/css/image-gallery.css";
import IconButton from "@mui/material/IconButton";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import Tooltip from "@mui/material/Tooltip";
import { ImageBaseURL } from "../../../config";
import Icon from "../../../icon";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { getInterest, getUser } from "../../../store/apps/user";
import { validateInput } from "../../../util/inputFieldNumberValidation";
import { useMediaQuery } from "react-responsive";
import FpShareConfirmationModal from "./fpShareConfirmationModal";
import useGarageListApi from "../api/fetchGarageApi";

const Img = styled("img")({
  margin: "auto",
  display: "block",
  maxWidth: "100%",
  maxHeight: "100%",
});

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const CustomAutocomplete = styled(Autocomplete)(({ disabled }) => ({
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "semibold",
    },
  },
  "& .MuiAutocomplete-option": {
    fontWeight: "semibold",
  },
  "& .MuiSvgIcon-root": {
    color: disabled ? "#C0C0C0" : "#4891FF",
  },
}));

const enabledLabelStyle = {
  color: "#4891FF", // Blue color for enabled state
};

const CustomTextFieldZip = styled(TextField)({
  "& .MuiInputLabel-root": enabledLabelStyle, // Apply label style
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "semibold", // Bold the input field value
    },
  },
});

const CustomTextField = styled(TextField)({
  "& .MuiInputLabel-root": enabledLabelStyle, // Apply label style
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
  },
});

const DealSheetModal = ({
  open,
  onClose,
  vehicleDetails,
  selectedCar,
  loadingDetails,
  handleFetchVehicleDetails,
  handleDealerChatOpenModal,
  askDealerLoadingDeal,
  testDriveLoadingDeal,
  setOpened,
  openedTestDrive,
  setOpenedTestDrive,
  memberInfo,
  setMemberInfo,
  downpaymentValue,
  setDownpaymentValue,
  monthlyPaymentErrorMessage,
  setMonthlyPaymentErrorMessage,
  downPaymentErrorMessage,
  setDownPaymentErrorMessage,
  onOpenShareModal,
  onShareFastpass,
  shareFpLoading,
}) => {
  
  const { shareMessage } = useGarageListApi(); // get the share message from the api
  const fallbackImg = `${ImageBaseURL}fallback_image.png`;
  const [copied, setCopied] = useState(false);
  const [adjustedPrincipal, setAdjustedPrincipal] = useState(0);
  const [calculationLoader, setCalculationLoader] = useState(false);
  const [savedMonthlyPayment, setSavedMonthlyPayment] = useState(0);
  const [isDownPaymentChanged, setIsDownPaymentChanged] = useState(false);
  const [newDownPayment, setNewDownPayment] = useState(0);
  const [isMonthlyPaymentChanged, setIsMonthlyPaymentChanged] = useState(false);
  const [financeAmount, setFinanceAmount] = useState("");
  const [totalAmountAccured, setTotalAmountAccured] = useState("");
  const isSmallMobile = useMediaQuery({ query: "(max-width: 375px)" });
  const [showShareModal, setShowShareModal] = useState(false);
  const [sharedVehicle, setSharedVehicle] = useState(null);
  const isTabletOrMobile = useMediaQuery({ query: '(max-width: 992px)' });
  const handleAskDealerModal = (selectedCar) => {
    handleDealerChatOpenModal(selectedCar);
    setOpened(true);
  };

  const handleTestDriveModal = (selectedCar) => {
    handleDealerChatOpenModal(selectedCar);
    setOpened(true);
    setOpenedTestDrive(true);
  };

  const viewDetails = (selectedCar) => {
    handleFetchVehicleDetails(selectedCar);
    setOpened(true);
  };

  const handleCopyToClipboard = () => {
    navigator.clipboard
      .writeText(selectedCar.vin)
      .then(() => {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000); // Reset after 2 seconds
      })
      .catch((err) => console.error("Failed to copy: ", err));
  };

  const [inputValue, setInputValue] = useState("");
  const [interestDropdown, setInterestDropdown] = useState([]);
  const [preAprvAmount, setpreAprvAmount] = useState("");
  const [term, setTerm] = useState("");
  const outTheDoorPrice = useRef("");
  const [termOptions, setTermOptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const apiCallCompletedRef = useRef(false);
  const navigate = useNavigate();
  const dispatch = useDispatch(); // or use your existing modal state
  let token = localStorage.getItem("token");

  useEffect(() => {
    if (selectedCar) {
      setInputValue(selectedCar?.outdoor_price?.toLocaleString() || "");
      setpreAprvAmount(selectedCar?.outdoor_price || "");
      outTheDoorPrice.current = selectedCar?.outdoor_price || "";
      setAdjustedPrincipal(selectedCar?.outdoor_price || 0);
    } else {
      setInputValue("");
      setpreAprvAmount("");
      outTheDoorPrice.current = "";
    }
  }, [selectedCar]);

  useEffect(() => {
    const fetchUserData = async () => {
      setNewDownPayment(0);
      setIsDownPaymentChanged(false);
      setIsMonthlyPaymentChanged(false);
      setLoading(false);
      setCalculationLoader(true);
      const number = localStorage.getItem("ph_number");
      if (!number || !token) {
        return;
      }

      try {
        dispatch(getUser({ phone_number: number, authToken: token })).then(
          (response) => {
            if (
              response &&
              response.meta &&
              response.meta.requestStatus === "fulfilled"
            ) {
              const userData = response.payload;
              setTerm(userData.term);
              getUserInterest(
                userData.lender_id,
                userData.fico_score,
                userData.term,
                outTheDoorPrice.current
              );
              // End //

              apiCallCompletedRef.current = true;
              setLoading(false);
              setCalculationLoader(false);
              setFinanceAmount(outTheDoorPrice.current);
            } else {
              navigate("/");
            }
          }
        );
      } catch (error) {
        console.error("Error fetching user data:", error);
        navigate("/");
      }
    };

    const getUserInterest = (lender_id, fico_score, term, preAprvAmount) => {
      try {
        dispatch(
          getInterest({
            lenderId: lender_id,
            creditScore: fico_score,
            authToken: token,
          })
        ).then((response) => {
          if (
            response &&
            response.meta &&
            response.meta.requestStatus === "fulfilled"
          ) {
            const interestDropdown = response.payload;
            setInterestDropdown(interestDropdown);

            // Update termOptions with preapproved_pct
            const updatedTermOptions = [
              { label: "36 months", value: "36" },
              { label: "48 months", value: "48" },
              { label: "60 months", value: "60" },
              { label: "72 months", value: "72" },
              { label: "84 months", value: "84" },
            ].map((termOption) => {
              const interest = findMatchingInterest(
                termOption.value,
                interestDropdown
              );
              return {
                ...termOption,
                preapproved_pct: interest ? interest.interest_rate : "N/A",
                label: `${termOption.value} months - ${interest ? interest.interest_rate : "N/A"
                  }% APR`,
              };
            });
            setTermOptions(updatedTermOptions);

            const matchingObject = findMatchingInterest(term, interestDropdown);
            if (matchingObject) {
              const emi = calculateEmi(
                preAprvAmount,
                matchingObject.interest_rate,
                term
              );
              setMemberInfo((prevInfo) => ({
                ...prevInfo,
                preapproved_pct: matchingObject.interest_rate,
                calculate_emi: emi,
              }));
              setSavedMonthlyPayment(emi);
            }
          }
        });
      } catch (error) {
        console.error("Error fetching user data:", error);
      }
    };

    if (open) {
      fetchUserData();
    }

    // Include dispatch and navigate in the dependency array
  }, [dispatch, navigate, apiCallCompletedRef, token, open]);
  const findMatchingInterest = (term, interestArray) => {
    for (const element of interestArray) {
      if (term >= element.min_term && term <= element.max_term) {
        return element;
      }
    }
    return null;
  };

  const calculateEmi = (
    principal,
    interestRate,
    term,
    updatedDownPayment = null
  ) => {
    setCalculationLoader(true);
    let newPrincipal = 0;
    if (
      updatedDownPayment > 0 &&
      updatedDownPayment != null &&
      updatedDownPayment <= principal
    ) {
      newPrincipal = principal - updatedDownPayment;
    } else {
      newPrincipal = principal;
    }
    // Convert annual interest rate to decimal
    const rate = interestRate / 100;
    // Calculate total amount accrued using simple interest formula: A = P(1 + rt)
    const totalAmount = newPrincipal * (1 + rate * (term / 12));
    // Calculate monthly payment
    const emi = totalAmount / term;
    setCalculationLoader(false);
    return parseFloat(emi.toFixed(2)); // Ensure two decimal places
  };

  const handleTermChange = (event, newValue) => {
    setNewDownPayment(0);
    setMonthlyPaymentErrorMessage("");
    const numericValue = Number(inputValue.replace(/,/g, ""));
    setFinanceAmount(numericValue);
    setAdjustedPrincipal(numericValue);
    const value = newValue.value;
    if (inputValue != 0) {
      const selectedInterest = findMatchingInterest(value, interestDropdown);

      if (selectedInterest) {
        const emi = calculateEmi(
          parseFloat(inputValue.replace(/,/g, "")) ||
          parseFloat(preAprvAmount.replace(/,/g, "")),
          selectedInterest.interest_rate,
          value
        );
        setMemberInfo((prevInfo) => ({
          ...prevInfo,
          preapproved_pct: selectedInterest.interest_rate,
          calculate_emi: emi.toLocaleString(),
        }));
        setTerm(value);
        setSavedMonthlyPayment(emi);
      }
    }
  };

  const handleDownPaymentChange = (event) => {
    setIsDownPaymentChanged(true);
    setIsMonthlyPaymentChanged(false);
    setMonthlyPaymentErrorMessage("");

    const input = event.target;
    let value = input.value.trim().replace(/,/g, ""); // Remove all commas
    // If input is empty, reset values and return early
    if (value === "") {
      setDownpaymentValue("");
      setNewDownPayment("");
      setDownPaymentErrorMessage(""); // Clear any error message
      updateValues(financeAmount, 0); // Ensure recalculations reset to zero
      setAdjustedPrincipal(0);
      return;
    }

    // ✅ Allow only numbers (0-9) and a single decimal point (.)
    if (!/^\d*\.?\d*$/.test(value)) {
      setDownPaymentErrorMessage(
        "Only numbers (0-9) and a single decimal point are allowed."
      );
      return;
    }

    // Remove non-numeric characters except decimal point
    value = value.replace(/[^0-9.]/g, "");

    // Ensure only one decimal point is present
    if ((value.match(/\./g) || []).length > 1) {
      setDownPaymentErrorMessage("Invalid input. Please enter a valid amount.");
      return;
    }

    // Validate input using utility function
    if (!validateInput(value).isValid) {
      setDownPaymentErrorMessage(
        "Amount cannot exceed 15 digits, including up to 2 decimals."
      );
      return;
    }

    // Convert to a valid number while keeping decimals
    const numericValue = parseFloat(value) || 0;
    if (isNaN(numericValue)) {
      setDownPaymentErrorMessage("Invalid input. Please enter a valid amount.");
      return;
    }

    // Get loan approval amount for comparison
    const loanApprovalAmount = parseFloat(inputValue.replace(/,/g, "")) || 0;

    if (numericValue > loanApprovalAmount) {
      setDownPaymentErrorMessage(
        "Down payment cannot be greater than Your Loan Approval Amount."
      );
      setDownpaymentValue(value);
    }

    setDownPaymentErrorMessage(""); // Clear error message

    const financeNumericValue = financeAmount
      ? parseFloat(String(financeAmount).replace(/,/g, "")) || 0
      : 0;

    setDownpaymentValue(value);
    setNewDownPayment(value);
    updateValues(
      financeNumericValue !== 0 ? financeNumericValue : inputValue,
      numericValue
    );
  };

  const handleMonthlyPaymentChange = (e) => {
    let inputElement = e.target.value;

    // Remove non-numeric characters except decimal point
    inputElement = inputElement.replace(/[^0-9.]/g, "");

    // Prevent multiple decimal points
    const decimalCount = (inputElement.match(/\./g) || []).length;
    if (decimalCount > 1) return;

    // Restrict to 2 decimal places
    if (inputElement.includes(".")) {
      const [integerPart, decimalPart] = inputElement.split(".");
      if (decimalPart.length > 2) {
        inputElement = `${integerPart}.${decimalPart.slice(0, 2)}`;
      }
    }

    // Handle case when input is empty (allows deletion)
    if (inputElement === "") {
      setMemberInfo((prevInfo) => ({
        ...prevInfo,
        calculate_emi: "", // Allow clearing input
      }));
      setMonthlyPaymentErrorMessage(""); // Clear error when input is empty
      setNewDownPayment(0);
      return;
    }

    // Convert savedMonthlyPayment to a float value
    const savedMonthlyPaymentDecimalVal = parseFloat(
      savedMonthlyPayment?.toFixed(2) || "0.00"
    );

    // Ensure valid numeric input before parsing
    if (inputElement === "." || isNaN(parseFloat(inputElement))) {
      setMonthlyPaymentErrorMessage("");
      return;
    }
    // Convert input to float for validation
    const inputValue = parseFloat(inputElement);

    // Validate input against savedMonthlyPayment
    if (inputValue > savedMonthlyPaymentDecimalVal) {
      setMonthlyPaymentErrorMessage(
        `You cannot enter a monthly payment greater than $${savedMonthlyPaymentDecimalVal}`
      );
      return; // Stop execution here, preventing invalid state update
    }

    // If valid, update state and clear error
    setIsMonthlyPaymentChanged(true);
    setMemberInfo((prevInfo) => ({
      ...prevInfo,
      calculate_emi: inputElement, // Store as string
    }));
    setMonthlyPaymentErrorMessage("");
  };

  const getNewDownPayment = (totalAmountAccrued, term) => {
    if (!isMonthlyPaymentChanged) {
      setNewDownPayment("");
      return false;
    }

    const userMonthlyPayment = memberInfo.calculate_emi;
    const annualInterestRate = memberInfo.preapproved_pct || 0;
    const numericLoanApprovalValue = Number(
      (
        (adjustedPrincipal && adjustedPrincipal !== 0
          ? adjustedPrincipal
          : financeAmount) || "0"
      )
        .toString()
        .replace(/,/g, "")
    );
    // Check for empty/null/undefined values
    if (
      totalAmountAccrued === "" ||
      totalAmountAccrued == null ||
      userMonthlyPayment === "" ||
      userMonthlyPayment == null ||
      term === "" ||
      term == null
    ) {
      setNewDownPayment("");
      return "";
    }

    // Step 1: Calculate Interest Factor
    const interestFactor = 1 + (annualInterestRate / 100) * (term / 12);
    // Step 2: Multiply Monthly Payment by Loan Term
    const totalPayment = userMonthlyPayment * term;
    // Step 3: Divide by Interest Factor
    const adjustedPayment = totalPayment / interestFactor;
    // Step 4: Subtract from Loan Approval Value (Final Down Payment Calculation)
    const requiredDownPayment = numericLoanApprovalValue - adjustedPayment;
    // Set the final calculated down payment
    setNewDownPayment(requiredDownPayment.toFixed(2));
  };

  const getTotalAccuredAmount = (adjustedPrincipal, term) => {
    if (adjustedPrincipal == 0) {
      const numericFinanceAmount =
        parseFloat(financeAmount.toString().replace(/,/g, "")) || 0;
      adjustedPrincipal = numericFinanceAmount;
    }
    const annualRateNew = (memberInfo.preapproved_pct || 0) / 100;
    let newPrincipal = 0;
    if (annualRateNew > 0 && term > 0) {
      if (newDownPayment > 0 && !isMonthlyPaymentChanged) {
        // Ensure financeAmount is a valid number
        const numericFinanceAmount =
          parseFloat(financeAmount.toString().replace(/,/g, "")) || 0;
        newPrincipal = Math.max(0, numericFinanceAmount - newDownPayment); // Subtract correct value
      } else {
        newPrincipal = adjustedPrincipal;
      }
      const totalAmountAccrued =
        newPrincipal * (1 + annualRateNew * (term / 12));
      setTotalAmountAccured(totalAmountAccrued);
      if (
        totalAmountAccrued > 0 &&
        term > 0 &&
        memberInfo.calculate_emi > 0 &&
        isMonthlyPaymentChanged
      ) {
        getNewDownPayment(totalAmountAccrued, term);
      }

      if (isDownPaymentChanged && !isMonthlyPaymentChanged) {
        const newMonthlyPaymentVal = totalAmountAccrued / term;
        if (newMonthlyPaymentVal != "" && newMonthlyPaymentVal != null) {
          setMemberInfo((prevInfo) => ({
            ...prevInfo,
            calculate_emi: newMonthlyPaymentVal.toFixed(2), // Allow editing
          }));
          setSavedMonthlyPayment(newMonthlyPaymentVal);
        }
      }
    }
  };
  useEffect(() => {
    getTotalAccuredAmount(adjustedPrincipal, term);
  }, [
    adjustedPrincipal,
    term,
    memberInfo.preapproved_pct,
    memberInfo.calculate_emi,
    isMonthlyPaymentChanged, // Add this to ensure recalculation
  ]);
  const updateValues = (purchaseAmount, updatedDownPayment) => {
    if (purchaseAmount === 0 || purchaseAmount === undefined) {
      purchaseAmount = inputValue;
    }
    const numericPurchaseAmount =
      parseFloat(purchaseAmount.toString().replace(/,/g, "")) || 0;
    const numericDownPayment =
      parseFloat(updatedDownPayment.toString().replace(/,/g, "")) || 0;
    if (numericPurchaseAmount !== 0) {
      const emi = calculateEmi(
        numericPurchaseAmount,
        memberInfo.preapproved_pct,
        term,
        updatedDownPayment
      );

      // Format EMI
      const formattedEmi = emi.toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
      setMemberInfo((prevInfo) => {
        const newInfo = {
          ...prevInfo,
          calculate_emi: formattedEmi,
        };
        return newInfo;
      });
      setSavedMonthlyPayment(emi);
    }
  };

  const handleOpenShareModal = (car) => {
    setSharedVehicle(car);
    setShowShareModal(true);
    setOpened(false);
  };

  const handleCloseShareModal = () => {
    setShowShareModal(false);
    setSharedVehicle(null);
    setOpened(true);
  };

  // When user clicks the share button:
  const handleShareClick = () => {
    setShowShareModal(true);
    setSharedVehicle(selectedCar);
  };

  // When user confirms in the modal:
  const handleConfirmShare = () => {
    if (onShareFastpass && sharedVehicle) {
      onShareFastpass(sharedVehicle, shareMessage);
      setShowShareModal(false);
    }
  };

  return (
    <Modal
      show={open === 1 && open !== 2}
      onHide={onClose}
      onClose={onClose}
      centered
      size="lg"
    >
      <Modal.Header
        className="modal-header-fixed"
        style={{ justifyContent: "center" }}
        closeButton
      >
        <Modal.Title>Deal Sheet</Modal.Title>
      </Modal.Header>
      <Modal.Body className="modal-body-scrollable">
        {vehicleDetails ? (
          <>
            <Paper
              sx={{
                p: 2,
                margin: "auto",
                maxWidth: "100%",
                flexGrow: 1,
                backgroundColor: (theme) =>
                  theme.palette.mode === "dark" ? "#1A2027" : "#fff",
              }}
            >
              <Grid container spacing={2}>
                <>
                  <Grid item xs={12} md={4} className="align-self-center">
                    <ButtonBase sx={{ width: "100%", height: "auto" }}>
                      <Img
                        alt={selectedCar.heading || ""}
                        src={selectedCar.photo_url || fallbackImg}
                        onError={(e) => {
                          e.target.src = fallbackImg;
                        }}
                        sx={{
                          "&:hover": {
                            transform: "scale(1.2)", // Adjust the scale value for desired zoom
                          },
                        }}
                      />
                    </ButtonBase>
                  </Grid>
                  <Grid item xs={12} md={8}>
                    {selectedCar.year && (
                      <Typography
                        gutterBottom
                        variant="subtitle2"
                        component="div"
                        sx={{ color: "#4891FF" }}
                      >
                        <b>{selectedCar.year || ""}</b>
                      </Typography>
                    )}
                    <Typography gutterBottom variant="h6" component="div">
                      <b>{selectedCar.heading}</b>
                    </Typography>
                    {selectedCar.seller_name && (
                      <Typography
                        gutterBottom
                        variant="subtitle2"
                        component="div"
                      >
                        from dealer{" "}
                        <b>
                          <i>{selectedCar.seller_name.toUpperCase() || ""}</i>
                        </b>
                      </Typography>
                    )}
                    <Typography variant="body2">
                      {selectedCar.model || ""} {selectedCar.version || ""}{" "}
                      {selectedCar.engine_block || selectedCar.engine || ""}{" "}
                      {selectedCar.drivetrain || ""}
                    </Typography>
                    <Typography variant="subtitle1" component="div">
                      VIN: {selectedCar.vin}
                      <Tooltip title={copied ? "Copied!" : "Copy to clipboard"}>
                        <IconButton
                          onClick={handleCopyToClipboard}
                          style={{ marginLeft: 8 }}
                        >
                          <ContentCopyIcon style={{ color: "#4891FF" }} />
                        </IconButton>
                      </Tooltip>
                    </Typography>
                    <Typography variant="subtitle1" component="div">
                      {selectedCar.price != null ? (
                        <b>$ {selectedCar.price.toLocaleString()}</b>
                      ) : (
                        " "
                      )}
                    </Typography>
                    <Button
                      onClick={() => viewDetails(selectedCar)}
                      className="view-more-details mb-2"
                    >
                      {loadingDetails === selectedCar.vin ? (
                        <CircularProgress size={20} sx={{ ml: 1, mt: 1 }} />
                      ) : (
                        "View More Details"
                      )}
                    </Button>
                    <div className="button-container-deal" style={{ display: 'flex', gap: '8px', justifyContent: isTabletOrMobile ? 'center' : 'start' }}>
                      <Button
                        variant="contained"
                        className="garage-deal"
                        onClick={() => handleTestDriveModal(selectedCar)}
                      >
                        <span
                          className="d-flex align-items-center"
                          style={{ gap: "8px" }}
                        >
                          {openedTestDrive && testDriveLoadingDeal === selectedCar.vin ? (
                            <CircularProgress size={22} className="text-white" />
                          ) : (
                            <svg
                              width="22"
                              height="22"
                              viewBox="0 0 48 48"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                fillRule="evenodd"
                                clipRule="evenodd"
                                d="M47.374 23.687C47.374 36.7689 36.7689 47.374 23.687 47.374C10.605 47.374 0 36.7689 0 23.687C0 10.605 10.605 0 23.687 0C36.7689 0 47.374 10.605 47.374 23.687ZM20.7561 42.4113C19.1956 38.5811 14.7351 28.2039 11.8435 27.24C9.82102 26.5659 7.03108 26.6592 5.00268 26.8647C6.35369 34.8663 12.7199 41.1636 20.7561 42.4113ZM5.51775 18.2875C7.84263 10.4525 15.0974 4.7374 23.687 4.7374C32.2766 4.7374 39.5313 10.4525 41.8562 18.2875C38.1165 17.5867 31.4046 16.5809 23.687 16.5809C15.9693 16.5809 9.25747 17.5867 5.51775 18.2875ZM42.3713 26.8647C40.3429 26.6592 37.5529 26.5659 35.5305 27.24C32.6388 28.2039 28.1783 38.5811 26.6178 42.4113C34.654 41.1636 41.0203 34.8663 42.3713 26.8647Z"
                                fill="white"
                              />
                            </svg>
                          )}
                          Test Drive
                        </span>
                      </Button>

                      <Button
                        variant="contained"
                        className="garage-dealer-btn"
                        style={{ marginLeft: isSmallMobile ? "0%" : "1%" }}
                        onClick={() => handleAskDealerModal(selectedCar)}
                      >
                        <span
                          className="d-flex align-items-center"
                          style={{ gap: "8px" }}
                        >
                          {!openedTestDrive && askDealerLoadingDeal === selectedCar.vin ? (
                            <CircularProgress size={22} className="text-white" />
                          ) : (
                            <Icon
                              icon="calendar"
                              size={22}
                              className="text-white"
                            />
                          )}
                          Ask Dealer
                        </span>
                      </Button>
                      {/* Share FastPass Button */}
                      {selectedCar && selectedCar.is_negotiation === 2 && !selectedCar.is_delete_mc && (
                        <div style={{ display: 'flex', flexDirection: 'column', alignItems: isTabletOrMobile ? 'center' : 'start', justifyContent:'center', width: 70 }}>
                          <div style={{ position: 'relative' }}>
                            {shareFpLoading === selectedCar.vin ? (
                              <CircularProgress size={40} sx={{ color: "#4891ff" }} />
                            ) : (
                              <img
                                onClick={handleShareClick}
                                src={ImageBaseURL + "fp_share_logo_transparent.png"}
                                width={50}
                                height={50}
                                alt="header"
                                style={{ objectFit: "contain", cursor: 'pointer' }}
                              />
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </Grid>
                </>
              </Grid>
            </Paper>
            <Paper
              sx={{
                p: 2,
                margin: "auto",
                maxWidth: "100%",
                flexGrow: 1,
                backgroundColor: (theme) =>
                  theme.palette.mode === "dark" ? "#1A2027" : "#fff",
              }}
              style={{ marginTop: "10px" }}
            >
              <Grid container spacing={2}>
                <Grid item xs={12} md={6} style={{ textAlign: "center" }}>
                  <Item className="page_bg">
                    <CustomTextFieldZip
                      fullWidth
                      id="outlined-basic"
                      label="Out the Door Price"
                      variant="outlined"
                      autoComplete="off"
                      value={calculationLoader ? "" : inputValue}
                      InputProps={{
                        sx: {
                          fontSize: "16px",
                          fontWeight: "semibold",
                          backgroundColor: "#f5f5f5", // Light gray background
                          color: "#000 !important", // Force black text color
                          cursor: "not-allowed", // Show not-allowed cursor
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#d3d3d3", // Light gray border
                          },
                          "& .MuiInputBase-input.Mui-disabled": {
                            color: "#000 !important", // Force black text for disabled input
                            WebkitTextFillColor: "#000 !important", // Fix for Webkit browsers (Chrome, Safari)
                            opacity: 1, // Ensure full visibility
                          },
                          readOnly: true,
                          sx: {
                            cursor: "default", // Prevents text selection cursor
                            caretColor: "transparent", // ✅ Hides blinking cursor
                          },
                        },
                        startAdornment: !calculationLoader ? (
                          <InputAdornment position="start">$</InputAdornment>
                        ) : null,
                        endAdornment: calculationLoader ? (
                          <InputAdornment position="end">
                            <CircularProgress
                              sx={{ color: "#4891FF" }}
                              size={20}
                            />
                          </InputAdornment>
                        ) : null,
                      }}
                    />
                  </Item>
                </Grid>

                <Grid item xs={12} md={6} style={{ textAlign: "center" }}>
                  <Item className="page_bg">
                    <CustomAutocomplete
                      options={termOptions}
                      sx={{
                        background: "#ffffff",
                      }}
                      value={
                        calculationLoader
                          ? null // Hide value while loading
                          : termOptions.find(
                            (option) => option.value == term
                          ) || null
                      }
                      onChange={handleTermChange}
                      getOptionLabel={(option) => option.label}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="What Term Are You Interested In?"
                          InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                              <>
                                {params.InputProps.endAdornment}
                                {calculationLoader && (
                                  <CircularProgress
                                    sx={{ color: "#4891FF" }}
                                    size={20}
                                  />
                                )}
                              </>
                            ),
                          }}
                          InputLabelProps={{
                            sx: {
                              color: "#4891FF",
                            },
                          }}
                        />
                      )}
                    />
                  </Item>
                </Grid>

                <Grid item xs={12} md={6} style={{ textAlign: "center" }}>
                  <Item className="page_bg">
                    <CustomTextField
                      fullWidth
                      id="outlined-basic"
                      label="Your Monthly Payment"
                      variant="outlined"
                      onChange={handleMonthlyPaymentChange}
                      sx={{
                        background: "#ffffff",
                      }}
                      autoComplete="off"
                      value={calculationLoader ? "" : memberInfo.calculate_emi} // Hide value while loading
                      InputProps={{
                        startAdornment: !calculationLoader ? (
                          <InputAdornment position="start">$</InputAdornment>
                        ) : null,
                        endAdornment: calculationLoader ? (
                          <InputAdornment position="end">
                            <CircularProgress
                              sx={{ color: "#4891FF" }}
                              size={20}
                            />
                          </InputAdornment>
                        ) : null,
                      }}
                    />
                    {monthlyPaymentErrorMessage && (
                      <div
                        style={{
                          color: "red",
                          fontSize: "14px",
                          marginTop: "8px",
                        }}
                      >
                        {monthlyPaymentErrorMessage}
                      </div>
                    )}
                  </Item>
                </Grid>

                <Grid item xs={12} md={6} style={{ textAlign: "center" }}>
                  <Item className="page_bg">
                    <CustomTextFieldZip
                      fullWidth
                      id="outlined-basic"
                      value={newDownPayment}
                      onChange={handleDownPaymentChange}
                      label="Your Down Payment"
                      variant="outlined"
                      autoComplete="off"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">$</InputAdornment>
                        ),
                      }}
                    />
                    {downPaymentErrorMessage && (
                      <div
                        style={{
                          color: "red",
                          fontSize: "14px",
                          marginTop: "8px",
                        }}
                      >
                        {downPaymentErrorMessage}
                      </div>
                    )}
                  </Item>
                </Grid>
              </Grid>
            </Paper>
          </>
        ) : (
          <CircularProgress sx={{ marginLeft: "45%" }} />
        )}
      </Modal.Body>
      <Modal.Footer className="modal-footer-fixed">
        <Button
          variant="btn btn-danger"
          onClick={onClose}
          className="fw-600"
          style={{ width: "25%" }}
        >
          CLOSE
        </Button>
      </Modal.Footer>
      {/* Share FastPass Confirmation Modal */}
      <FpShareConfirmationModal
        show={showShareModal}
        onHide={handleCloseShareModal}
        sharedVehicle={sharedVehicle}
        handleDealerChatOpenModal={handleConfirmShare}
      />
    </Modal>
  );
};

DealSheetModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  vehicleDetails: PropTypes.shape({
    trim: PropTypes.string,
    engine: PropTypes.string,
    drivetrain: PropTypes.string,
  }),
  selectedCar: PropTypes.string,
  loadingDetails: PropTypes.bool,
  handleFetchVehicleDetails: PropTypes.func,
  handleDealerChatOpenModal: PropTypes.func,
  askDealerLoadingDeal: PropTypes.bool,
  testDriveLoadingDeal: PropTypes.bool,
  setOpened: PropTypes.func,
  openedTestDrive: PropTypes.bool,
  setOpenedTestDrive: PropTypes.func,
  memberInfo: PropTypes.object,
  setMemberInfo: PropTypes.func,
  downpaymentValue: PropTypes.string,
  setDownpaymentValue: PropTypes.func,
  monthlyPaymentErrorMessage: PropTypes.string,
  downPaymentErrorMessage: PropTypes.string,
  setDownPaymentErrorMessage: PropTypes.func,
  setMonthlyPaymentErrorMessage: PropTypes.func,
  onShareFastpass: PropTypes.func,
  shareFpLoading: PropTypes.bool,
};

export default DealSheetModal;
