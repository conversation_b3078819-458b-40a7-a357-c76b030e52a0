import { Button, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle } from '@mui/material';
import PropTypes from "prop-types";
import { useMediaQuery } from 'react-responsive';

const DeletionConfirmationDialog = ({ open, onClose, onConfirm, carToDelete }) => {
    const isMobile = useMediaQuery({ maxWidth: 767 });

    return (
        <Dialog
            open={open}
            onClose={onClose}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
            fullWidth={true}  // Makes dialog full width on mobile
            maxWidth="sm"     // Limits the dialog's width
        >
            <DialogTitle id="alert-dialog-title" sx={{ textAlign: isMobile ? 'center' : 'left' }}>
                {"Confirm Deletion"}
            </DialogTitle>
            <DialogContent>
                {carToDelete?.is_negotiation === 0 ? (
                    <DialogContentText id="alert-dialog-description" sx={{ fontSize: isMobile ? '14px' : '16px' }}>
                        Are you sure you want to delete <span style={{ fontWeight: "600" }}>{carToDelete?.heading || "this vehicle"}</span> ?
                    </DialogContentText>
                ) : (
                    <DialogContentText id="alert-dialog-description" sx={{ fontSize: isMobile ? '14px' : '16px' }}>
                        Removing the vehicle from the garage will stop all chats and remove it from your options. Do you wish to proceed?
                    </DialogContentText>
                )}
            </DialogContent>
            <DialogActions sx={{ flexDirection: isMobile ? 'column' : 'row', alignItems: 'center' }}>
                <Button
                    onClick={onClose}
                    sx={{
                        marginBottom: isMobile ? '10px' : '0',
                        width: isMobile ? '100%' : 'auto',
                        textAlign: 'center',
                    }}
                >
                    {carToDelete?.is_negotiation === 0 ? "Cancel" : "Return to list"}
                </Button>
                <Button
                    onClick={onConfirm}
                    autoFocus
                    sx={{
                        color: "red",
                        width: isMobile ? '100%' : 'auto',
                        textAlign: 'center',
                    }}
                    style={{ marginLeft: isMobile ? '0' : '5px' }}
                >
                    {carToDelete?.is_negotiation === 0 ? "Delete" : "Remove from my garage"}
                </Button>
            </DialogActions>
        </Dialog>
    );
};

DeletionConfirmationDialog.propTypes = {
    open: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    onConfirm: PropTypes.func.isRequired,
    carToDelete: PropTypes.object.isRequired, // Changed to object since carToDelete should be an object
};

export default DeletionConfirmationDialog;
