import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import PropTypes from "prop-types";
import { FaCheck, FaTimes } from "react-icons/fa";
import { Typography } from "@mui/material";
import useGarageListApi from "../api/fetchGarageApi";

const FpShareConfirmationModal = ({ show, onHide, sharedVehicle, handleDealerChatOpenModal }) => {

    const { shareMessage } = useGarageListApi(); // get the share message from the api
    const handleReject = () => {
        onHide();
    };

    const handleAccept = async () => {
        await handleDealerChatOpenModal(sharedVehicle, shareMessage);
        onHide();
    };

    return (
        <Modal show={show} onHide={onHide} centered>
            <Modal.Header className="conf-modal-header-fixed"></Modal.Header>
            <Modal.Body className="conf-modal-body-scrollable">
                <div className="conf-modal-question">
                    Do you want to share your FastPass with the Dealer? This will provide the dealer your loan details to write up the Purchase Agreement.
                </div>
                <div className="conf-modal-buttons">
                    <Button
                        className="custom-button no-button fp-share-modal-no-btn"
                        onClick={handleReject}
                    >
                        <FaTimes className="conf-modal-icon" /> No
                    </Button>
                    <Button
                        className="custom-button yes-button fp-share-modal-yes-btn"
                        onClick={handleAccept}
                    >
                        <FaCheck className="conf-modal-icon" /> Yes
                    </Button>
                </div>
            </Modal.Body>
        </Modal>
    );
};

FpShareConfirmationModal.propTypes = {
    show: PropTypes.bool.isRequired,
    onHide: PropTypes.func.isRequired,
    sharedVehicle: PropTypes.object,
    handleDealerChatOpenModal: PropTypes.func.isRequired,
};

export default FpShareConfirmationModal; 