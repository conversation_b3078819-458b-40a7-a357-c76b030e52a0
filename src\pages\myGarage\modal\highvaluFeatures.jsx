import PropTypes from "prop-types";
import {
  Typography,
  CircularProgress,
} from "@mui/material";
import { Modal, Button } from "react-bootstrap";
import { useEffect, useState } from "react";
import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import ButtonBase from "@mui/material/ButtonBase";
import { ImageBaseURL } from "../../../config";

const Img = styled("img")({
  margin: "auto",
  display: "block",
  maxWidth: "100%",
});

const HighValueFeaturessModal = ({ highValueopen, onClose, vehicleDetails, selectedCar }) => {
  const [features, setFeatures] = useState("Loading...");
  const [showAll, setShowAll] = useState(false);
  const fallbackImg = `${ImageBaseURL}fallback_image.png`;
  useEffect(() => {
    if (vehicleDetails && vehicleDetails.high_value_features) {
      setFeatures(vehicleDetails.high_value_features);
    } else {
      setFeatures("No Information Available");
    }
  }, [vehicleDetails]);

  let initialFeatures = [];
  let remainingFeatures = [];

  if (features !== "Loading..." && features !== "No Information Available") {
    const featureArray = features.split("|");
    initialFeatures = featureArray.slice(0, 9);
    remainingFeatures = featureArray.slice(9);
  }

  const handleShowMore = () => {
    setShowAll(!showAll);
  };

  return (
    <Modal show={highValueopen} onHide={onClose} onClose={onClose} centered size="lg">
      <Modal.Header className="modal-header-fixed" style={{ justifyContent: "center" }} closeButton>
        <Modal.Title>Detailed Vehicle Feature View</Modal.Title>
      </Modal.Header>
      <Modal.Body className="modal-body-scrollable">
        {vehicleDetails ? (
          <>
            <Paper
              sx={{
                p: 2,
                margin: "auto",
                maxWidth: "100%",
                flexGrow: 1,
                backgroundColor: (theme) =>
                  theme.palette.mode === "dark" ? "#1A2027" : "#fff",
              }}
            >
              <Grid container spacing={2}>
                <Grid item xs={12} md={4} style={{ textAlign: "center" }}>
                  <ButtonBase sx={{ width: "100%", height: "auto" }}>
                    <Img
                      alt={selectedCar.heading}
                      src={selectedCar.photo_url || fallbackImg}
                      onError={(e) => {
                        e.target.src = fallbackImg;
                      }}
                    />
                  </ButtonBase>
                </Grid>
                <Grid item xs={12} md={8}>
                  <Typography gutterBottom variant="h6" component="div">
                    <b>{selectedCar.heading}</b>
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    {selectedCar.model || ""} {selectedCar.version || ""}{" "}
                    {selectedCar.engine_block || selectedCar.engine || ""}{" "}
                    {selectedCar.drivetrain || ""}
                  </Typography>
                  <Typography gutterBottom variant="subtitle1" component="div">
                    {selectedCar.price != null ? <b>$ {selectedCar.price.toLocaleString()}</b> : " "}
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
            <Paper
              className="mt-3"
              sx={{
                p: 2,
                margin: "auto",
                maxWidth: "100%",
                flexGrow: 1,
                backgroundColor: (theme) =>
                  theme.palette.mode === "dark" ? "#1A2027" : "#fff",
              }}
            >
              <Typography variant="h6" component="div" align="center">
                <b>
                  <u>High Value Features</u>
                </b>
              </Typography>
              <Grid container spacing={2} className="mt-2">
                <Grid item xs={12}>
                  <div
                    style={{
                      display: "flex",
                      flexWrap: "wrap",
                      justifyContent: "space-between",
                      wordBreak: "break-word",
                      lineHeight: "1",
                    }}
                  >
                    {initialFeatures
                      .filter((feature) => feature)
                      .map((feature) => (
                        <div key={feature} style={{ width: "45%" }}>
                          <ul>
                            <li>{feature}</li>
                          </ul>
                        </div>
                      ))}
                    {showAll &&
                      remainingFeatures
                        .filter((feature) => feature)
                        .map((feature) => (
                          <div key={feature} style={{ width: "45%" }}>
                            <ul>
                              <li>{feature}</li>
                            </ul>
                          </div>
                        ))}
                  </div>
                </Grid>
                {remainingFeatures.length > 0 && (
                  <Grid item xs={12} style={{ textAlign: "center" }}>
                    <Button
                      onClick={handleShowMore}
                      className="map-text"
                      style={{
                        background: "transparent",
                        border: "none",
                        color: "#4891FF",
                        fontWeight: "600",
                      }}
                    >
                      {showAll ? "View Less High Value Features" : "View All High Value Features"}
                    </Button>
                  </Grid>
                )}
              </Grid>
            </Paper>
          </>
        ) : (
          <CircularProgress />
        )}
      </Modal.Body>
      <Modal.Footer className="modal-footer-fixed">
        <Button
          variant="btn btn-danger"
          onClick={onClose}
          className="fw-600"
          style={{ width: "25%" }}
        >
          CLOSE
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

HighValueFeaturessModal.propTypes = {
  highValueopen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  selectedCar: PropTypes.object.isRequired,
  vehicleDetails: PropTypes.shape({
    trim: PropTypes.string,
    exterior_color: PropTypes.string,
    interior_color: PropTypes.string,
    engine: PropTypes.string,
    drivetrain: PropTypes.string,
    high_value_features: PropTypes.string,
    price: PropTypes.number,
    miles: PropTypes.number,
    inventory_type: PropTypes.string,
    heading: PropTypes.string,
  }).isRequired,
};

export default HighValueFeaturessModal;
