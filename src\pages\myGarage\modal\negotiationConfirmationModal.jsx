import { Button, Dialog, <PERSON>alogActions, DialogContent, DialogContentText, DialogTitle } from '@mui/material';
import PropTypes from "prop-types";
import { useMediaQuery } from 'react-responsive';

const NegotiationConfirmationModal = ({ open, onClose, onConfirm, selectedCars, updatedSelectedCarsRef, checkedRow }) => {
    const isMobile = useMediaQuery({ maxWidth: 767 });
    return (
        <Dialog
            open={open}
            onClose={onClose}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
            fullWidth
            maxWidth="sm"
        >
            <DialogTitle id="alert-dialog-title" sx={{ textAlign: isMobile ? 'center' : 'left' }}>
                {"Confirm AI Negotiation"}
            </DialogTitle>
            <DialogContent>
                <DialogContentText id="alert-dialog-description" sx={{ fontSize: isMobile ? '14px' : '16px' }}>
                    {(() => {
                        const selectedVehicles = (checkedRow ? selectedCars : updatedSelectedCarsRef.current) || [];
                        const vehicleNames = selectedVehicles.map(car => car.heading);

                        let formattedText = '';

                        if (vehicleNames.length === 1) {
                            formattedText = vehicleNames[0];
                        } else if (vehicleNames.length === 2) {
                            formattedText = `${vehicleNames[0]} & ${vehicleNames[1]}`;
                        } else if (vehicleNames.length > 2) {
                            formattedText = `${vehicleNames.slice(0, -1).join(', ')} & ${vehicleNames[vehicleNames.length - 1]}`;
                        }

                        return `AI Negotiation will start for ${formattedText}. Do you wish to proceed?`;
                    })()}
                </DialogContentText>
            </DialogContent>
            <DialogActions sx={{ flexDirection: isMobile ? 'column' : 'row', alignItems: 'center' }}>
                <Button
                    onClick={onClose}
                    sx={{
                        marginBottom: isMobile ? '10px' : '0',
                        width: isMobile ? '100%' : 'auto',
                        textAlign: 'center',
                    }}
                >
                    Cancel
                </Button>
                <Button
                    onClick={onConfirm}
                    autoFocus
                    className='text-success'
                    sx={{
                        width: isMobile ? '100%' : 'auto',
                        textAlign: 'center',
                    }}
                    style={{ marginLeft: isMobile ? '0' : '5px' }}
                >
                    Confirm
                </Button>
            </DialogActions>
        </Dialog>
    );
};

NegotiationConfirmationModal.propTypes = {
    open: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    onConfirm: PropTypes.func.isRequired,
    selectedCars: PropTypes.arrayOf(
        PropTypes.shape({
            heading: PropTypes.string.isRequired
        })
    ).isRequired,
    updatedSelectedCarsRef: PropTypes.object.isRequired,
    checkedRow: PropTypes.bool.isRequired,
};

export default NegotiationConfirmationModal;
