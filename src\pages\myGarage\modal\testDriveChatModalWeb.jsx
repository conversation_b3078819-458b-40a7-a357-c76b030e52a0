import PropTypes from "prop-types";
import { Box, Typography, TextField, CircularProgress } from "@mui/material";
import { Modal } from "react-bootstrap";
import { useEffect, useState, useRef } from "react";
import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import { ImageBaseURL } from "../../../config";
import SendIcon from "@mui/icons-material/Send";
import useGarageListApi from "../api/fetchGarageApi";
import { toast } from "react-toastify";
import dayjs from 'dayjs';
import { DemoContainer } from '@mui/x-date-pickers/internals/demo';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const labelStyle = {
  color: "#4891FF",
};

const CustomTextField = styled(TextField)({
  "& .MuiInputLabel-root": labelStyle, // Apply label style
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "bold", // Bold the input field value
    },
  },
});

const ChatMessage = styled(Paper)(({ theme, alignRight }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  maxWidth: "60%",
  marginBottom: theme.spacing(2),
  backgroundColor: alignRight ? "#4891FF" : "#61C454",
  color: "#fff",
  textAlign: "left",
  alignSelf: alignRight ? "flex-end" : "flex-start",
  borderRadius: alignRight
    ? "10px 0px 10px 10px"  // Blue: Rounded top-right
    : "0px 10px 10px 10px", // Green: Rounded top-left
  position: "relative",
  display: "flex",         // Use flexbox for layout
  flexDirection: "column", // Stack text and timestamp vertically
  alignItems: alignRight ? "flex-end" : "flex-start",
  wordBreak: "break-word", // Ensure long words break and don't overflow
}));


const Timestamp = styled(Typography)(({ theme }) => ({
  fontSize: "0.75rem",
  color: "#ccc",
  marginTop: theme.spacing(0.5), // Add some space between text and timestamp
}));


const TestDriveChatModalWeb = ({ open, onClose, selectedCar, sfmessages, formatTimestamp, refreshMessages  }) => {
  const [userRequestMsg, setUserRequestMsg] = useState("");
  const [userMsgSendLoader, setUserMsgSendLoader] = useState(null);
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hello! How can I assist you today?",
      fromDealer: true
    },
  ]);

  const truncateText = (text, maxLength) => {
    if (text.length > maxLength) {
      return text.slice(0, maxLength) + "...";
    }
    return text;
  }; // Truncate text if necessary

  const { sendDealerRequestMsg } = useGarageListApi();

  const sendAskDealerMessage = async (cardetails, userMsg) => {
    if(!selectedDateTime) return;
    setUserMsgSendLoader(true);
    try {
      await sendDealerRequestMsg(cardetails, userMsg);
      setSelectedDateTime(""); // Clear the selected date and time
      toast.success(`Message Send Successfully. Waiting for dealer response...`);
      await refreshMessages(cardetails);
      setIsDatePicker(false); // Revert to text field after submission
      setUserMsgSendLoader(false);
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  useEffect(() => {
    const defaultMessage = {
      id: 1,
      text: "Hello! How can I assist you today?",
      fromDealer: true,
    };
    if (open) {
      const defaultMessage = {
        id: 1,
        text: "Hello! How can I assist you today?",
        fromDealer: true,
      };
      if (sfmessages && sfmessages.length > 0) {
        // Process API data
        const formattedMessages = sfmessages.map((msg) => ({
          id: msg.id,
          text: msg.text,
          fromDealer: msg.fromDealer,
          timestamp: msg.timestamp,
        }));

        // Combine default message with API messages and sort by timestamp
        const allMessages = [defaultMessage, ...formattedMessages].sort(
          (a, b) => new Date(a.timestamp) - new Date(b.timestamp)
        );

        setMessages(allMessages);
      } else {
        // Reset messages if no data is present
        setMessages([
          {
            id: 1,
            text: "Hello! How can I assist you today?",
            fromDealer: true,
          },
        ]);
      }
    }
  }, [sfmessages, open]);

  const messageEndRef = useRef(null);
  const scrollToBottom = () => {
    messageEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const [isDatePicker, setIsDatePicker] = useState(true);
  const [selectedDateTime, setSelectedDateTime] = useState("");
  const [value, setValue] = useState(dayjs(''));

  return (
    <Modal show={open} onHide={onClose} centered size="xl">
      <Modal.Header
        style={{ justifyContent: "center", alignItems: "baseline" }}
        closeButton
      >
        <Modal.Title>
          <Box sx={{ flexGrow: 1 }}>
            <Grid container spacing={2}>
              <Grid item md={4}>
                <Item
                  style={{
                    display: "flex",
                    justifyContent: "left",
                    alignItems: "center",
                  }}
                >
                  <img
                    src={ImageBaseURL + "fp_logo.png"}
                    alt="header"
                    style={{ maxWidth: "50%", maxHeight: "50%" }}
                  />
                </Item>
              </Grid>
              <Grid item md={6}>
                <Item
                  style={{
                    display: "flex",
                    justifyContent: "left",
                    alignItems: "center",
                  }}
                >
                  {selectedCar && (
                    <Typography
                      variant="h6"
                      gutterBottom
                      style={{ fontWeight: "bold" }}
                      title={selectedCar.seller_name.toUpperCase()}
                    >
                      {truncateText(selectedCar.seller_name.toUpperCase(), 30)}
                    </Typography>
                  )}
                </Item>
              </Grid>
            </Grid>
          </Box>
        </Modal.Title>
      </Modal.Header>
      <Modal.Body className="modal-body-scrollable" style={{ maxHeight: "60vh", overflowY: "auto" }}>
        <Paper
          sx={{
            maxWidth: "100%",
            flexGrow: 1,
            backgroundColor: (theme) =>
              theme.palette.mode === "dark" ? "#1A2027" : "#fff",
          }}
        >
          <Box
            sx={{ display: "flex", flexDirection: "column", padding: 2 }}>

            {selectedCar && messages.length > 0 &&
              messages.map((msg) => (
                <ChatMessage key={msg.id} alignRight={!msg.fromDealer}>
                  {msg.text}
                  <Timestamp alignRight={!msg.fromDealer}>
                    {formatTimestamp(msg.timestamp)}
                  </Timestamp>
                </ChatMessage>
              ))}
            <div ref={messageEndRef} />
          </Box>
        </Paper>
      </Modal.Body>
      <Modal.Footer className="modal-footer-fixed">
        <Box sx={{ flexGrow: 1 }}>
          <Grid container spacing={2}>
            <Grid item md={11}>
              <Item
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                {isDatePicker ? (
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography variant="body1" sx={{ marginRight: 1 }}>
                      Your test drive is scheduled for
                    </Typography>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DemoContainer components={['DateTimePicker', 'DateTimePicker']}>
                        <DateTimePicker
                          label="Select Date Time Slot"
                          value={value}
                          onChange={(newValue) => setValue(newValue)}
                        />
                      </DemoContainer>
                    </LocalizationProvider>
                  </Box>
                  ) : (
                    <CustomTextField
                      id="outlined-multiline-flexible"
                      label="Type Your Query Here..."
                      variant="outlined"
                      multiline
                      fullWidth
                      value={userRequestMsg}
                      onChange={(e) => setUserRequestMsg(e.target.value)}
                      sx={{
                        width: "100%",
                        background: "#ffffff",
                      }}
                      autoComplete="off"
                      InputProps={{
                        sx: {
                          fontSize: "16px",
                        },
                      }}
                    />
                  )}
              </Item>
            </Grid>
            <Grid
              item
              md={1}
              style={{
                display: "flex",
                justifyContent: "left",
                alignItems: "center",
              }}
              pl={2}
            >
              <Item>
                {userMsgSendLoader ? (
                  <CircularProgress
                    size={40}
                    sx={{ color: "#4891FF" }} // Customize color if needed
                  />
                ) : (
                  <SendIcon
                    sx={{ color: "#4891FF", cursor: "pointer", fontSize: "40px" }}
                    onClick={() => sendAskDealerMessage(selectedCar, userRequestMsg)}
                  />
                )}
              </Item>
            </Grid>
          </Grid>
        </Box>
      </Modal.Footer>
    </Modal>
  );
};

TestDriveChatModalWeb.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  selectedCar: PropTypes.object, // Change this to object type to match your data structure
  sfmessages: PropTypes.arrayOf(PropTypes.object).isRequired,
  formatTimestamp: PropTypes.func.isRequired,
  refreshMessages: PropTypes.func.isRequired,
};

export default TestDriveChatModalWeb;
