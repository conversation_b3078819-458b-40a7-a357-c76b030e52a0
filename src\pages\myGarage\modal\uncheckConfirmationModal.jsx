import {
    Button,
    Dialog,
    <PERSON>alogActions,
    DialogContent,
    DialogContentText,
    DialogTitle,
} from "@mui/material";
import PropTypes from "prop-types";
import { useMediaQuery } from 'react-responsive';

const UncheckConfirmationModal = ({ open, onClose, onConfirm }) => {
    const isMobile = useMediaQuery({ maxWidth: 767 });

    return (
        <Dialog
            open={open}
            onClose={onClose}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
            fullWidth={true}  // Makes dialog full width on mobile
            maxWidth="sm"     // Limits the dialog's width
        >
            <DialogTitle id="alert-dialog-title" sx={{ textAlign: isMobile ? 'center' : 'left' }}>
                Confirm Removal
            </DialogTitle>
            <DialogContent>
                <DialogContentText id="alert-dialog-description" sx={{ fontSize: isMobile ? '14px' : '16px' }}>
                    If you remove the vehicle from the Top 5, you will no longer receive notifications from the dealer. Do you want to proceed?
                </DialogContentText>
            </DialogContent>
            <DialogActions sx={{ flexDirection: isMobile ? 'column' : 'row', alignItems: 'center' }}>
                <Button
                    onClick={onClose}
                    autoFocus
                    sx={{
                        marginBottom: isMobile ? '10px' : '0',
                        width: isMobile ? '100%' : 'auto',
                        textAlign: 'center',
                    }}
                >
                    Return to list
                </Button>
                <Button
                    onClick={onConfirm}
                    className="text-danger"
                    sx={{
                        width: isMobile ? '100%' : 'auto',
                        textAlign: 'center',
                    }}
                    style={{ marginLeft: isMobile ? '0' : '5px' }}
                >
                    Remove from Top 5
                </Button>
            </DialogActions>
        </Dialog>
    );
};

UncheckConfirmationModal.propTypes = {
    open: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    onConfirm: PropTypes.func.isRequired,
};

export default UncheckConfirmationModal;
