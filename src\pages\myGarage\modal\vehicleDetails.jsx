import PropTypes from "prop-types";
import {
  Box,
  Typography,
  CircularProgress,
} from "@mui/material";
import { Mo<PERSON>, Button } from "react-bootstrap";
import { useEffect, useState } from "react";
import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import ButtonBase from "@mui/material/ButtonBase";
import { ImageBaseURL } from "../../../config";
import ImageGallery from 'react-image-gallery';
import 'react-image-gallery/styles/css/image-gallery.css';
import IconButton from '@mui/material/IconButton';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import Tooltip from '@mui/material/Tooltip';

const Img = styled("img")({
  margin: "auto",
  display: "block",
  maxWidth: "100%",
  maxHeight: "100%",
});

const VehicleDetailsModal = ({
  open,
  onClose,
  vehicleDetails,
  selectedCar,
}) => {
  const [features, setFeatures] = useState("Loading...");
  const [photoLinks, setPhotoLinks] = useState([]);
  const [showAll, setShowAll] = useState(false);
  const fallbackImg = `${ImageBaseURL}fallback_image.png`;
  const [loading, setLoading] = useState(true);  // Add loading state
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    const isValidImage = async (url) => {
      try {
        const response = await fetch(url);
        return response.ok;
      } catch (error) {
        return false;
      }
    };

    const filterValidLinks = async (links) => {
      const validLinksPromises = links.map(async (link) => {
        const isValid = await isValidImage(link);
        return isValid ? link : null;
      });

      const validLinks = await Promise.all(validLinksPromises);
      return validLinks.filter(link => link !== null);
    };

    if (vehicleDetails?.features) {
      setFeatures(vehicleDetails.features);

      const photoLinksData = vehicleDetails.photo_links;
      if (photoLinksData) {
        setLoading(true);  // Start loading before filtering
        filterValidLinks(photoLinksData.split('|')).then((filteredLinks) => {
          setPhotoLinks(filteredLinks);
          setLoading(false);  // Stop loading after filtering is done
        });
      } else {
        setPhotoLinks([]);
        setLoading(false);  // No photo links, stop loading
      }
    } else {
      setFeatures("No Information Available");
      setPhotoLinks([]);
      setLoading(false);  // No data, stop loading
    }
  }, [vehicleDetails]);

  let initialFeatures = [];
  let remainingFeatures = [];

  if (features !== "Loading..." && features !== "No Information Available") {
    const featureArray = features.split("|");
    initialFeatures = featureArray.slice(0, 9);
    remainingFeatures = featureArray.slice(9);
  }

  const handleShowMore = () => {
    setShowAll(!showAll);
  };

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(selectedCar.vin)
      .then(() => {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000); // Reset after 2 seconds
      })
      .catch(err => console.error('Failed to copy: ', err));
  };

  return (
    <Modal show={open} onHide={onClose} onClose={onClose} centered size="lg">
      <Modal.Header className="modal-header-fixed" style={{ justifyContent: "center" }} closeButton>
        <Modal.Title>Detailed Vehicle View</Modal.Title>
      </Modal.Header>
      <Modal.Body className="modal-body-scrollable">
        {vehicleDetails ? (
          <>
            <Paper
              sx={{
                p: 2,
                margin: "auto",
                maxWidth: "100%",
                flexGrow: 1,
                backgroundColor: (theme) =>
                  theme.palette.mode === "dark" ? "#1A2027" : "#fff",
              }}
            >
              <Grid container spacing={2}>
                {loading ? (  // Show loading spinner if loading is true
                  <Grid item xs={12} style={{ textAlign: 'center' }}>
                    <CircularProgress />  {/* Use Material-UI CircularProgress for loading spinner */}
                  </Grid>
                ) : (
                  <>
                    {photoLinks.length > 1 ? (
                      <>
                        <Grid item xs={12} md={12}>
                          <ImageGallery items={photoLinks.map(url => ({
                            original: url,
                            thumbnail: url,
                          }))} />
                        </Grid>
                        <Grid item xs={12} md={12}>
                          <Typography gutterBottom variant="h6" component="div">
                            <b>{selectedCar.heading || ""}</b>
                          </Typography>
                          {selectedCar.seller_name && (
                            <Typography gutterBottom variant="subtitle2" component="div" sx={{ mb: 2 }}>
                              from dealer <b><i>{selectedCar.seller_name.toUpperCase() || ""}</i></b>
                            </Typography>
                          )}
                          <Typography variant="body2" gutterBottom>
                            {selectedCar.model || ""} {selectedCar.version || ""}{" "}
                            {selectedCar.engine_block || selectedCar.engine || ""}{" "}
                            {selectedCar.drivetrain || ""}
                          </Typography>
                          <Typography gutterBottom variant="subtitle1" component="div">
                            VIN: {selectedCar.vin}
                            <Tooltip title={copied ? "Copied!" : "Copy to clipboard"}>
                              <IconButton onClick={handleCopyToClipboard} style={{ marginLeft: 8 }}>
                                <ContentCopyIcon style={{ color: '#4891FF' }} />
                              </IconButton>
                            </Tooltip>
                          </Typography>
                          <Typography gutterBottom variant="subtitle1" component="div">
                            {selectedCar.price != null ? <b>$ {selectedCar.price.toLocaleString()}</b> : " "}
                          </Typography>
                        </Grid>
                      </>
                    ) : (
                      <>
                        <Grid item xs={12} md={4} style={{ textAlign: "center" }}>
                          <ButtonBase sx={{ width: "100%", height: "auto" }}>
                            <Img
                              alt={selectedCar.heading || ""}
                              src={selectedCar.photo_url || fallbackImg}
                              onError={(e) => {
                                e.target.src = fallbackImg;
                              }}
                              sx={{
                                '&:hover': {
                                  transform: 'scale(1.2)',  // Adjust the scale value for desired zoom
                                },
                              }}
                            />
                          </ButtonBase>
                        </Grid>
                        <Grid item xs={12} md={8}>
                          <Typography gutterBottom variant="h6" component="div">
                            <b>{selectedCar.heading}</b>
                          </Typography>
                          {selectedCar.seller_name && (
                            <Typography gutterBottom variant="subtitle2" component="div" sx={{ mb: 2 }}>
                              from dealer <b><i>{selectedCar.seller_name.toUpperCase() || ""}</i></b>
                            </Typography>
                          )}
                          <Typography variant="body2" gutterBottom>
                            {selectedCar.model || ""} {selectedCar.version || ""}{" "}
                            {selectedCar.engine_block || selectedCar.engine || ""}{" "}
                            {selectedCar.drivetrain || ""}
                          </Typography>
                          <Typography gutterBottom variant="subtitle1" component="div">
                            VIN: {selectedCar.vin}
                            <Tooltip title={copied ? "Copied!" : "Copy to clipboard"}>
                              <IconButton onClick={handleCopyToClipboard} style={{ marginLeft: 8 }}>
                                <ContentCopyIcon style={{ color: '#4891FF' }} />
                              </IconButton>
                            </Tooltip>
                          </Typography>
                          <Typography gutterBottom variant="subtitle1" component="div">
                            {selectedCar.price != null ? <b>$ {selectedCar.price.toLocaleString()}</b> : " "}
                          </Typography>
                        </Grid>
                      </>
                    )}
                  </>
                )}
              </Grid>
            </Paper>
            <Paper
              className="mt-3"
              sx={{
                p: 2,
                margin: "auto",
                maxWidth: "100%",
                flexGrow: 1,
                backgroundColor: (theme) =>
                  theme.palette.mode === "dark" ? "#1A2027" : "#fff",
              }}
            >
              <Typography variant="h6" component="div" align="center">
                <b>
                  Vehicle Details
                </b>
              </Typography>
              <Grid container spacing={2} className="mt-2">
                <Grid item xs={12} sm={6}>
                  <Box display="flex" alignItems="center">
                    <svg
                      width="35px"
                      height="35px"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M3 8L5.72187 10.2682C5.90158 10.418 6.12811 10.5 6.36205 10.5H17.6379C17.8719 10.5 18.0984 10.418 18.2781 10.2682L21 8M6.5 14H6.51M17.5 14H17.51M8.16065 4.5H15.8394C16.5571 4.5 17.2198 4.88457 17.5758 5.50772L20.473 10.5777C20.8183 11.1821 21 11.8661 21 12.5623V18.5C21 19.0523 20.5523 19.5 20 19.5H19C18.4477 19.5 18 19.0523 18 18.5V17.5H6V18.5C6 19.0523 5.55228 19.5 5 19.5H4C3.44772 19.5 3 19.0523 3 18.5V12.5623C3 11.8661 3.18166 11.1821 3.52703 10.5777L6.42416 5.50772C6.78024 4.88457 7.44293 4.5 8.16065 4.5ZM7 14C7 14.2761 6.77614 14.5 6.5 14.5C6.22386 14.5 6 14.2761 6 14C6 13.7239 6.22386 13.5 6.5 13.5C6.77614 13.5 7 13.7239 7 14ZM18 14C18 14.2761 17.7761 14.5 17.5 14.5C17.2239 14.5 17 14.2761 17 14C17 13.7239 17.2239 13.5 17.5 13.5C17.7761 13.5 18 13.7239 18 14Z"
                        stroke="#000000"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>{" "}
                    <Box ml={2}>
                      <Typography variant="subtitle1" component="div">
                        <b>Trim</b>
                      </Typography>
                      <Typography variant="subtitle2" component="div">
                        {vehicleDetails.trim || "No Information Available."}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box display="flex" alignItems="center">
                    <svg width="38px" height="38px" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg" fill="#000000">
                      <g id="SVGRepo_bgCarrier" strokeWidth="0" />
                      <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />
                      <g id="SVGRepo_iconCarrier"> <path fill="var(--ci-primary-color, #000000)" d="M412.284,294.37l-12.5,15.642c-8.354,10.454-50.027,64.208-50.027,95.883,0,36.451,28.049,66.105,62.526,66.105s62.527-29.654,62.527-66.105c0-31.675-41.673-85.429-50.028-95.883Zm0,145.63c-16.832,0-30.526-15.3-30.526-34.105,0-11.662,15.485-37.883,30.531-59.2,15.043,21.3,30.522,47.509,30.522,59.2C442.811,424.7,429.116,440,412.284,440Z" className="ci-primary" /> <path fill="var(--ci-primary-color, #000000)" d="M122.669,51.492,96.133,124.4,30.092,97.205,17.908,126.8l67.271,27.7L26.9,314.606a48.056,48.056,0,0,0,28.689,61.523l184.719,67.232a48,48,0,0,0,61.523-28.688L397.6,151.56Zm149.1,352.236a16,16,0,0,1-20.508,9.563L66.537,346.059a16,16,0,0,1-9.563-20.507L73.553,280H316.8ZM85.2,248l29.594-81.311,36.333,14.961a32.644,32.644,0,1,0,11.236-29.98l-36.615-15.077,16.046-44.085,214.79,78.177L328,249.219V248Z" className="ci-primary" /> </g>
                    </svg>{" "}
                    <Box ml={2}>
                      <Typography variant="subtitle1" component="div">
                        <b>Exterior Color</b>
                      </Typography>
                      <Typography variant="subtitle2" component="div">
                        {vehicleDetails.exteriorColor || vehicleDetails.exterior_color ||
                          "No Information Available."}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box display="flex" alignItems="center">
                    <svg version="1.1" id="_x32_" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" width="35px" height="35px" viewBox="0 0 512 512" xmlSpace="preserve" fill="#000000">
                      <g id="SVGRepo_bgCarrier" strokeWidth="0" />
                      <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />
                      <g id="SVGRepo_iconCarrier"> <style type="text/css"></style> <g> <path className="st0" d="M391.703,195.531l-0.203-0.344l-0.266-0.375L256,0L120.516,195.156l-0.234,0.375 c-27.172,42.719-45.609,92.109-45.703,141.75c-0.016,21.328,3.5,42.688,11.438,63.063c7.906,20.375,20.281,39.688,37.313,56.719 h0.016C159.906,493.641,208.063,512.031,256,512c47.938,0.031,96.078-18.359,132.656-54.938l-15.859,15.859l15.859-15.859 c17.047-17.031,29.422-36.344,37.328-56.703c7.938-20.391,11.438-41.75,11.438-63.063 C437.328,287.641,418.891,238.25,391.703,195.531z M384.141,384.078c-5.719,14.672-14.453,28.438-27.219,41.234l15.875-15.875 l-15.875,15.875C329,453.219,292.609,467.078,256,467.109c-36.609-0.031-73-13.891-100.922-41.797l0,0 c-12.781-12.797-21.5-26.563-27.234-41.25c-5.703-14.672-8.375-30.344-8.375-46.781c-0.109-38.078,14.859-80.328,38.531-117.375 l98-141.188l98.031,141.219c23.641,37.047,38.594,79.281,38.5,117.359C392.531,353.734,389.859,369.406,384.141,384.078z" /> <path className="st0" d="M205.328,237.781c-23.422,26.031-60.719,94.547-37.297,142.25c26.156,53.281,61.75,21.609,45.109-12.125 C181.047,302.844,205.328,237.781,205.328,237.781z" /> </g> </g>
                    </svg>{" "}
                    <Box ml={2}>
                      <Typography variant="subtitle1" component="div">
                        <b>Interior Color</b>
                      </Typography>
                      <Typography variant="subtitle2" component="div">
                        {vehicleDetails.interiorColor || vehicleDetails.interior_color ||
                          "No Information Available."}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box display="flex" alignItems="center">
                    <svg
                      fill="#000000"
                      height="35px"
                      width="35px"
                      version="1.1"
                      id="Capa_1"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlnsXlink="http://www.w3.org/1999/xlink"
                      viewBox="0 0 374.104 374.104"
                      xmlSpace="preserve"
                    >
                      <path
                        d="M310.384,114.877h14.884c13.926,0,25.255-11.329,25.255-25.255V25.255C350.523,11.329,339.194,0,325.268,0h-14.884
                            c-13.927,0-25.257,11.329-25.257,25.255v23.184h-62.852c-4.015-15.704-18.282-27.35-35.22-27.35
                            c-16.939,0-31.207,11.646-35.222,27.35H88.977V25.255C88.977,11.329,77.648,0,63.724,0H48.839C34.912,0,23.581,11.329,23.581,25.255
                            v64.367c0,13.926,11.331,25.255,25.259,25.255h14.884c13.924,0,25.253-11.329,25.253-25.255V66.438h62.856
                            c3.279,12.825,13.396,22.941,26.222,26.22v168.006c-11.573,3.794-19.958,14.692-19.958,27.518v19.482h-69.12v-23.184
                            c0-13.926-11.329-25.255-25.253-25.255H48.839c-13.928,0-25.259,11.329-25.259,25.255v64.367c0,13.926,11.331,25.255,25.259,25.255
                            h14.884c13.924,0,25.253-11.329,25.253-25.255v-23.184h69.12v19.483c0,15.966,12.991,28.955,28.958,28.955
                            c15.965,0,28.954-12.989,28.954-28.955v-19.483h69.118v23.184c0,13.926,11.33,25.255,25.257,25.255h14.884
                            c13.926,0,25.255-11.329,25.255-25.255v-64.367c0-13.926-11.329-25.255-25.255-25.255h-14.884
                            c-13.927,0-25.257,11.329-25.257,25.255v23.184h-69.118v-19.482c0-12.824-8.383-23.721-19.954-27.516V92.659
                            c12.825-3.279,22.941-13.396,26.22-26.22h62.852v23.184C285.127,103.548,296.457,114.877,310.384,114.877z M70.977,89.622
                            c0,4-3.253,7.255-7.253,7.255H48.839c-4.002,0-7.259-3.255-7.259-7.255V25.255c0-4,3.256-7.255,7.259-7.255h14.884
                            c4,0,7.253,3.255,7.253,7.255V89.622z M70.977,348.849c0,4-3.253,7.255-7.253,7.255H48.839c-4.002,0-7.259-3.255-7.259-7.255
                            v-64.367c0-4,3.256-7.255,7.259-7.255h14.884c4,0,7.253,3.255,7.253,7.255V348.849z M303.127,284.481c0-4,3.255-7.255,7.257-7.255
                            h14.884c4,0,7.255,3.255,7.255,7.255v64.367c0,4-3.254,7.255-7.255,7.255h-14.884c-4.001,0-7.257-3.255-7.257-7.255V284.481z
                            M198.009,345.148c0,6.041-4.914,10.955-10.954,10.955h-0.002c-6.041,0-10.956-4.914-10.956-10.955v-56.966
                            c0-6.041,4.916-10.956,10.958-10.956c6.04,0,10.954,4.915,10.954,10.956V345.148z M187.692,75.756
                            c-0.211-0.015-0.422-0.032-0.637-0.032s-0.426,0.017-0.637,0.032c-9.823-0.34-17.714-8.414-17.714-18.317
                            c0-10.118,8.232-18.35,18.352-18.35c10.118,0,18.349,8.231,18.349,18.35C205.404,67.342,197.513,75.416,187.692,75.756z
                            M303.127,25.255c0-4,3.255-7.255,7.257-7.255h14.884c4,0,7.255,3.255,7.255,7.255v64.367c0,4-3.254,7.255-7.255,7.255h-14.884
                            c-4.001,0-7.257-3.255-7.257-7.255V25.255z"
                      />
                    </svg>{" "}
                    <Box ml={2}>
                      <Typography variant="subtitle1" component="div">
                        <b>Drivetrain</b>
                      </Typography>
                      <Typography variant="subtitle2" component="div">
                        {vehicleDetails.drivetrain ||
                          "No Information Available."}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box display="flex" alignItems="center">
                    <svg
                      fill="#000000"
                      height="35px"
                      width="35px"
                      version="1.1"
                      id="Capa_1"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlnsXlink="http://www.w3.org/1999/xlink"
                      viewBox="0 0 383.114 383.114"
                      xmlSpace="preserve"
                    >
                      <path
                        d="M144.114,316.654c4.864,1.025,7.976,5.8,6.95,10.663c-0.894,4.237-4.633,7.146-8.798,7.146
                            c-0.615,0-1.239-0.063-1.865-0.195c-52.492-11.066-93.56-52.135-104.624-104.627c-1.025-4.864,2.087-9.638,6.951-10.663
                            c4.86-1.024,9.637,2.087,10.663,6.95C62.984,271.446,98.596,307.058,144.114,316.654z M177.525,201.533c0-4.971-4.029-9-9-9h-0.031
                            c-4.971,0-8.984,4.029-8.984,9s4.044,9,9.015,9S177.525,206.503,177.525,201.533z M383.114,22.071v109.6c0,4.971-4.029,9-9,9
                            s-9-4.029-9-9v-10.949H225.56l-30.513,52.852c5.318,5.05,9.203,11.599,10.969,18.96h43.302v-42.865c0-4.971,4.029-9,9-9s9,4.029,9,9
                            v51.598c0.002,0.089,0.004,0.178,0.004,0.268c0,29.557-13.044,56.124-33.679,74.247c-0.21,0.222-0.434,0.436-0.67,0.64
                            c-8.145,7.018-17.32,12.601-27.13,16.603c-11.401,4.669-23.861,7.267-36.911,7.321c-0.02,0.003-0.038,0-0.056,0
                            c-0.024,0-0.044,0-0.067,0.001c-0.025,0.001-0.048,0.001-0.071,0c-0.025-0.001-0.051-0.001-0.074,0c-0.025,0-0.052,0-0.076,0
                            c-0.028,0-0.079-0.007-0.078,0c0,0,0,0,0,0c-17.77,0-34.465-4.718-48.89-12.964c-0.172-0.086-0.344-0.178-0.513-0.275
                            c-0.179-0.103-0.353-0.212-0.522-0.325c-29.227-17.183-48.887-48.963-48.887-85.247s19.661-68.064,48.888-85.247
                            c0.169-0.113,0.343-0.223,0.522-0.325c0.169-0.098,0.34-0.189,0.513-0.275c14.427-8.248,31.117-12.964,48.893-12.964
                            c0.719,0,1.419,0.084,2.089,0.244c0.671-0.16,1.37-0.244,2.089-0.244h47.517c0.109-0.003,0.22-0.003,0.329,0h144.578V51.022H172.69
                            c-0.717,0-1.415-0.084-2.083-0.242c-0.668,0.158-1.366,0.242-2.083,0.242C85.519,51.022,18,118.541,18,201.533
                            s67.519,150.511,150.512,150.511c57.937,0,111.379-33.87,136.151-86.288c0.416-0.878,0.954-1.658,1.586-2.329
                            c8.479-18.72,13.235-39.469,13.32-61.301c-0.002-0.075-0.003-0.149-0.003-0.225v-52.233c0-4.971,4.029-9,9-9s9,4.029,9,9v51.598
                            c0.002,0.089,0.004,0.178,0.004,0.268c0,92.917-75.594,168.511-168.512,168.511c-0.182,0-0.363,0-0.546,0
                            C75.594,370.043,0,294.45,0,201.533S75.594,33.022,168.512,33.022c0.719,0,1.419,0.084,2.089,0.244
                            c0.671-0.16,1.37-0.244,2.089-0.244h192.424V22.071c0-4.971,4.029-9,9-9S383.114,17.1,383.114,22.071z M88.195,192.533h42.81
                            c1.767-7.362,5.651-13.91,10.971-18.961l-21.397-37.063C102.922,149.559,90.745,169.614,88.195,192.533z M120.577,266.557
                            l21.398-37.063c-5.319-5.051-9.204-11.599-10.971-18.961h-42.81C90.745,233.455,102.92,253.508,120.577,266.557z M200.871,275.581
                            l-21.4-37.068c-3.475,1.032-7.153,1.586-10.958,1.586c-3.806,0-7.483-0.554-10.959-1.586l-21.401,37.069
                            c9.913,4.348,20.86,6.763,32.36,6.763c0.02,0,0.041,0,0.063,0h0.002c0.026,0,0.047,0,0.071,0c0.021,0,0.048,0,0.066,0
                            c0.023,0,0.044-0.001,0.068,0c0.019-0.002,0.043-0.001,0.063-0.001c0.008,0,0.01,0,0.019,0c10.25-0.045,20.315-2.037,29.685-5.788
                            C199.331,276.242,200.102,275.917,200.871,275.581z M168.512,222.098c11.339,0,20.564-9.226,20.564-20.565
                            s-9.225-20.565-20.564-20.565c-11.341,0-20.567,9.226-20.567,20.565S157.171,222.098,168.512,222.098z M195.047,229.493
                            l21.398,37.063c17.654-13.049,29.83-33.104,32.379-56.022h-42.808C204.25,217.894,200.365,224.443,195.047,229.493z
                            M204.775,120.721H172.69c-0.717,0-1.415-0.084-2.083-0.242c-0.668,0.158-1.366,0.242-2.083,0.242
                            c-11.505,0-22.456,2.415-32.372,6.765l21.4,37.067c3.476-1.032,7.153-1.586,10.959-1.586c3.805,0,7.483,0.554,10.958,1.586
                            L204.775,120.721z"
                      />
                    </svg>{" "}
                    <Box ml={2}>
                      <Typography variant="subtitle1" component="div">
                        <b>Engine</b>
                      </Typography>
                      <Typography variant="subtitle2" component="div">
                        {vehicleDetails.engine || "No Information Available."}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box display="flex" alignItems="center">
                    <svg fill="#000000" width="36px" height="36px" viewBox="0 0 56 56" id="Layer_1" version="1.1" xmlSpace="preserve" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">

                      <g id="SVGRepo_bgCarrier" strokeWidth="0" />

                      <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />

                      <g id="SVGRepo_iconCarrier"> <g> <path d="M33.7,27.5c-0.3-0.1-7.3-1.5-11.2,2.1c-3.4,3.1-3.7,9-3.7,10.7l-1.5,1.3c-0.2,0.2-0.3,0.5-0.3,0.7c0,0.2,0.1,0.5,0.3,0.7 c0.2,0.2,0.5,0.3,0.7,0.3c0.2,0,0.5-0.1,0.7-0.3l1.5-1.3c0.5,0.1,1.4,0.1,2.5,0.1c2.6,0,6.4-0.4,8.8-2.7c3.9-3.6,3.1-10.7,3.1-11 C34.5,27.9,34.1,27.6,33.7,27.5z M30.1,37.8C30.1,37.8,30.1,37.8,30.1,37.8c-2,1.9-5.6,2.2-7.9,2.1l5.1-4.6 c0.2-0.2,0.3-0.5,0.3-0.7c0-0.2-0.1-0.5-0.3-0.7c-0.4-0.4-1-0.4-1.4-0.1l-5.1,4.6c0.2-2.2,1-5.5,3-7.3c2.5-2.3,7-2,8.7-1.8 C32.7,31.1,32.6,35.5,30.1,37.8z" /> <path d="M35,8.2H16.1c-1.4,0-2.5,1.1-2.5,2.5v7.5c0,1.4,1.1,2.5,2.5,2.5H35c1.4,0,2.5-1.1,2.5-2.5v-7.5C37.5,9.3,36.4,8.2,35,8.2z M35.5,18.2c0,0.3-0.2,0.5-0.5,0.5H16.1c-0.3,0-0.5-0.2-0.5-0.5v-7.5c0-0.3,0.2-0.5,0.5-0.5H35c0.3,0,0.5,0.2,0.5,0.5V18.2z" /> <path d="M27.9,11.4h-1c-0.6,0-1,0.4-1,1s0.4,1,1,1h1c0.6,0,1-0.4,1-1S28.5,11.4,27.9,11.4z" /> <path d="M33.1,11.4h-1c-0.6,0-1,0.4-1,1s0.4,1,1,1h1c0.6,0,1-0.4,1-1S33.6,11.4,33.1,11.4z" /> <path d="M27.9,15.1h-1c-0.6,0-1,0.4-1,1s0.4,1,1,1h1c0.6,0,1-0.4,1-1S28.5,15.1,27.9,15.1z" /> <path d="M33.1,15.1h-1c-0.6,0-1,0.4-1,1s0.4,1,1,1h1c0.6,0,1-0.4,1-1S33.6,15.1,33.1,15.1z" /> <path d="M46.8,12.5C46.8,12.5,46.8,12.5,46.8,12.5l-1.7-1.7c-0.4-0.4-1-0.4-1.4,0s-0.4,1,0,1.4l1.4,1.4v6.1l4.4,3V38 c0,0.6-0.5,1.1-1.1,1.1c-0.6,0-1.1-0.5-1.1-1.1v-9.9c0-1.1-0.9-2-2-2h-1.9v-19c0-2.2-1.8-4.1-4.1-4.1H11.9C9.7,3,7.8,4.8,7.8,7.1 v37.1c0,0.8,0.2,1.5,0.6,2.1H6.9c-1.3,0-2.4,1.1-2.4,2.4v2c0,1.3,1.1,2.4,2.4,2.4h37.4c1.3,0,2.4-1.1,2.4-2.4v-2 c0-1.3-1.1-2.4-2.4-2.4h-1.6c0.4-0.6,0.6-1.3,0.6-2.1V28.1h1.9V38c0,1.7,1.4,3.1,3.1,3.1c1.7,0,3.1-1.4,3.1-3.1V17.2L46.8,12.5z M44.7,48.6v2c0,0.2-0.2,0.4-0.4,0.4H6.9c-0.2,0-0.4-0.2-0.4-0.4v-2c0-0.2,0.2-0.4,0.4-0.4h5h27.3h5C44.5,48.2,44.7,48.4,44.7,48.6 z M11.9,46.2c-1.1,0-2.1-0.9-2.1-2.1V7.1c0-1.1,0.9-2.1,2.1-2.1h27.3c1.1,0,2.1,0.9,2.1,2.1v37.1c0,1.1-0.9,2.1-2.1,2.1H11.9z M47.1,18.7v-3l2.4,2.4v2.3L47.1,18.7z" /> </g> </g>

                    </svg>
                    <Box ml={2}>
                      <Typography variant="subtitle1" component="div">
                        <b>Fuel Type</b>
                      </Typography>
                      <Typography variant="subtitle2" component="div">
                        {vehicleDetails.fuel_type || "No Information Available."}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Paper>
            <Paper
              className="mt-3"
              sx={{
                p: 2,
                margin: "auto",
                maxWidth: "100%",
                flexGrow: 1,
                backgroundColor: (theme) =>
                  theme.palette.mode === "dark" ? "#1A2027" : "#fff",
              }}
            >
              <Typography variant="h6" component="div" align="center">
                <b>
                  Key Features
                </b>
              </Typography>
              <Grid container spacing={2} className="mt-2">
                {features === "No Information Available" ? (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" component="div" className="text-center">
                      {features}
                    </Typography>
                  </Grid>
                ) : (
                  <>
                    <Grid item xs={12}>
                      <div
                        style={{
                          display: "flex",
                          flexWrap: "wrap",
                          justifyContent: "space-between",
                          wordBreak: "break-word",
                          lineHeight: "1",
                        }}
                      >
                        {initialFeatures
                          .filter((feature) => feature)
                          .map((feature) => (
                            <div key={feature} style={{ width: "45%" }}>
                              <ul>
                                <li>{feature}</li>
                              </ul>
                            </div>
                          ))}
                        {showAll &&
                          remainingFeatures
                            .filter((feature) => feature)
                            .map((feature) => (
                              <div key={feature} style={{ width: "45%" }}>
                                <ul>
                                  <li>{feature}</li>
                                </ul>
                              </div>
                            ))}
                      </div>
                    </Grid>
                    {remainingFeatures.length > 0 && (
                      <Grid item xs={12} style={{ textAlign: "center" }}>
                        <Button
                          onClick={handleShowMore}
                          className="map-text"
                          style={{
                            background: "transparent",
                            border: "none",
                            color: "#4891FF",
                            fontWeight: "600",
                          }}
                        >
                          {showAll ? "View Less Features" : "View All Features"}
                        </Button>
                      </Grid>
                    )}
                  </>
                )}
              </Grid>
            </Paper>
          </>
        ) : (
          <CircularProgress />
        )}
      </Modal.Body>
      <Modal.Footer className="modal-footer-fixed">
        <Button
          variant="btn btn-danger"
          onClick={onClose}
          className="fw-600"
          style={{ width: "25%" }}
        >
          CLOSE
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

VehicleDetailsModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  vehicleDetails: PropTypes.shape({
    trim: PropTypes.string,
    exterior_color: PropTypes.string,
    interior_color: PropTypes.string,
    engine: PropTypes.string,
    drivetrain: PropTypes.string,
    features: PropTypes.string,
    exteriorColor: PropTypes.string,
    interiorColor: PropTypes.string,
    fuel_type: PropTypes.string,
    photo_links: PropTypes.string,
  }),
  selectedCar: PropTypes.string,
};

export default VehicleDetailsModal;
