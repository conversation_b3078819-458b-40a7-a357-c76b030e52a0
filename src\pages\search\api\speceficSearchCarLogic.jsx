import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getMakeData,
  getModelData,
  getTrimData,
  getGarageVehicleVins,
  getVehicleMinPrice,
} from "../../../store/apps/car/index";
import { checkAIHealth } from "../../../store/apps/AIChat";
import { fetchLocationData } from "../../../store/apps/location";
import { useNavigate } from "react-router-dom";
import { getloggedInUserDetails } from "../../../store/apps/user";
import debounce from "lodash.debounce";
import { getYearRange } from '../../../util/yearMaxMin';
import { CarLookingOptions } from "../../../config";
import { pageMetaData } from "../../../../data/pageMetaData";
import { pageComponentData } from "../../../../data/componentMetaData";

export function useSearchCarLogic() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const token = localStorage.getItem("token");
  const [distance, setDistance] = useState(import.meta.env.VITE_DEFAULT_MILES);
  const [carBrand, setCarBrand] = useState("");
  const [carModel, setCarModel] = useState("");
  const [carBrandOptions, setCarBrandOptions] = useState([]);
  const [carModelOptions, setCarModelOptions] = useState([]);
  const [vehicleTrimOptions, setVehicleTrimOptions] = useState([]); // Store all trims
  const [selectedTrims, setSelectedTrims] = useState([]); // Store selected trims
  const [selectedExteriorColors, setSelectedExteriorColors] = useState([]); // Store selected exterior colors
  const [selectedInteriorColors, setSelectedInteriorColors] = useState([]); // Store selected interior colors
  const [selectedFuelTypes, setSelectedFuelTypes] = useState([]); // Store selected interior colors
  const [allModels, setAllModels] = useState([]); // Store all models initially
  const [zipCode, setZipCode] = useState("");
  const [loadingBrand, setLoadingBrand] = useState(true);
  const [loadingModel, setLoadingModel] = useState(true);
  const [loadingTrim, setLoadingTrim] = useState(true); // Store loading state for trim
  const [loadingUser, setLoadingUser] = useState(false);
  const [isBrandSelected, setIsBrandSelected] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [userCoordinates, setUserCoordinates] = useState({
    latitude: null,
    longitude: null,
  });
  const [cuLogo, setCuLogo] = useState("");
  const [shouldFetchUserData, setShouldFetchUserData] = useState(true);
  const [maxApprovalAmount, setMaxApprovalAmount] = useState();
  const [purchasePower, setPurchasePower] = useState();
  const [userCarLookingFor, setUserCarLookingFor] = useState("");
  const [userVhicleVins, setuserVhicleVins] = useState([]);
  const [selectedModelId, setSelectedModelId] = useState(null); // Store selected model ID
  const [loadingLocation, setloadingLocation] = useState(false);
  const [invVehicleMinPrice, setInvVehicleMinPrice] = useState(0);
  const [priceValue, setpriceValue] = useState([0, 0]); // Initial value, assuming min and max both 0 initially
  const [minMaxPrice, setMinMaxPrice] = useState({ min: 0, max: 0 }); // Initial min/max state
  const [yearValue, setYearValue] = useState([0, 0]); // Default value
  const [isModalButtonDisabled, setIsModalButtonDisabled] = useState(false);
  const [minInput, setMinInput] = useState(0);
  const [maxInput, setMaxInput] = useState(0);
  const [minPriceError, setMinPriceError] = useState("");
  const [maxPriceError, setMaxPriceError] = useState("");
  const [timeoutId, setTimeoutId] = useState(null); // State to store the timeout ID
  const [minYearInput, setMinYearInput] = useState(0);
  const [maxYearInput, setMaxYearInput] = useState(0);
  const [minYearError, setMinYearError] = useState("");
  const [maxYearError, setMaxYearError] = useState("");
  const [minYearValue, setMinYearValue] = useState(0);
  const [maxYearValue, setMaxYearValue] = useState(0);
  const [inventoryPriceYearData, setinventoryPriceYearData] = useState({});
  const [pageId, setPageId] = useState(null); // State to store the page_id
  const [componentMetaData, setComponentMetaData] = useState(null); // State to store the componentMetaData
  const aiHealth = useSelector(state => state.aiChat.aiHealth);
  const aiHealthLoading = useSelector(state => state.aiChat.aiHealthLoading);
  const aiHealthError = useSelector(state => state.aiChat.aiHealthError);

  useEffect(() => {
    dispatch(checkAIHealth(token));
    console.log("aiHealth",aiHealth);
    
  }, [dispatch, token]);

  // Fetch makes and models on initial load
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const [modelResponse, trimResponse] = await Promise.all([
          dispatch(getModelData(token)),
          dispatch(getTrimData(token)),
        ]);
        const models = modelResponse.payload?.data ?? [];
        const vinsResponse = await dispatch(getGarageVehicleVins(token));
        setuserVhicleVins(vinsResponse.payload);
        const trims = trimResponse.payload?.data ?? [];
        setAllModels(models);
        setVehicleTrimOptions(trims);
        setLoadingBrand(false);
        setLoadingModel(false);
        setLoadingTrim(false);
      } catch (error) {
        console.error("Error fetching initial data:", error);
        setLoadingBrand(false);
        setLoadingModel(false);
        setLoadingTrim(false);
      }
    };

    fetchInitialData();
  }, [dispatch, token]);

  // Fetch user data and coordinates if necessary
  useEffect(() => {
    if (!shouldFetchUserData) return;
    if (!userCoordinates.latitude || !userCoordinates.longitude || !zipCode) {
      setLoadingUser(true);
      setloadingLocation(true);
      const fetchUserData = async () => {
        try {
          const { payload: { data: userDetails = [] } = {} } = await dispatch(
            getloggedInUserDetails(token)
          );
          if (userDetails.length > 0) {
            setMaxApprovalAmount(userDetails[0].preapproved_max_amt ?? 0);
            setPurchasePower(userDetails[0].user_total_purchase_power ?? 0);
            setUserCarLookingFor(userDetails[0].user_car_looking_for ?? "");
            setUserCoordinates({
              latitude: userDetails[0].latitude,
              longitude: userDetails[0].longitude,
            });
            setZipCode(userDetails[0].zip_code);
            setCuLogo(userDetails[0].logo_url);
            if (userDetails[0].zip_code == "" || userDetails[0].zip_code == 0) {
              setIsButtonDisabled(true);
            } else {
              setIsButtonDisabled(false);
            }
            const vehicleMinPrice = await dispatch(getVehicleMinPrice(token));
              // Extract data
              const vehicleData = vehicleMinPrice.payload.data[0] ?? {
                new_min_price: 0,
                used_min_price: 0,
                new_min_year: 0,
                used_min_year: 0,
                new_max_year: 0,
                used_max_year: 0
              };
              setinventoryPriceYearData(vehicleData);
              // Set vehicle min price based on user preference
              const userCarLookingFor = userDetails[0].user_car_looking_for ?? "";
              // Set default state values
              let minPrice = 0;
              let minYear = 0;
              let maxYear = 0;

              switch (userCarLookingFor) {
                case "new or used":
                  // If user is looking for either New or Used, we should set the lowest min price and the earliest min year, 
                  // and the latest max year.
                  minPrice = Math.min(vehicleData.new_min_price, vehicleData.new_min_price);
                  minYear = Math.min(vehicleData.new_min_year, vehicleData.used_min_year);
                  maxYear = Math.max(vehicleData.new_max_year, vehicleData.used_max_year);
                  break;

                case "new":
                  // If user is looking for a New car only
                  minPrice = vehicleData.new_min_price;
                  minYear = new Date().getFullYear() - import.meta.env.VITE_NEW_INVENTORY_YEAR_RANGE;
                  maxYear = vehicleData.new_max_year;
                  break;

                case "used":
                  // If user is looking for a Used car only
                  minPrice = vehicleData.used_min_price;
                  minYear = vehicleData.used_min_year;
                  maxYear = vehicleData.used_max_year;
                  break;

                default:
                  minPrice = Math.min(vehicleData.new_min_price, vehicleData.new_min_price);
                  minYear = Math.min(vehicleData.new_min_year, vehicleData.used_min_year);
                  maxYear = Math.max(vehicleData.new_max_year, vehicleData.used_max_year);
              }
              // Update state with calculated values
              setInvVehicleMinPrice(minPrice);
              setMinYearValue(minYear);
              setMaxYearValue(maxYear);
              setYearValue([minYear, maxYear]);
              setMinYearInput(minYear);
              setMaxYearInput(maxYear);
            // Filter makes based on userCarLookingFor
            const [makeResponse] = await Promise.all([
              dispatch(getMakeData(token)),
            ]);
            const makes = makeResponse.payload?.data ?? [];
            const filteredMakes = filterMakes(makes, userCarLookingFor);
            setCarBrandOptions(filteredMakes); // Set filtered makes
          }
         setLoadingUser(false);
          setloadingLocation(false);
        } catch (error) {
          console.error("Error fetching user data:", error);
          setLoadingUser(false);
          setloadingLocation(false);
        }
      };
      fetchUserData();
    }
  }, [shouldFetchUserData, userCoordinates.latitude, userCoordinates.longitude, dispatch, token]);

  // Filter Makes according to userCarLookingFor
  const filterMakes = (makes, userCarLookingFor) => {
    switch (userCarLookingFor) {
      case "new or used":
        return makes.filter(make => 
          (make.new_inv_flag === true || make.new_inv_flag === false) ||
          (make.used_inv_flag === true || make.used_inv_flag === false)
        );
  
      case "new":
        return makes.filter(make => make.new_inv_flag);
  
      case "used":
        return makes.filter(make => make.used_inv_flag);
  
      default:
        return makes; // Fallback if none match
    }
  };
  // Handle brand selection
  const handleBrandChange = (event, newValue) => {
    setSelectedTrims([]); // Clear trims initially
    if (newValue) {
      const selectedBrandId = newValue.id;
      setCarBrand(newValue.make);
      setIsBrandSelected(true);
      setCarModel(""); // Reset the selected model
      filterModelOptions(selectedBrandId);
    } else {
      handleBrandClear();
    }
  };
  
  // Filter models based on selected brand
  const filterModelOptions = (brandId) => {
    const filteredModels = allModels.filter(
      (model) => model.make_id === brandId
    );
    setCarModelOptions(filteredModels);
  };

  const handleModelChange = (event, newValue) => {
    setCarModel(newValue ? newValue.model : "");
    setSelectedModelId(newValue ? newValue.id : null); // Update selected model ID
  };

  const handleDistanceChange = (event, newValue) => {
    setDistance(newValue ? newValue.value : "");
  };

  const debouncedFetchLocation = debounce((value, fetchLocation) => {
    fetchLocation(value);
  }, 300); // Adjust debounce delay as needed

  const handleZipCodeChange = (event) => {
    const value = event.target.value;

    // If input is empty, reset the states
    if (value === "") {
      setZipCode("");
      setIsButtonDisabled(true);
      setShouldFetchUserData(false);
      setIsModalButtonDisabled(true);
      return;
    }

    // Ensure only numeric values with a maximum of 5 digits are allowed
    if (/^\d{0,5}$/.test(value)) {
      setZipCode(value);

      // Trigger API call when exactly 5 digits are entered
      if (value.length === 5) {
        debouncedFetchLocation(value, fetchLocation); // Call the API when we have a valid 5-digit zip code
        setIsModalButtonDisabled(false);
      } else {
        setIsButtonDisabled(true); // Disable the button if less than 5 digits are entered
        setIsModalButtonDisabled(true);
      }
    }
  };

  const fetchLocation = async (zip) => {
    setloadingLocation(true);
    try {
      const response = await dispatch(
        fetchLocationData({ zipcode: zip, token: token })
      );
      if (fetchLocationData.fulfilled.match(response)) {
        const { latitude, longitude } = response.payload.data;
        setUserCoordinates({ latitude, longitude });
        setErrorMessage("");
        setIsButtonDisabled(false);
        setloadingLocation(false);
        setIsModalButtonDisabled(false);
      } else {
        console.error("Error fetching location data:", response.payload);
        setErrorMessage("Invalid Zip Code. Please Enter Correct Zip Code.");
        setIsButtonDisabled(true);
        setloadingLocation(false);
        setIsModalButtonDisabled(true);
      }
    } catch (error) {
      console.error("Unexpected error:", error);
    }
  };

  const handleBrandClear = () => {
    setCarBrand("");
    setIsBrandSelected(false);
    setCarModelOptions([]); // Clear the model options
    handleModelClear();
    setSelectedTrims([]);
  };

  const handleModelClear = () => {
    setCarModel("");
  };

  const clearZipCode = () => {
    setZipCode("");
    setIsButtonDisabled(true);
    setIsModalButtonDisabled(true);
  };

  // Handle trim selection
  const handleTrimsChange = (event, value) => {
    const trimValues = value.map((item) => item.trim); // Extract the 'trim' values
    setSelectedTrims(trimValues); // Store only the 'trim' values
  };

  // Handle exterior color selection
  const handleExteriorColorChange = (event, value) => {
    const choosenExteriorColors = value.map((color) => color.title);
    setSelectedExteriorColors(choosenExteriorColors);
  };

  // Handle interior color selection
  const handleInteriorColorChange = (event, value) => {
    const choosenInteriorColors = value.map((color) => color.title);
    setSelectedInteriorColors(choosenInteriorColors);
  };

  // Handle fuel type selection
  const handleFuelTypeChange = (event, value) => {
    setSelectedFuelTypes(value);
  };

  // function for search button
  const handleArrowButtonClick = (aiQuery, vin) => {
    if(maxPriceError==="" && minPriceError==="" && maxYearError==="" && minYearError===""){
      const [minPrice, maxPrice] = priceValue;
      const [userMinYear, userMaxYear] = yearValue;
      const priceData = { 
        min: invVehicleMinPrice || 0, 
        max: purchasePower || maxApprovalAmount || 0    
      };
      // Check if the priceData matches priceValue
      const priceValuesMatch = priceData.min === priceValue[0] && priceData.max === priceValue[1];
      const { minYear, maxYear } = getYearRange();
      const yearData = { 
        min: minYear, 
        max: maxYear
      };
      const yearValuesMatch = yearData.min === yearValue[0] && yearData.max === yearValue[1];
      const params = {
        ...(typeof aiQuery === "string" && aiQuery.trim() !== "" && { aiQuery }), // Add aiQuery only if it's not empty
        vin,
        carBrand,
        carModel,
        latitude: userCoordinates.latitude,
        longitude: userCoordinates.longitude,
        trims: selectedTrims,
        exterior_colors: selectedExteriorColors,
        interior_colors: selectedInteriorColors,
        fuel_types: selectedFuelTypes,
        distance,
        zipCode,
        user_pre_aprv_loan_amount: maxApprovalAmount,
        user_total_purchase_power: purchasePower,
        user_car_looking_option: userCarLookingFor,
        prevRoute: "specific-search",
        addedCarinGarageVins: userVhicleVins,
        priceRangeValue: [minPrice, maxPrice],
        yearRangeValue: [userMinYear, userMaxYear],
        yearValuesMatch: yearValuesMatch,
        priceValuesMatch: priceValuesMatch
      };
      navigate("/swipe-cards", { state: { params } });
    }else{
      return true;
    }
  };

  const distanceOptions = [
    { label: "10 miles", value: "10" },
    { label: "20 miles", value: "20" },
    { label: "50 miles", value: "50" },
    { label: "100 miles", value: "100" },
    { label: "250 miles", value: "250" },
  ];

  const handleCarLookingChange = async(event, newValue) => {
    setUserCarLookingFor(newValue?.value ?? "");
    let minPrice = 0;
    let minYear = 0;
    let maxYear = 0;
    switch (newValue?.value) {
      case "new or used":
        // If user is looking for either New or Used, we should set the lowest min price and the earliest min year, 
        // and the latest max year.
        minPrice = Math.min(inventoryPriceYearData.new_min_price, inventoryPriceYearData.new_min_price);
        minYear = Math.min(inventoryPriceYearData.new_min_year, inventoryPriceYearData.used_min_year);
        maxYear = Math.max(inventoryPriceYearData.new_max_year, inventoryPriceYearData.used_max_year);
        break;

      case "new":
        // If user is looking for a New car only
        minPrice = inventoryPriceYearData.new_min_price;
        minYear = new Date().getFullYear() - import.meta.env.VITE_NEW_INVENTORY_YEAR_RANGE;
        maxYear = inventoryPriceYearData.new_max_year;
        break;

      case "used":
        // If user is looking for a Used car only
        minPrice = inventoryPriceYearData.used_min_price;
        minYear = inventoryPriceYearData.used_min_year;
        maxYear = inventoryPriceYearData.used_max_year;
        break;

      default:
        minPrice = inventoryPriceYearData.new_min_price;
        minYear = inventoryPriceYearData.new_min_year;
        maxYear = inventoryPriceYearData.new_max_year;
    }
    // Update state with calculated values
    setInvVehicleMinPrice(minPrice);
    setMinYearValue(minYear);
    setMaxYearValue(maxYear);
    setYearValue([minYear, maxYear]);
    setMinYearInput(minYear);
    setMaxYearInput(maxYear);
    setpriceValue([minPrice, purchasePower || maxApprovalAmount || 0 ]);
    setCarBrand("");
    setCarModel("");
    setCarModelOptions([]);
    setIsBrandSelected(false);
    // Filter makes based on userCarLookingFor
    const [makeResponse] = await Promise.all([
      dispatch(getMakeData(token)),
    ]);
    const makes = makeResponse.payload?.data ?? [];
    const filteredMakes = filterMakes(makes, newValue?.value ?? "");
    setCarBrandOptions(filteredMakes); 
  };

  useEffect(() => {
      // Get the page_id from pageMetaData based on slug
      const slug = "specific-search"; // Replace this with the desired slug
      const pageData = pageMetaData.find((page) => page.slug === slug);
      if (pageData) {
        setPageId(pageData.id);
      }
  
      // Get Page Componenet Meta Data
      const matchingComponents = pageComponentData.filter(
        (pageComponent) => pageComponent.page_id === pageId
      );
  
      if (matchingComponents.length > 0) {
        setComponentMetaData(matchingComponents);
      }
    }, [pageId]);

  return {
    distance,
    carBrand,
    carModel,
    carBrandOptions,
    carModelOptions,
    zipCode,
    loadingBrand,
    loadingModel,
    loadingUser,
    isBrandSelected,
    errorMessage,
    isButtonDisabled,
    userCoordinates,
    distanceOptions,
    cuLogo,
    handleBrandChange,
    handleModelChange,
    handleDistanceChange,
    handleZipCodeChange,
    handleTrimsChange,
    handleExteriorColorChange,
    handleInteriorColorChange,
    handleFuelTypeChange,
    clearZipCode,
    handleArrowButtonClick,
    purchasePower,
    maxApprovalAmount,
    userCarLookingFor,
    loadingTrim,
    vehicleTrimOptions,
    selectedTrims,
    selectedExteriorColors,
    selectedInteriorColors,
    selectedFuelTypes,
    selectedModelId,
    setSelectedTrims,
    setSelectedInteriorColors,
    setSelectedFuelTypes,
    setSelectedExteriorColors,
    loadingLocation,
    CarLookingOptions,
    handleCarLookingChange,
    invVehicleMinPrice,
    priceValue,
    setpriceValue,
    minMaxPrice,
    setMinMaxPrice,
    yearValue,
    setYearValue,
    isModalButtonDisabled, 
    setIsModalButtonDisabled,
    minInput, 
    setMinInput,
    maxInput, 
    setMaxInput,
    minPriceError, 
    setMinPriceError,
    maxPriceError, 
    setMaxPriceError,
    timeoutId, 
    setTimeoutId,
    minYearInput, 
    setMinYearInput,
    maxYearInput, 
    setMaxYearInput,
    minYearError, 
    setMinYearError,
    maxYearError, 
    setMaxYearError,
    minYearValue, 
    setMinYearValue,
    maxYearValue, 
    setMaxYearValue,
    componentMetaData,
    aiHealth,
    aiHealthLoading,
    aiHealthError
  };
}
