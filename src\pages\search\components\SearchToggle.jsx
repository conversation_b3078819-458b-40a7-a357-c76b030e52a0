import React from "react";
import { ToggleButton, ToggleButtonGroup, toggleButtonGroupClasses } from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import MicIcon from "@mui/icons-material/Mic";
import DirectionsCarIcon from "@mui/icons-material/DirectionsCar";
import { useMediaQuery } from 'react-responsive';
import { styled } from '@mui/material/styles';
import Paper from '@mui/material/Paper';

const StyledToggleButtonGroup = styled(ToggleButtonGroup)(() => ({
  [`& .${toggleButtonGroupClasses.grouped}`]: {
    border: 0,
    borderRadius: 16,
    borderColor: '#105DC7',
    [`&.${toggleButtonGroupClasses.disabled}`]: {
      border: 0,
    },
  },
  [`& .${toggleButtonGroupClasses.middleButton},& .${toggleButtonGroupClasses.lastButton}`]:
    {
      marginLeft: -1,
      borderLeft: '1px solid transparent',
    },
}));

const SearchToggle = ({ onSearchTypeChange }) => {
  const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' });
  const [searchType, setSearchType] = React.useState("basic");

  const handleChange = (event, newSearchType) => {
    if (newSearchType !== null) {
      setSearchType(newSearchType);
      onSearchTypeChange(newSearchType); // Send selection to parent
    }
  };
  

  return (
    <Paper
      elevation={0}
      sx={{
        display: "flex",
        border: `1px solid #105DC7 !important`,
        flexWrap: "wrap",
        borderRadius: 16,
        boxShadow: "rgba(0, 0, 0, 0.2) 0px 2px 2px",
      }}
    >
      <StyledToggleButtonGroup
        value={searchType}
        exclusive
        onChange={handleChange}
        size="small"
        sx={{
          borderColor: "#105DC7 !important",
          "& .Mui-selected": {
            backgroundColor: "#105DC7 !important",
            color: "white !important",
          },
        }}
      >
        <ToggleButton
          value="basic"
          sx={{
            color: searchType === "basic" ? "white !important" : "black",
            backgroundColor:
              searchType === "basic" ? "#105DC7 !important" : "transparent",
            "&:hover": {
              backgroundColor:
                searchType === "basic" ? "#105DC7 !important" : "transparent",
            },
          }}
        >
          <SearchIcon
            sx={{
              color:
                searchType === "basic"
                  ? "white !important"
                  : "#105DC7  !important",
              marginRight: isTabletOrMobile && searchType === "basic" ? 1 : 0,
            }}
          />
          {(!isTabletOrMobile || searchType === "basic") && " Basic Search"}
        </ToggleButton>

        {/* <ToggleButton
          value="voice"
          sx={{
            color: searchType === "voice" ? "white !important" : "black",
            backgroundColor:
              searchType === "voice" ? "#1565C0 !important" : "transparent",
            "&:hover": {
              backgroundColor:
                searchType === "voice" ? "#1565C0 !important" : "transparent",
            },
          }}
        >
          <MicIcon
            sx={{
              color:
                searchType === "voice"
                  ? "white !important"
                  : "#105DC7  !important",
              marginRight: isTabletOrMobile && searchType === "voice" ? 1 : 0,
            }}
          />
          {(!isTabletOrMobile || searchType === "voice") && " Voice Search"}
        </ToggleButton> */}

        <ToggleButton
          value="vin"
          sx={{
            color: searchType === "vin" ? "white !important" : "black",
            backgroundColor:
              searchType === "vin" ? "#1565C0 !important" : "transparent",
            "&:hover": {
              backgroundColor:
                searchType === "vin" ? "#1565C0 !important" : "transparent",
            },
          }}
        >
          <DirectionsCarIcon
            sx={{
              color:
                searchType === "vin"
                  ? "white !important"
                  : "#105DC7  !important",
              marginRight: isTabletOrMobile && searchType === "vin" ? 1 : 0,
            }}
          />
          {(!isTabletOrMobile || searchType === "vin") && " VIN Search"}
        </ToggleButton>
      </StyledToggleButtonGroup>
    </Paper>
  );
};

export default SearchToggle;
