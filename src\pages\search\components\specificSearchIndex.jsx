import { useMediaQuery } from 'react-responsive'
import SpecificSearchMobile from './specificSearchMobile';
import SpecificSearcWeb from './specificSearchWeb';
import MasterLayout from '../../../components/layouts/masterLayout';

function SpecificSearchIndex() {
    const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' })
    return (
        <MasterLayout>
            {isTabletOrMobile ? <SpecificSearchMobile /> : <SpecificSearcWeb />}
        </MasterLayout>
    );
}

export default SpecificSearchIndex;