import { useEffect, useState, useRef } from "react";
import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import { useMediaQuery } from "react-responsive";
import { useSearchCarLogic } from "../api/speceficSearchCarLogic"; // Import the logic
import { Autocomplete, IconButton, Fab } from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import CircularProgress from "@mui/material/CircularProgress";
import InputAdornment from "@mui/material/InputAdornment";
import CloseIcon from "@mui/icons-material/Close";
import SpecificAdvancedSearchModal from "../modals/specificAdvancedSearchModalWeb";
import ConfirmModalCloseDialog from "../../genericSearch/modal/ConfirmModalCloseDialog";
import { useDispatch } from "react-redux";
import { callComponentActionApi } from "../../../util/callComponenetActionApi";
import SpeechNotRecognizedDialog from "../../search/modals/SpeechNotRecognizedDialog";
import MicIcon from '@mui/icons-material/Mic';
import SearchToggle from "./SearchToggle";
import { useGlobalTutorialModal } from "../../../context/TutorialModalContext";
import { hasSeenTutorial, shouldShowTutorial } from "../../../context/TutorialStorage";
import { ImageBaseURL } from "../../../config";
import FloatingAiBot from "../../../context/FloatingAiBot";
import AiChatModal from "../../../context/AiChatContexts/components/AiChatModal";
import UseAiChatApi from "../../../context/AiChatContexts/api/aiChatApi";


const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const labelStyle = {
  color: "#4891FF",
};

const CustomTextField = styled(TextField)({
  "& .MuiInputLabel-root": labelStyle, // Apply label style
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "semibold", // Bold the input field value
    },
  },
});

const CustomAutocomplete = styled(Autocomplete)({
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "semibold", // Bold the selected option value
    },
  },
  "& .MuiAutocomplete-option": {
    fontWeight: "semibold", // Bold the dropdown options
  },
});

const StyledButton = styled(({ ...other }) => <Button {...other} />)(
  ({ isBigScreen }) => ({
    borderRadius: "10px",
    width: isBigScreen ? "20%" : "25%",
    fontWeight: "600",
    fontSize: "16px",
    padding: "15px",
    backgroundColor: "#4891FF",
    color: "#fff",

    // Hover state
    "&:hover": {
      backgroundColor: "#357AE8",
    },

    // Disabled state
    "&.Mui-disabled": {
      backgroundColor: "#4891FF",
      color: "#fff",
      opacity: 0.6,
      cursor: "not-allowed",
    },
  })
);

const SpecificSearchCarWeb = () => {
  const token = localStorage.getItem("token");
  const isBigScreen = useMediaQuery({ query: "(min-width: 2560px)" });
  const {
    distance,
    carBrand,
    carModel,
    carBrandOptions,
    carModelOptions,
    zipCode,
    loadingBrand,
    loadingModel,
    isBrandSelected,
    errorMessage,
    isButtonDisabled,
    loadingUser,
    handleBrandChange,
    handleModelChange,
    handleDistanceChange,
    handleZipCodeChange,
    handleArrowButtonClick,
    distanceOptions,
    cuLogo,
    clearZipCode,
    purchasePower,
    maxApprovalAmount,
    userCarLookingFor,
    vehicleTrimOptions,
    handleTrimsChange,
    handleExteriorColorChange,
    handleInteriorColorChange,
    handleFuelTypeChange,
    selectedTrims,
    selectedExteriorColors,
    selectedInteriorColors,
    selectedFuelTypes,
    selectedModelId,
    setSelectedTrims,
    setSelectedInteriorColors,
    setSelectedFuelTypes,
    setSelectedExteriorColors,
    loadingLocation,
    CarLookingOptions,
    handleCarLookingChange,
    invVehicleMinPrice,
    priceValue,
    setpriceValue,
    minMaxPrice,
    setMinMaxPrice,
    yearValue,
    setYearValue,
    isModalButtonDisabled,
    setIsModalButtonDisabled,
    minInput,
    setMinInput,
    maxInput,
    setMaxInput,
    minPriceError,
    setMinPriceError,
    maxPriceError,
    setMaxPriceError,
    timeoutId,
    setTimeoutId,
    minYearInput,
    setMinYearInput,
    maxYearInput,
    setMaxYearInput,
    minYearError,
    setMinYearError,
    maxYearError,
    setMaxYearError,
    minYearValue,
    setMinYearValue,
    maxYearValue,
    setMaxYearValue,
    componentMetaData,
    aiHealth,
    aiHealthLoading,
    aiHealthError
  } = useSearchCarLogic(token);

  const { showBot } = UseAiChatApi(); // showBot is triggering the AI Bot only when user details is loaded

  const selectedCarLookingOption =
    CarLookingOptions.find((option) => option.value === userCarLookingFor) ||
    CarLookingOptions.find((option) => option.value === "new or used");
  const [modalOpen, setModalOpen] = useState(false); // State for modal open
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false); // State for confirm dialog
  const [isListening, setIsListening] = useState(false);
  const [isSpeechDialogOpen, setIsSpeechDialogOpen] = useState(false);
  const [dialogText, setDialogText] = useState("");
  const [isFocused, setIsFocused] = useState(false); // Track if the input field is focused
  const [aiQuery, setAiQuery] = useState("");
  const [error, setError] = useState(""); // State for error messages
  const [searchType, setSearchType] = useState("basic");
  const [isVINSearch, setIsVINSearch] = useState("");
  const [isVINError, setIsVINError] = useState(""); // State for error messages
  const [aiModalOpen, setAiModalOpen] = useState(false); // State for AI chat modal

  const formatPrice = (value) => {
    if (value === "") return "";
    return new Intl.NumberFormat("en-US", {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const handleCloseModal = () => {
    const priceData = {
      min: invVehicleMinPrice || 0,
      max: purchasePower || maxApprovalAmount || 0
    };
    const priceValuesMatch =
      Number(priceData.min) === Number(priceValue[0]) &&
      Number(priceData.max) === Number(priceValue[1]);
    const yearData = {
      min: minYearValue,
      max: maxYearValue
    };
    const yearValuesMatch =
      Number(yearData.min) === Number(yearValue[0]) &&
      Number(yearData.max) === Number(yearValue[1]);

    if (
      selectedTrims.length > 0 ||
      selectedExteriorColors.length > 0 ||
      selectedInteriorColors.length > 0 ||
      selectedFuelTypes.length > 0 ||
      !priceValuesMatch ||
      !yearValuesMatch > 0
    ) {
      setIsConfirmDialogOpen(true); // Open the custom dialog
    } else {
      setModalOpen(false);
    }
  };

  const handleConfirmClose = () => {
    const priceData = {
      min: invVehicleMinPrice || 0,
      max: purchasePower || maxApprovalAmount || 0
    };
    setIsConfirmDialogOpen(false);
    setModalOpen(false);
    // Reset all fields
    setSelectedTrims([]);
    setSelectedInteriorColors([]);
    setSelectedFuelTypes([]);
    setSelectedExteriorColors([]);
    setMinYearError("");
    setMaxYearError("");
    setMinPriceError("");
    setMaxPriceError("");
    setMinMaxPrice(priceData);
    setpriceValue([priceData.min, priceData.max]);
    setMinInput(formatPrice(priceData.min));
    setMaxInput(formatPrice(priceData.max));
    setMinYearInput(minYearValue);
    setMaxYearInput(maxYearValue);
    setMinYearValue(minYearValue);
    setMaxYearValue(maxYearValue);
    setYearValue([minYearValue, maxYearValue]);
  };

  const handleCancelClose = () => {
    setIsConfirmDialogOpen(false); // Close the custom dialog without making changes
  };

  // Format price
  const formatAmount = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(Number(amount));
  };

  let price = ""; // Initialize as an empty string

  if (purchasePower) {
    price = formatAmount(purchasePower);
  } else if (maxApprovalAmount) {
    price = formatAmount(maxApprovalAmount);
  }

  const carLookingFor = userCarLookingFor
    ? userCarLookingFor.toLocaleUpperCase()
    : "NEW OR USED";

  const vehiclesText = carLookingFor ? "vehicles" : "Vehicles";

  const searchText = `N.B.: ${carLookingFor} ${vehiclesText} will be searched based on amount ${price}`;

  const aiSearchText = `Search for vehicles with a max amount of ${price} with these features`;

  // Handle advanced search
  const handleAdvancedSearch = () => {
    setModalOpen(true);
  };

  const dispatch = useDispatch();
  useEffect(() => {
    let isMounted = true; // Add a flag to check if the component is still mounted

    if (!loadingUser && isMounted && componentMetaData != null) {
      try {
        const component = componentMetaData.find((comp) => comp.slug === "specific-search" && comp.type === "url");
        if (!component) {
          console.error(`Component with slug "specific-search" not found.`);
          return;
        }
        // Call the API with the component ID
        callComponentActionApi(dispatch, component.id);
      } catch (error) {
        console.error("Error handling button click:", error);
      }
    }

    // Cleanup function to reset the flag when the component is unmounted
    return () => {
      isMounted = false;
    };
  }, [loadingUser, componentMetaData]);

  const handleDialogClose = () => {
    setIsSpeechDialogOpen(false);
    setDialogText("");
  };

  const triggerSearch = () => {
    // Stop the microphone if it is listening
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
      setIsListening(false); // Update state to reflect recognition stopped
    }
    // Construct final query: Prepend aiSearchText if price exists
    const finalQuery = price ? `${aiSearchText} ${aiQuery}` : aiQuery;

    // Trigger the search with the updated query
    handleArrowButtonClick(finalQuery);
  };

  const handleAiQueryChange = (event) => {
    const value = event.target.value;
    setAiQuery(value);
  };

  const handleClear = () => {
    setAiQuery(""); // Clear the text field
    setError(""); // Clear error message
    if (isListening) {
      recognitionRef.current.stop(); // Stop listening if already listening
      setIsListening(false); // Update state to reflect recognition stop
      isManuallyStoppedRef.current = true; // 👈 Mark as manually stopped
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      // Check if there's no error and the input field is not empty
      if (!error && aiQuery.trim()) {
        // Trigger the search or button click action
        triggerSearch();
      }

      if (!isVINError && isVINSearch.trim()) {
        // Trigger the search or button click action
        triggerVINSearch();
      }
    }
  };

  const handleVINChange = (event) => {
    const value = event.target.value;
    setIsVINSearch(value);
  };

  const handleVINClear = () => {
    setIsVINSearch("");
    setIsVINError("");
  };

  const triggerVINSearch = () => {
    handleArrowButtonClick("", isVINSearch);
  };

  const recognitionRef = useRef(null); // Use ref to persist recognition instance across renders
  const isManuallyStoppedRef = useRef(false);

  useEffect(() => {
    if ("SpeechRecognition" in window || "webkitSpeechRecognition" in window) {
      const recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
      recognition.lang = "en-US";
      recognition.continuous = true; // Stops after one result
      recognition.interimResults = false;

      recognition.onstart = () => {
        setIsListening(true); // Update state to reflect recognition start
      };

      recognition.onresult = (event) => {
        let transcript = "";

        for (let i = event.resultIndex; i < event.results.length; i++) {
          if (event.results[i].isFinal) {
            transcript += event.results[i][0].transcript;
          }
        }

        // Append finalized transcript
        if (transcript.trim()) {
          setAiQuery((prev) => (prev + " " + transcript).trim());
        }
      };

      recognition.onerror = (event) => {
        console.error("Speech recognition error:", event.error);
        if (event.error === "service-not-allowed") {
          setIsSpeechDialogOpen(true);
          setDialogText("Your current browser does not support speech to text functionality. Please switch to other browser");
        }
      };

      recognition.onend = () => {
        setIsListening(false);
        // 👇 Restart only if it wasn't manually stopped
        if (!isManuallyStoppedRef.current) {
          recognition.start();
        }
      };

      recognitionRef.current = recognition;
    }

    return () => {
      if (recognitionRef.current) {
        isManuallyStoppedRef.current = true;
        recognitionRef.current.stop();
      }
    };
  }, []);

  const toggleSpeechRecognition = () => {
    if (!("SpeechRecognition" in window || "webkitSpeechRecognition" in window)) {
      setIsSpeechDialogOpen(true);
      setDialogText("Your current browser does not support speech to text functionality. Please switch to supported browser like Chrome, Edge, or Safari.");
      return;
    }

    const recognition = recognitionRef.current;

    if (!recognition) {
      console.error("SpeechRecognition is not initialized.");
      return;
    }

    if (isListening) {
      isManuallyStoppedRef.current = true; // 👈 Mark as manually stopped
      recognition.stop();
    } else {
      isManuallyStoppedRef.current = false; // 👈 Allow auto-restart
      recognition.start();
    }
  };

  // for tutorial
  const { openTutorialModal } = useGlobalTutorialModal();
  useEffect(() => {
    const checkAndShowTutorial = async () => {

      const localFlag = hasSeenTutorial("search_vehicles");
      if (localFlag) return; // Already seen locally, skip

      const shouldShow = await shouldShowTutorial({
        tutorialKey: "search_vehicles",
        dispatch,
      });

      if (shouldShow) {
        openTutorialModal({
          bodyImageSrc: ImageBaseURL + "tutorials/images/search-tutorial-feature-image.png",
          additionalTopText: (
            <span>Search then</span>
          ),
          mainText: (
            <span>Swipe Right</span>
          ),
          subText: (
            <span>Next click on Garage</span>
          ),
          audioSrc: ImageBaseURL + "tutorials/audios/search-audio.mp3"
        });
      }
    };

    checkAndShowTutorial();
  }, []);
  const handleBotClick = () => {
    setAiModalOpen(true);
  };

  return (
    <Box sx={{ flexGrow: 1, mt: isBigScreen ? 40 : 10 }}>
      <Grid container spacing={1} sx={{ padding: "100px 0px" }}>
        <Grid item sm={12}>
          <Item className="page_bg" sx={{ marginTop: isBigScreen ? -20 : -10 }}>
            {" "}
            {/* Adjust margin to move text upwards */}
            <Typography variant="h5" component="h5">
              <b>Search Best Vehicles in Your Price Range</b>
            </Typography>
          </Item>
        </Grid>
        <Grid
          container
          item
          xs={12}
          md={6}
          direction="column"
          alignItems="center"
          justifyContent="center"
        >
          <Item
            className="page_bg"
            sx={{ padding: "100px 0px" }}
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {!loadingUser ? (
              <img
                alt="CuLogo"
                src={cuLogo}
                style={{ maxWidth: "60%", maxHeight: "60%" }}
              />
            ) : (
              <CircularProgress sx={{ color: "#4891FF" }} />
            )}
            <Typography
              variant="h6"
              component="h6"
              sx={{ padding: "20px 0px" }}
            >
              <b>Preferred Dealerships Featured Inventory</b>
            </Typography>
          </Item>
        </Grid>
        <Grid
          container
          item
          xs={12}
          md={6}
          direction="column"
          alignItems="center"
          justifyContent="center"
        >
          <Grid container spacing={2} direction="column" alignItems="center">
            <Grid item xs={12} style={{ width: "100%", paddingTop: "0px" }}>
              <Item
                style={{
                  display: "flex",
                  justifyContent: "center",
                  background: "transparent",
                }}
              >
                <SearchToggle onSearchTypeChange={setSearchType} />
              </Item>
              {searchType === "voice" && (
                <>
                  {/* Display aiSearchText if price is available */}
                  {price && (
                    <Item>
                      <Typography variant="body2" component="body2">
                        <span
                          style={{
                            backgroundColor: "#FFFACD",
                            fontWeight: "600",
                          }}
                        >
                          &nbsp;&nbsp;&nbsp;&nbsp;
                          {aiSearchText}
                          &nbsp;&nbsp;&nbsp;&nbsp;
                        </span>
                      </Typography>
                    </Item>
                  )}
                  <Item
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      width: "100%",
                    }}
                  >
                    {/* First Row: TextField & Mic Button */}
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 2, // Space between TextField and button
                        width: isBigScreen ? "50%" : "70%",
                        justifyContent: "center",
                        padding: "10px",
                      }}
                    >
                      <CustomTextField
                        id="outlined-basic"
                        label="Enter your preferences"
                        placeholder={
                          isFocused || aiQuery ? "Enter your preferences" : ""
                        }
                        variant="outlined"
                        autoComplete="off"
                        value={aiQuery}
                        onChange={handleAiQueryChange}
                        error={!!error}
                        helperText={error}
                        onFocus={() => setIsFocused(true)}
                        onKeyDown={handleKeyDown}
                        sx={{
                          flexGrow: 1,
                          background: "#ffffff",
                        }}
                        InputLabelProps={{
                          sx: {
                            fontFamily: "inherit",
                            fontSize: "16px",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              {loadingLocation ? (
                                <CircularProgress
                                  sx={{ color: "#4891FF" }}
                                  size={20}
                                />
                              ) : (
                                aiQuery !== "" && (
                                  <IconButton onClick={handleClear}>
                                    <CloseIcon />
                                  </IconButton>
                                )
                              )}
                            </InputAdornment>
                          ),
                        }}
                      />

                      <Fab
                        id="fab-button"
                        variant="circular"
                        onClick={toggleSpeechRecognition}
                        className={isListening ? "fab-listening" : ""}
                        sx={{
                          background: "#4891FF",
                          color: "#fff",
                          "&:hover": {
                            background: "#357AE8",
                          },
                          boxShadow: "none",
                        }}
                      >
                        <MicIcon fontSize="medium" />
                      </Fab>
                    </Box>

                    {/* Second Row: Search Button */}
                    <StyledButton
                      variant="contained"
                      onClick={triggerSearch}
                      disabled={!!error || !aiQuery?.trim()}
                      startIcon={<SearchIcon fontSize="x-large" />}
                      sx={{ marginTop: "10px", textAlign: "center" }}
                    >
                      Search
                    </StyledButton>
                  </Item>
                </>
              )}

              {searchType === "vin" && (
                <Item
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    width: "100%",
                  }}
                >
                  {/* First Row: TextField */}
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      width: isBigScreen ? "50%" : "70%",
                      justifyContent: "center",
                      padding: "10px",
                    }}
                  >
                    <CustomTextField
                      id="outlined-basic"
                      label="Search by Your Desired VIN"
                      placeholder={
                        isFocused || isVINSearch
                          ? "Search by Your Desired VIN"
                          : ""
                      }
                      variant="outlined"
                      autoComplete="off"
                      value={isVINSearch}
                      onChange={handleVINChange}
                      error={!!isVINError}
                      helperText={isVINError}
                      onFocus={() => setIsFocused(true)}
                      onKeyDown={handleKeyDown}
                      sx={{
                        flexGrow: 1,
                        background: "#ffffff",
                      }}
                      InputLabelProps={{
                        sx: {
                          fontFamily: "inherit",
                          fontSize: "16px",
                        },
                      }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            {loadingLocation ? (
                              <CircularProgress
                                sx={{ color: "#4891FF" }}
                                size={20}
                              />
                            ) : (
                              isVINSearch !== "" && (
                                <IconButton onClick={handleVINClear}>
                                  <CloseIcon />
                                </IconButton>
                              )
                            )}
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                  {/* Second Row: Search Button */}
                  <StyledButton
                    variant="contained"
                    onClick={triggerVINSearch}
                    disabled={!!isVINError || !isVINSearch?.trim()}
                    startIcon={<SearchIcon />}
                    sx={{ marginTop: "10px", textAlign: "center" }}
                  >
                    Search
                  </StyledButton>
                </Item>
              )}

              {searchType === "basic" && (
                <>
                  <Item
                    style={{ display: "flex", justifyContent: "center" }}
                    className="page_bg"
                  >
                    <CustomAutocomplete
                      value={selectedCarLookingOption}
                      id="distance-select"
                      variant="outlined"
                      sx={{ width: isBigScreen ? "50%" : "70%" }}
                      disableClearable
                      onChange={handleCarLookingChange}
                      options={CarLookingOptions}
                      autoHighlight
                      getOptionLabel={(option) => option.label}
                      style={{ borderColor: "#4891FF", background: "#ffffff" }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Vehicle Looking For"
                          InputLabelProps={{
                            classes: {
                              root: "input_fonts",
                            },
                          }}
                          InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                              <>
                                {loadingUser ? (
                                  <CircularProgress
                                    sx={{ color: "#4891FF" }}
                                    size={20}
                                  />
                                ) : null}
                                {params.InputProps.endAdornment}
                              </>
                            ),
                          }}
                        />
                      )}
                    />
                  </Item>
                  <Item
                    className="page_bg"
                    style={{ display: "flex", justifyContent: "center" }}
                  >
                    <CustomAutocomplete
                      options={carBrandOptions}
                      sx={{
                        width: isBigScreen ? "50%" : "70%",
                        background: "#ffffff",
                      }}
                      value={
                        carBrandOptions.find(
                          (option) => option.make === carBrand
                        ) || null
                      } // Ensure this is finding the correct brand
                      onChange={handleBrandChange}
                      getOptionLabel={(option) => option.make}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Select Make"
                          InputLabelProps={{
                            classes: {
                              root: "input_fonts",
                            },
                            sx: {
                              color: "#4891FF",
                            },
                          }}
                          InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                              <>
                                {loadingBrand ? (
                                  <CircularProgress
                                    sx={{ color: "#4891FF" }}
                                    size={20}
                                  />
                                ) : null}
                                {params.InputProps.endAdornment}
                              </>
                            ),
                          }}
                        />
                      )}
                    />
                  </Item>
                  <Item
                    className="page_bg"
                    style={{
                      display: "flex",
                      justifyContent: "center",
                      paddingBottom: 0,
                    }}
                  >
                    <p
                      className="input_fonts_disabled"
                      style={{ display: !isBrandSelected ? "block" : "none" }}
                    >
                      N.B.: Please Select a Make first to View the Vehicle Model
                      List
                    </p>
                  </Item>
                  <Item
                    className="page_bg"
                    style={{
                      display: "flex",
                      justifyContent: "center",
                      paddingTop: 0,
                    }}
                  >
                    <CustomAutocomplete
                      id="model-select"
                      sx={{
                        width: isBigScreen ? "50%" : "70%",
                        background: "#ffffff",
                      }}
                      options={carModelOptions} // Use filtered model options here
                      autoHighlight
                      onChange={handleModelChange}
                      value={
                        carModelOptions.find(
                          (option) => option.model === carModel
                        ) || null
                      }
                      disabled={!isBrandSelected} // Disable if no brand is selected
                      title={
                        !isBrandSelected
                          ? "Please Select a Vehicle Make first"
                          : ""
                      }
                      getOptionLabel={(option) => option.model}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Select Model"
                          InputLabelProps={{
                            classes: {
                              root: !isBrandSelected
                                ? "input_fonts_disabled"
                                : "input_fonts",
                            },
                          }}
                          InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                              <>
                                {loadingModel ? (
                                  <CircularProgress
                                    sx={{ color: "#4891FF" }}
                                    size={20}
                                  />
                                ) : null}
                                {params.InputProps.endAdornment}
                              </>
                            ),
                          }}
                        />
                      )}
                    />
                  </Item>
                  <Item
                    style={{ display: "flex", justifyContent: "center" }}
                    className="page_bg"
                  >
                    <CustomAutocomplete
                      value={{ label: `${distance} miles`, value: distance }}
                      id="distance-select"
                      variant="outlined"
                      sx={{ width: isBigScreen ? "50%" : "70%" }}
                      disableClearable
                      onChange={handleDistanceChange}
                      options={distanceOptions}
                      autoHighlight
                      getOptionLabel={(option) => option.label}
                      style={{ borderColor: "#4891FF", background: "#ffffff" }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Select Distance"
                          InputLabelProps={{
                            classes: {
                              root: "input_fonts",
                            },
                          }}
                        />
                      )}
                    />
                  </Item>
                  <Item className="page_bg">
                    <CustomTextField
                      id="outlined-basic"
                      label="Zip Code"
                      variant="outlined"
                      sx={{
                        width: isBigScreen ? "50%" : "70%",
                        background: "#ffffff",
                      }}
                      autoComplete="off"
                      value={zipCode}
                      onChange={handleZipCodeChange}
                      InputProps={{
                        sx: {
                          fontSize: "16px",
                          fontWeight: "semibold", // Bold the input field value
                        },
                        endAdornment: (
                          <InputAdornment position="end">
                            {loadingLocation ? (
                              <CircularProgress
                                sx={{ color: "#4891FF" }}
                                size={20}
                              />
                            ) : (
                              <>
                                {zipCode !== "" && !loadingLocation && (
                                  <CloseIcon
                                    onClick={clearZipCode}
                                    style={{ cursor: "pointer" }}
                                  />
                                )}
                              </>
                            )}
                          </InputAdornment>
                        ),
                      }}
                    />
                    {errorMessage && (
                      <div style={{ color: "red", marginTop: "5px" }}>
                        {errorMessage}
                      </div>
                    )}
                  </Item>
                  {(price || carLookingFor) && (
                    <Item>
                      <Typography variant="body2" component="body2">
                        <span
                          style={{
                            backgroundColor: "#FFFACD",
                            fontWeight: "600",
                          }}
                        >
                          &nbsp;&nbsp;&nbsp;&nbsp;
                          {searchText}
                          &nbsp;&nbsp;&nbsp;&nbsp;
                        </span>
                      </Typography>
                    </Item>
                  )}
                  {!isButtonDisabled && (
                    <Item>
                      <span
                        className="fs-6 fw-bold btn-link"
                        style={{ color: "#4891FF", cursor: "pointer" }}
                        onClick={() => handleAdvancedSearch()}
                      >
                        Advanced Search
                      </span>
                    </Item>
                  )}
                  <Item className="page_bg">
                    <StyledButton
                      variant="contained"
                      onClick={handleArrowButtonClick}
                      disabled={isButtonDisabled}
                      isBigScreen={isBigScreen}
                      startIcon={<SearchIcon fontSize="x-large" />}
                    >
                      Search
                    </StyledButton>
                  </Item>
                </>
              )}
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      <SpecificAdvancedSearchModal
        open={modalOpen}
        onClose={handleCloseModal}
        vehicleTrimOptions={vehicleTrimOptions}
        handleArrowButtonClick={handleArrowButtonClick}
        handleTrimsChange={handleTrimsChange}
        handleExteriorColorChange={handleExteriorColorChange}
        handleInteriorColorChange={handleInteriorColorChange}
        handleFuelTypeChange={handleFuelTypeChange}
        selectedModelId={selectedModelId}
        handleModelChange={handleModelChange}
        carModelOptions={carModelOptions}
        isBrandSelected={isBrandSelected}
        loadingModel={loadingModel}
        carModel={carModel}
        carBrand={carBrand}
        carBrandOptions={carBrandOptions}
        loadingBrand={loadingBrand}
        handleBrandChange={handleBrandChange}
        distanceOptions={distanceOptions}
        handleDistanceChange={handleDistanceChange}
        distance={distance}
        zipCode={zipCode}
        handleZipCodeChange={handleZipCodeChange}
        loadingUser={loadingUser}
        clearZipCode={clearZipCode}
        errorMessage={errorMessage}
        isButtonDisabled={isButtonDisabled}
        userMaxPrice={purchasePower || maxApprovalAmount}
        invVehicleMinPrice={invVehicleMinPrice}
        priceValue={priceValue}
        setpriceValue={setpriceValue}
        minMaxPrice={minMaxPrice}
        setMinMaxPrice={setMinMaxPrice}
        yearValue={yearValue}
        setYearValue={setYearValue}
        isModalButtonDisabled={isModalButtonDisabled}
        setIsModalButtonDisabled={setIsModalButtonDisabled}
        minInput={minInput}
        setMinInput={setMinInput}
        maxInput={maxInput}
        setMaxInput={setMaxInput}
        minPriceError={minPriceError}
        setMinPriceError={setMinPriceError}
        maxPriceError={maxPriceError}
        setMaxPriceError={setMaxPriceError}
        timeoutId={timeoutId}
        setTimeoutId={setTimeoutId}
        minYearInput={minYearInput}
        setMinYearInput={setMinYearInput}
        maxYearInput={maxYearInput}
        setMaxYearInput={setMaxYearInput}
        minYearError={minYearError}
        setMinYearError={setMinYearError}
        maxYearError={maxYearError}
        setMaxYearError={setMaxYearError}
        minYearValue={minYearValue}
        setMinYearValue={setMinYearValue}
        maxYearValue={maxYearValue}
        setMaxYearValue={setMaxYearValue}
        loadingLocation={loadingLocation}
        selectedTrims={selectedTrims}
      />
      <ConfirmModalCloseDialog
        open={isConfirmDialogOpen}
        onClose={handleCancelClose}
        onConfirm={handleConfirmClose}
      />
      <SpeechNotRecognizedDialog
        open={isSpeechDialogOpen}
        onClose={handleDialogClose}
        text={dialogText}
      />
      {showBot && !aiModalOpen && !aiHealthLoading && aiHealth?.data?.status === "healthy" && <FloatingAiBot onClick={handleBotClick} />}
      <AiChatModal 
        show={aiModalOpen} 
        onHide={() => setAiModalOpen(false)}
      />
    </Box>
  );
};

export default SpecificSearchCarWeb;
