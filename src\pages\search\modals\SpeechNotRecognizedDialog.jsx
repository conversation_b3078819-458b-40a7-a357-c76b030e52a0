"use client";
import { Button, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle } from '@mui/material';
import PropTypes from "prop-types";

const SpeedNotRecognizedDialog = ({ open, onClose, text }) => {

    return (
      <Dialog
        open={open}
        onClose={onClose}
        disableEnforceFocus
        disableScrollLock
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          Speech-to-Text Not Supported
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            { text }
          </DialogContentText>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "center",
          }}
        >
          <Button
            onClick={onClose}
            sx={{
              textTransform: "none", // Prevents uppercase
              boxShadow: "none", // Removes the shadow
              backgroundColor: "var(--primary-blue)",
              color: "white",
              "&:hover": {
                boxShadow: "none", // Ensures no shadow appears on hover
                backgroundColor: "var(--primary-blue)", // Optional hover effect
              },
            }}
          >
            Dismiss
          </Button>
        </DialogActions>
      </Dialog>
    );
};

SpeedNotRecognizedDialog.propTypes = {
    open: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    text: PropTypes.string.isRequired,
};

export default SpeedNotRecognizedDialog;
