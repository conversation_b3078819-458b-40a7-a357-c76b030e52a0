import PropTypes from "prop-types";
import { useMediaQuery } from "react-responsive";
import {
  Box,
  Button,
  TextField,
  Autocomplete,
  Checkbox,
  CircularProgress,
  InputAdornment
} from "@mui/material";
import { Modal } from "react-bootstrap";
import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import SearchIcon from "@mui/icons-material/Search";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import LensIcon from "@mui/icons-material/Lens";
import CloseIcon from "@mui/icons-material/Close";
import { VehicleColors, FuelTypes } from "../../../config";
import Slider from "@mui/material/Slider";
import { useEffect } from "react";
import Typography from "@mui/material/Typography";

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));
const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;
const enabledLabelStyle = {
  color: "#4891FF", // Blue color for enabled state
};

const disabledLabelStyle = {
  color: "#C0C0C0", // Grey color for disabled state
};

const CustomTextField = styled(TextField)(({ disabled }) => ({
  "& .MuiInputLabel-root": disabled ? disabledLabelStyle : enabledLabelStyle,
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "semibold",
    },
  },
  "& .MuiSvgIcon-root": {
    color: disabled ? "#C0C0C0" : "#4891FF",
  },
}));
const CustomAutocomplete = styled(Autocomplete)(({ disabled }) => ({
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#4891FF",
      borderWidth: "2px",
    },
    "&:hover fieldset": {
      borderColor: "#4891FF",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4891FF",
    },
    "& input": {
      fontWeight: "semibold",
    },
  },
  "& .MuiAutocomplete-option": {
    fontWeight: "semibold",
  },
  "& .MuiSvgIcon-root": {
    color: disabled ? "#C0C0C0" : "#4891FF",
  },
}));

const StyledButton = styled(({ ...other }) => <Button {...other} />)(
  ({ isMobile }) => ({
    borderRadius: "10px",
    width: isMobile ? "40%" : "35%",
    fontWeight: "600",
    fontSize: "12px",
    padding: "12px",
    backgroundColor: "#4891FF",
    color: "#fff",

    "&:hover": {
      backgroundColor: "#357AE8",
    },

    "&.Mui-disabled": {
      backgroundColor: "#4891FF",
      color: "#fff",
      opacity: 0.6,
      cursor: "not-allowed",
    },
  })
);

const SpecificAdvancedSearchModalMobile = ({
  open,
  onClose,
  vehicleTrimOptions,
  handleArrowButtonClick,
  handleTrimsChange,
  handleExteriorColorChange,
  handleInteriorColorChange,
  handleFuelTypeChange,
  selectedModelId,
  handleModelChange,
  handleBrandChange,
  carModelOptions,
  isBrandSelected,
  loadingModel,
  carModel,
  carBrand,
  carBrandOptions,
  loadingBrand,
  distanceOptions,
  handleDistanceChange,
  distance,
  zipCode,
  handleZipCodeChange,
  loadingLocation,
  clearZipCode,
  errorMessage,
  userMaxPrice,
  invVehicleMinPrice,
  priceValue,
  setpriceValue,
  minMaxPrice,
  setMinMaxPrice,
  yearValue,
  setYearValue,
  minYear,
  maxYear,
  isButtonDisabled,
  setIsModalButtonDisabled,
  minInput,
  setMinInput,
  maxInput,
  setMaxInput,
  minPriceError,
  setMinPriceError,
  maxPriceError,
  setMaxPriceError,
  timeoutId,
  setTimeoutId,
  minYearInput,
  setMinYearInput,
  maxYearInput,
  setMaxYearInput,
  minYearError,
  setMinYearError,
  maxYearError,
  setMaxYearError,
  minYearValue,
  maxYearValue,
  selectedTrims
}) => {
  const isBigScreen = useMediaQuery({ query: "(min-width: 2560px)" });

  const filteredVehicleTrimOptions = vehicleTrimOptions.filter(
    (option) =>
      option.trim &&
      option.trim !== "NULL" &&
      option.trim !== null &&
      option.trim !== " "
  );

  // Filter vehicle trim options based on selected model
  const filteredTrimOptions = selectedModelId
    ? filteredVehicleTrimOptions.filter(
      (option) => option.model_id === selectedModelId
    )
    : filteredVehicleTrimOptions;

  const isNumeric = (value) => {
    return /^-?\d+(\.\d+)?$/.test(value); // Regular expression for numeric values (including decimals)
  };

  // Price Slider //
  useEffect(() => {
    const getPriceRange = () => {
      const data = {
        min: Number(invVehicleMinPrice) || 0, // Ensure it's a number
        max: Number(userMaxPrice) || 0, // Ensure it's a number
      };
      setMinMaxPrice(data);
      setpriceValue([data.min, data.max]);
      setMinInput(formatPrice(data.min)); // Format with commas
      setMaxInput(formatPrice(data.max)); // Format with commas
    };

    if (invVehicleMinPrice && userMaxPrice) {
      getPriceRange();
    }
  }, [
    invVehicleMinPrice,
    userMaxPrice,
    setMinMaxPrice,
    setpriceValue,
    setMinInput,
    setMaxInput,
  ]);

  const handlePriceChange = (event, newValue) => {
    if (!isNaN(newValue[0]) && !isNaN(newValue[1])) {
      // Ensure both values are valid numbers
      setpriceValue(newValue);
      if(newValue[0] > newValue[1]){
        setMinPriceError("");
        setMaxPriceError("");
        setIsModalButtonDisabled(false);
      }else if(newValue[0] < newValue[1]){
        setMaxPriceError("");
        setMinPriceError("");
        setIsModalButtonDisabled(false);
      }
      setMinInput(formatPrice(newValue[0])); // Format with commas
      setMaxInput(formatPrice(newValue[1])); // Format with commas
    }
  };

  // Utility function to format the price with commas
  const formatPrice = (value) => {
    if (value === "") return "";
    return new Intl.NumberFormat("en-US", {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Remove commas when the user is editing
  const removeFormatting = (value) => value.replace(/,/g, "");

  const setCaretPosition = (input, position) => {
    if (input.setSelectionRange) {
      input.focus();
      input.setSelectionRange(position, position);
    }
  };
  // Handle min price input change
  const handleMinChange = (event) => {
    const inputField = event.target;
    const rawValue = removeFormatting(inputField.value); // Remove commas
    const startCaretPosition = inputField.selectionStart; // Capture cursor position

    // Track whether the user pressed backspace
    const isBackspace = event.nativeEvent.inputType === "deleteContentBackward";

    // If input is invalid, show an error and stop further processing
    if (!isPriceNumeric(rawValue) && rawValue !== "") {
      setMinPriceError("Please enter a valid numeric value.");
      return;
    } else {
      setMinPriceError(""); // Clear error if valid
    }

    // Temporarily set the input field value without formatting
    setMinInput(rawValue);

    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    const newTimeoutId = setTimeout(() => {
      if (!isNaN(parseFloat(rawValue)) && rawValue !== "") {
        const newMin = parseFloat(rawValue);

        if (newMin >= minMaxPrice.min && newMin <= priceValue[1]) {
          setpriceValue([newMin, priceValue[1]]);
          const formattedValue = formatPrice(newMin);
          setMinInput(formattedValue); // Apply formatting after edit

          // Calculate the new caret position after formatting
          let newCaretPosition = startCaretPosition;
          const commasBeforeCaret = (
            inputField.value.substring(0, startCaretPosition).match(/,/g) || []
          ).length;
          const commasAfterFormatting = (
            formattedValue.substring(0, startCaretPosition).match(/,/g) || []
          ).length;
          const caretOffset = commasAfterFormatting - commasBeforeCaret;

          // Adjust the caret position for backspace
          if (isBackspace) {
            newCaretPosition = startCaretPosition - 1 + caretOffset;
          } else {
            newCaretPosition = startCaretPosition + caretOffset;
          }

          setTimeout(() => setCaretPosition(inputField, newCaretPosition), 0); // Restore adjusted cursor position
          setIsModalButtonDisabled(false);
        } else {
          setMinPriceError(
            `Please enter a value between ${formatPrice(
              priceValue[0]
            )} and ${formatPrice(priceValue[1])}`
          );
          setIsModalButtonDisabled(true);
        }
      }
    }, 500);

    setTimeoutId(newTimeoutId);
  };

  // Handle max price input change
  const handleMaxChange = (event) => {
    const inputField = event.target;
    const rawValue = removeFormatting(inputField.value); // Remove commas
    const startCaretPosition = inputField.selectionStart; // Capture cursor position

    // Track whether the user pressed backspace
    const isBackspace = event.nativeEvent.inputType === "deleteContentBackward";

    // If input is invalid, show an error and stop further processing
    if (!isPriceNumeric(rawValue) && rawValue !== "") {
      setMaxPriceError("Please enter a valid numeric value.");
      return;
    } else {
      setMaxPriceError(""); // Clear error if valid
    }

    // Temporarily set the input field value without formatting
    setMaxInput(rawValue);

    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    const newTimeoutId = setTimeout(() => {
      const newMax = parseFloat(rawValue);

      if (!isNaN(newMax)) {
        if (newMax <= minMaxPrice.max && newMax >= priceValue[0]) {
          setpriceValue([priceValue[0], newMax]);
          const formattedValue = formatPrice(newMax); // Apply formatting after the edit
          setMaxInput(formattedValue); // Set formatted value

          // Calculate the new caret position after formatting
          let newCaretPosition = startCaretPosition;
          const commasBeforeCaret = (
            inputField.value.substring(0, startCaretPosition).match(/,/g) || []
          ).length;
          const commasAfterFormatting = (
            formattedValue.substring(0, startCaretPosition).match(/,/g) || []
          ).length;
          const caretOffset = commasAfterFormatting - commasBeforeCaret;

          // Adjust the caret position for backspace
          if (isBackspace) {
            newCaretPosition = startCaretPosition - 1 + caretOffset;
          } else {
            newCaretPosition = startCaretPosition + caretOffset;
          }

          setTimeout(() => setCaretPosition(inputField, newCaretPosition), 0); // Restore adjusted cursor position
          setMaxPriceError("");
          setIsModalButtonDisabled(false);
        } else if(newMax <= priceValue[0]){
          setMaxPriceError(
            `Max value should be greater than ${formatPrice(priceValue[0])}`
          );
          setIsModalButtonDisabled(true);
        } else {
          setMaxPriceError(
            `The value entered is greater than the approved amount from your Credit Union.`
          );
          setIsModalButtonDisabled(true);
        }
      }
    }, 500);

    setTimeoutId(newTimeoutId);
  };

  // Function to check if the input is a valid numeric value (supports partial inputs)
  const isPriceNumeric = (value) => {
    return /^\d*$/.test(value);
  };

  // Price Slider End //

  // Year Slider //
  const handleYearChange = (event, newValue) => {
    setYearValue(newValue);
    setMinYearInput(newValue[0]);
    setMaxYearInput(newValue[1]);
  };

  const handleMinYearChange = (event) => {
    const newMin = Number(event.target.value); // Get the user input as a number
    if (!isNumeric(newMin) && newMin !== "") {
      setMinYearError("Please enter a valid numeric value.");
      return; // If not a number, do not set the input
    } else {
      setMinYearError(""); // Clear error if valid
    }

    setMinYearInput(event.target.value); // Update the input field immediately

    // Check if newMin is within valid range and less than or equal to the current max
    if (newMin < minYear || newMin > maxYear) {
      setMinYearError(`Please Select a value between ${minYear} and ${maxYear}`);
      setIsModalButtonDisabled(true);
    } else if (newMin > yearValue[1]) {
      setMinYearError(`Please Select a value less than or equal to ${yearValue[1]}`);
      setIsModalButtonDisabled(true);
    } else {
      setMinYearError(""); // Clear the error message
      setYearValue([newMin, yearValue[1]]); // Update the slider value
      setIsModalButtonDisabled(false);
    }
  };

  const handleMaxYearChange = (event) => {
    const newMax = Number(event.target.value); // Get the user input as a number
    if (!isNumeric(newMax) && newMax !== "") {
      setMaxYearError("Please enter a valid numeric value.");
      return; // If not a number, do not set the input
    } else {
      setMaxYearError(""); // Clear error if valid
    }

    setMaxYearInput(event.target.value); // Update the input field immediately

    if (newMax > maxYear || newMax < minYear) {
      setMaxYearError(`Please Select a value between ${minYear} and ${maxYear}`);
      setIsModalButtonDisabled(true);
    } else if (newMax < yearValue[0]) {
      setMaxYearError(`Please Select a value greater than or equal to ${yearValue[0]}`);
      setIsModalButtonDisabled(true);
    } else {
      setMaxYearError(""); // Clear the error message
      setYearValue([yearValue[0], newMax]); // Update the slider value
      setIsModalButtonDisabled(false);
    }
  };

  // Year Slider End//

  return (
    <Modal show={open} onHide={onClose} centered size="lg">
      <Modal.Header className="modal-header-fixed" closeButton>
        <Modal.Title>Advanced Search</Modal.Title>
      </Modal.Header>
      <Modal.Body className="modal-body-scrollable">
        <Box sx={{ flexGrow: 1 }}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Item
                style={{ display: "flex", justifyContent: "center" }}
                className="page_bg"
              >
                <CustomAutocomplete
                  value={{ label: `${distance} miles`, value: distance }}
                  id="distance-select"
                  variant="outlined"
                  fullWidth
                  disableClearable
                  onChange={handleDistanceChange}
                  options={distanceOptions}
                  autoHighlight
                  getOptionLabel={(option) => option.label}
                  style={{ borderColor: "#4891FF", background: "#ffffff" }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Select Distance"
                      InputLabelProps={{
                        classes: {
                          root: "input_fonts",
                        },
                      }}
                    />
                  )}
                />
              </Item>
            </Grid>
            <Grid item xs={12}>
              <Item className="page_bg">
                <CustomTextField
                  id="outlined-basic"
                  label="Zip Code"
                  variant="outlined"
                  fullWidth
                  autoComplete="off"
                  value={zipCode}
                  onChange={handleZipCodeChange}
                  InputProps={{
                    sx: {
                      fontSize: "16px",
                      fontWeight: "semibold", // Bold the input field value
                    },
                    endAdornment: (
                      <InputAdornment position="end">
                        {loadingLocation ? (
                          <CircularProgress
                            sx={{ color: "#4891FF" }}
                            size={20}
                          />
                        ) : (
                          <>
                            {zipCode !== "" && !loadingLocation && (
                              <CloseIcon
                                onClick={clearZipCode}
                                style={{ cursor: "pointer" }}
                              />
                            )}
                          </>
                        )}
                      </InputAdornment>
                    ),
                  }}
                />
                {errorMessage && (
                  <div style={{ color: "red", marginTop: "5px" }}>
                    {errorMessage}
                  </div>
                )}
              </Item>
            </Grid>
            {(!isBrandSelected || carModel == "") && (
              <Grid item xs={12}>
                <Item
                  className="page_bg"
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    paddingBottom: 0,
                  }}
                >
                  {!isBrandSelected && (
                    <p className="input_fonts_disabled">
                      N.B.: Please Select a Make first to View the Vehicle Model
                      List
                    </p>
                  )}
                  {isBrandSelected && carModel == "" && (
                    <p className="input_fonts_disabled">
                      N.B.: Please Select a Model first to View the Vehicle Trim
                      List
                    </p>
                  )}
                </Item>
              </Grid>
            )}
            <Grid item xs={12}>
              <Item className="page_bg">
                <CustomAutocomplete
                  options={carBrandOptions}
                  fullWidth
                  value={carBrandOptions.find(
                    (option) => option.make === carBrand || null
                  )}
                  onChange={handleBrandChange}
                  getOptionLabel={(option) => option.make}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Select Make"
                      InputLabelProps={{
                        classes: {
                          root: "input_fonts",
                        },
                        sx: {
                          fontWeight: "bold",
                          color: "#4891FF",
                        },
                      }}
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {loadingBrand ? (
                              <CircularProgress
                                sx={{ color: "#4891FF" }}
                                size={20}
                              />
                            ) : null}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                      }}
                    />
                  )}
                />
              </Item>
            </Grid>
            <Grid item xs={12}>
              <Item className="page_bg">
                <CustomAutocomplete
                  id="model-select"
                  fullWidth
                  options={carModelOptions} // Use filtered model options here
                  autoHighlight
                  onChange={handleModelChange}
                  value={
                    carModelOptions.find(
                      (option) => option.model === carModel
                    ) || null
                  }
                  disabled={!isBrandSelected} // Disable if no brand is selected
                  title={
                    !isBrandSelected ? "Please Select a Vehicle Make first" : ""
                  }
                  getOptionLabel={(option) => option.model}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Select Model"
                      InputLabelProps={{
                        classes: {
                          root: !isBrandSelected
                            ? "input_fonts_disabled"
                            : "input_fonts",
                        },
                      }}
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {loadingModel ? (
                              <CircularProgress
                                sx={{ color: "#4891FF" }}
                                size={20}
                              />
                            ) : null}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                      }}
                    />
                  )}
                />
              </Item>
            </Grid>
            <Grid item xs={12}>
              <Item className="page_bg">
                <CustomAutocomplete
                  multiple
                  limitTags={1}
                  id="checkboxes-tags-demo"
                  options={filteredTrimOptions}
                  onChange={handleTrimsChange}
                  value={filteredTrimOptions.filter(option => selectedTrims.includes(option.trim))}
                  disabled={carModel == ''}
                  disableCloseOnSelect
                  getOptionLabel={(option) => option.trim}
                  renderOption={(props, option, { selected }) => {
                      const { key, ...optionProps } = props;
                      return (
                          <li key={key} {...optionProps}>
                              <Checkbox
                                  icon={icon}
                                  checkedIcon={checkedIcon}
                                  style={{ marginRight: 8 }}
                                  checked={selected}
                              />
                              {option.trim}
                          </li>
                      );
                  }}
                  renderInput={(params) => (
                      <CustomTextField {...params} label="Select Trim" />
                  )}
                />
              </Item>
            </Grid>
            <Grid item xs={12}>
              <Item className="page_bg">
                <CustomAutocomplete
                  multiple
                  limitTags={1}
                  id="checkboxes-tags-demo"
                  options={VehicleColors}
                  onChange={handleExteriorColorChange}
                  disableCloseOnSelect
                  getOptionLabel={(option) => option.title}
                  renderOption={(props, option, { selected }) => {
                    const { key, ...optionProps } = props;
                    return (
                      <li key={key} {...optionProps}>
                        <Checkbox
                          icon={icon}
                          checkedIcon={checkedIcon}
                          style={{ marginRight: 8 }}
                          checked={selected}
                        />
                        <LensIcon
                          sx={{
                            color: option.colorCode,
                            marginRight: 1,
                            border: "1px solid #D3D3D3",
                            borderRadius: "50%",
                          }}
                        />
                        {option.title}
                      </li>
                    );
                  }}
                  renderInput={(params) => (
                    <CustomTextField
                      {...params}
                      label="Select Exterior Colors"
                    />
                  )}
                />
              </Item>
            </Grid>
            <Grid item xs={12}>
              <Item className="page_bg">
                <CustomAutocomplete
                  multiple
                  limitTags={1}
                  id="checkboxes-tags-demo"
                  options={VehicleColors}
                  onChange={handleInteriorColorChange}
                  disableCloseOnSelect
                  getOptionLabel={(option) => option.title}
                  renderOption={(props, option, { selected }) => {
                    const { key, ...optionProps } = props;
                    return (
                      <li key={key} {...optionProps}>
                        <Checkbox
                          icon={icon}
                          checkedIcon={checkedIcon}
                          style={{ marginRight: 8 }}
                          checked={selected}
                        />
                        <LensIcon
                          sx={{
                            color: option.colorCode,
                            marginRight: 1,
                            border: "1px solid #D3D3D3",
                            borderRadius: "50%",
                          }}
                        />
                        {option.title}
                      </li>
                    );
                  }}
                  renderInput={(params) => (
                    <CustomTextField
                      {...params}
                      label="Select Interior Colors"
                    />
                  )}
                />
              </Item>
            </Grid>
            <Grid item xs={12}>
              <Item className="page_bg">
                <CustomAutocomplete
                  multiple
                  limitTags={1}
                  id="checkboxes-tags-demo"
                  options={FuelTypes}
                  onChange={handleFuelTypeChange}
                  disableCloseOnSelect
                  renderOption={(props, option, { selected }) => {
                    const { key, ...optionProps } = props;
                    return (
                      <li key={key} {...optionProps}>
                        <Checkbox
                          icon={icon}
                          checkedIcon={checkedIcon}
                          style={{ marginRight: 8 }}
                          checked={selected}
                        />
                        {option}
                      </li>
                    );
                  }}
                  renderInput={(params) => (
                    <CustomTextField {...params} label="Select Fuel Types" />
                  )}
                />
              </Item>
            </Grid>
            {/* price range */}
            <Grid item xs={12}>
              <Item className="page_bg">
                <Typography align="left" style={{ fontWeight: "600" }} >
                  Select Price Range
                </Typography>
              </Item>

              {/* Min and Max fields side by side */}
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Item className="page_bg">
                    <CustomTextField
                      label="Min"
                      type="text"
                      value={minInput}
                      onChange={handleMinChange}
                      onFocus={() => setMinInput(removeFormatting(minInput))} // Remove formatting on focus
                      onBlur={() => {
                        // Validate input before applying formatting
                        if (!isNaN(minInput) && minInput.trim() !== "") {
                          setMinInput(formatPrice(Number(minInput))); // Reapply formatting if valid number
                        } else if (minInput.trim() === "") {
                          setMinInput(""); // If input is empty, keep it empty
                        }
                      }}
                      fullWidth
                      variant="outlined"
                      error={Boolean(minPriceError)}
                      helperText={minPriceError}
                      InputProps={{
                        startAdornment: <InputAdornment position="start">$</InputAdornment>,
                      }}
                    />
                  </Item>
                </Grid>

                <Grid item xs={6}>
                  <Item className="page_bg">
                    <CustomTextField
                      label="Max"
                      type="text"
                      value={maxInput}
                      onChange={handleMaxChange}
                      onFocus={() => setMaxInput(removeFormatting(maxInput))} // Remove formatting on focus
                      onBlur={() => {
                        // Validate input before applying formatting
                        if (!isNaN(maxInput) && maxInput.trim() !== "") {
                          setMaxInput(formatPrice(Number(maxInput))); // Reapply formatting if valid number
                        } else if (maxInput.trim() === "") {
                          setMaxInput(""); // If input is empty, keep it empty
                        }
                      }}
                      fullWidth
                      variant="outlined"
                      error={Boolean(maxPriceError)}
                      helperText={maxPriceError}
                      InputProps={{
                        startAdornment: <InputAdornment position="start">$</InputAdornment>,
                      }}
                    />
                  </Item>
                </Grid>
              </Grid>

              <Item className="page_bg">
                <Slider
                  min={minMaxPrice.min}
                  max={minMaxPrice.max}
                  value={priceValue}
                  onChange={handlePriceChange}
                  getAriaValueText={(val) => `${val}`}
                />
              </Item>
            </Grid>
            {/* Model Year Range */}
            <Grid item xs={12}>
              <Item className="page_bg">
                <Typography align="left" style={{ fontWeight: "600" }} >
                  Select Model Year
                </Typography>
              </Item>

              {/* Min and Max fields side by side */}
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Item className="page_bg">
                  <select
                    value={minYearInput}
                    onChange={handleMinYearChange}
                    style={{
                      width: "100%", 
                      padding: "12px", 
                      height: "57px", // Adjust this value to increase height
                      lineHeight: "50px", // Adjust to center-align the text vertically
                      borderRadius: "4px", 
                      border: `2px solid ${minYearError ? "red" : "#4891FF"}`,
                      label: "Min",
                      backgroundColor: "#fff",
                      fontSize: "16px", 
                    }}
                  >
                    {Array.from(
                      { length: Number(maxYearValue) - Number(minYearValue) },
                      (_, index) => Number(minYearValue) + index
                    ).map((year) => (
                      <option
                        key={year}
                        value={year}
                        style={{ color: "#585858", fontSize: "16px", fontWeight: "400" }}
                      >
                        {year}
                      </option>
                    ))}
                  </select>
                    {minYearError && (
                      <Typography color="error" variant="caption">
                        {minYearError}
                      </Typography>
                    )}
                  </Item>
                </Grid>

                <Grid item xs={6}>
                  <Item className="page_bg">
                  <select
                    value={maxYearInput}
                    onChange={handleMaxYearChange}
                    style={{
                      width: "100%", 
                      padding: "12px", 
                      height: "57px", // Adjust this value to increase height
                      lineHeight: "50px", // Adjust to center-align the text vertically
                      borderRadius: "4px", 
                      border: `2px solid ${minYearError ? "red" : "#4891FF"}`,
                      label: "Min",
                      backgroundColor: "#fff",
                      fontSize: "16px", 
                    }}
                  >
                    {Array.from(
                      { length: Number(maxYearValue) - Number(minYearValue) },
                      (_, index) => Number(maxYearValue) - index
                    ).map((year) => (
                      <option
                        key={year}
                        value={year}
                        style={{ color: "#585858", fontSize: "16px", fontWeight: "400" }}
                      >
                        {year}
                      </option>
                    ))}
                  </select>
                    {maxYearError && (
                      <Typography color="error" variant="caption">
                        {maxYearError}
                      </Typography>
                    )}
                  </Item>
                </Grid>
              </Grid>

              <Item className="page_bg">
                <Slider
                  min={Number(minYearValue)}
                  max={Number(maxYearValue)}
                  value={yearValue}
                  onChange={handleYearChange}
                  getAriaValueText={(val) => `${val}`}
                  valueLabelDisplay="auto"
                />
              </Item>
            </Grid>
          </Grid>
        </Box>
      </Modal.Body>
      <Modal.Footer className="modal-footer-fixed">
        <StyledButton
          variant="contained"
          isBigScreen={isBigScreen}
          onClick={handleArrowButtonClick}
          disabled={isButtonDisabled}
        >
          <SearchIcon />
          Search
        </StyledButton>
      </Modal.Footer>
    </Modal>
  );
};

// Update propTypes for SpecificAdvancedSearchModalMobile
SpecificAdvancedSearchModalMobile.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  key: PropTypes.string.isRequired,
  vehicleTrimOptions: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      trim: PropTypes.string.isRequired,
    })
  ).isRequired,
  handleArrowButtonClick: PropTypes.func.isRequired,
  handleTrimsChange: PropTypes.func.isRequired,
  handleExteriorColorChange: PropTypes.func.isRequired,
  handleInteriorColorChange: PropTypes.func.isRequired,
  handleFuelTypeChange: PropTypes.func.isRequired,
  selectedTrims: PropTypes.array.isRequired,
  selectedExteriorColors: PropTypes.array.isRequired,
  selectedInteriorColors: PropTypes.array.isRequired,
  selectedFuelTypes: PropTypes.array.isRequired,
  selectedModelId: PropTypes.string,
  handleModelChange: PropTypes.func.isRequired,
  carModelOptions: PropTypes.arrayOf(
    PropTypes.shape({
      model: PropTypes.string.isRequired,
    })
  ).isRequired,
  distanceOptions: PropTypes.arrayOf(
    PropTypes.shape({
      model: PropTypes.string.isRequired,
    })
  ).isRequired,
  isBrandSelected: PropTypes.bool.isRequired,
  loadingModel: PropTypes.bool.isRequired,
  carModel: PropTypes.string,
  carBrand: PropTypes.string,
  carBrandOptions: PropTypes.arrayOf(
    PropTypes.shape({
      make: PropTypes.string.isRequired,
    })
  ).isRequired,
  handleBrandChange: PropTypes.func.isRequired,
  loadingBrand: PropTypes.bool.isRequired,
  handleDistanceChange: PropTypes.func.isRequired,
  distance: PropTypes.string,
  zipCode: PropTypes.string,
  handleZipCodeChange: PropTypes.func.isRequired,
  loadingLocation: PropTypes.bool.isRequired,
  clearZipCode: PropTypes.func.isRequired,
  errorMessage: PropTypes.string,
  isButtonDisabled: PropTypes.bool.isRequired,
  userMaxPrice: PropTypes.number.isRequired,
  invVehicleMinPrice: PropTypes.number.isRequired,
  priceValue: PropTypes.array.isRequired,
  setpriceValue: PropTypes.func.isRequired,
  minMaxPrice: PropTypes.object.isRequired,
  setMinMaxPrice: PropTypes.func.isRequired,
  yearValue: PropTypes.array.isRequired,
  setYearValue: PropTypes.func.isRequired,
  minYear: PropTypes.number.isRequired,
  maxYear: PropTypes.number.isRequired,
  isModalButtonDisabled: PropTypes.bool.isRequired,
  setIsModalButtonDisabled: PropTypes.func.isRequired,
  minInput: PropTypes.number.isRequired,
  setMinInput: PropTypes.func.isRequired,
  maxInput: PropTypes.number.isRequired,
  setMaxInput: PropTypes.func.isRequired,
  minPriceError: PropTypes.string,
  setMinPriceError: PropTypes.func.isRequired,
  maxPriceError: PropTypes.string,
  setMaxPriceError: PropTypes.func.isRequired,
  timeoutId: PropTypes.number,
  setTimeoutId: PropTypes.func.isRequired,
  minYearInput: PropTypes.number.isRequired,
  setMinYearInput: PropTypes.func.isRequired,
  maxYearInput: PropTypes.number.isRequired,
  setMaxYearInput: PropTypes.func.isRequired,
  minYearError: PropTypes.string,
  setMinYearError: PropTypes.func.isRequired,
  maxYearError: PropTypes.string,
  setMaxYearError: PropTypes.func.isRequired,
  minYearValue: PropTypes.number.isRequired,
  maxYearValue: PropTypes.number.isRequired,
};

export default SpecificAdvancedSearchModalMobile;
