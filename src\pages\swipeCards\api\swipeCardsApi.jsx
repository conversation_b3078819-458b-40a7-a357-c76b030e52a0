import { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import {
  getcarInventory,
  getAISearchCarInventory,
  rightSwipe,
  getVehicleDetailsInventory,
} from "../../../store/apps/car";
import { useNavigate, useLocation } from "react-router-dom";
import { DistanceOptions } from "../../../config";
import { pageMetaData } from "../../../../data/pageMetaData";
import { pageComponentData } from "../../../../data/componentMetaData";
import { callComponentActionApi } from "../../../util/callComponenetActionApi";

export const SwipeCardsApi = () => {
  const [carsInfo, setCarsInfo] = useState([]); // State to store the cars data
  const [distanceOptions, setDistanceOptions] = useState(DistanceOptions || []); // State to store the distance options
  const [totalCarCount, setTotalCarCount] = useState(0); // State to store the total car count
  const [totalShowCount, setTotalShowCount] = useState(0); // State to store the total car count
  const [page] = useState(1); // New state to store the current page
  const [page_size] = useState(import.meta.env.VITE_SWIPE_CARD_PAGE_SIZE); // New state to store the page size
  // Sorting options for price
  const sortingPriceOptions = [
    { label: "Low to High", order: "asc", field: "price" },
    { label: "High to Low", order: "desc", field: "price" },
  ];

  // Get initial sorting options from session storage or use defaults
  const getInitialSortingOptions = () => {
    const storedPriceSorting = sessionStorage.getItem('priceSorting');
    
    // If no stored preferences, clear any existing values and use defaults
    if (!storedPriceSorting) {
      sessionStorage.removeItem('priceSorting');
      return {
        priceSorting: sortingPriceOptions[0]
      };
    }
    
    return {
      priceSorting: storedPriceSorting ? JSON.parse(storedPriceSorting) : sortingPriceOptions[0]
    };
  };

  const initialSorting = getInitialSortingOptions();

  const [selectedPriceSorting, setSelectedPriceSorting] = useState(initialSorting.priceSorting);
  const [tempPriceSorting, setTempPriceSorting] = useState(initialSorting.priceSorting);
  const [dataFlag, setDataFlag] = useState(false); // New state to track data availability
  const [loading, setLoading] = useState(false); // New state to track loading
  const [loadingRightSwipe, setLoadingRightSwipe] = useState(false); // New state to track loading
  const [hasMoreData, setHasMoreData] = useState(true); // New state to track more data availability
  const [error, setError] = useState(null);
  const [totalMcVehicleCount, setTotalMcVehicleCount] = useState(0);
  const dispatch = useDispatch(); // Redux dispatch
  const token = localStorage.getItem("token"); // Get the token
  const location = useLocation(); // Get the location
  const navigate = useNavigate();
  const searchParams = location.state?.params;
  const prevRoute = searchParams?.prevRoute;
  const [dealerList, setDealerList] = useState([]);
  const [searhCriteria, setSearhCriteria] = useState({
    aiQuery: searchParams?.aiQuery || null,
    bodyType: searchParams?.body_type || [],
    vin: searchParams?.vin || null,
    carmodel: searchParams?.carModel || "",
    carbrand: searchParams?.carBrand || [],
    latitude: searchParams?.latitude,
    longitude: searchParams?.longitude,
    trims: searchParams?.trims || [],
    exterior_colors: searchParams?.exterior_colors || [],
    interior_colors: searchParams?.interior_colors || [],
    fuel_types: searchParams?.fuel_types || [],
    distance: searchParams?.distance,
    zip: searchParams?.zipCode,
    page: page,
    page_size: page_size,
    sort_by: initialSorting.priceSorting.field,
    sort_order: initialSorting.priceSorting.order,
    userAvailableAmount:
      searchParams?.user_total_purchase_power == null ||
        searchParams?.user_total_purchase_power === 0
        ? searchParams?.user_pre_aprv_loan_amount
        : searchParams?.user_total_purchase_power,
    userCarLookingOption: searchParams?.user_car_looking_option ?? null,
    addedCarinGarageVins: searchParams?.addedCarinGarageVins ?? [],
    priceRangeValue: searchParams?.priceRangeValue ?? [],
    yearRangeValue: searchParams?.yearRangeValue ?? [],
    yearValuesMatch: searchParams?.yearValuesMatch ?? false,
    priceValuesMatch: searchParams?.priceValuesMatch ?? false,
  });
  const [pageId, setPageId] = useState(null);
  const [componentMetaData, setComponentMetaData] = useState(null); // State to store the componentMetaData

  // Add effect to handle page refresh and initial load
  useEffect(() => {
    const storedPriceSorting = sessionStorage.getItem('priceSorting');
    
    if (storedPriceSorting) {
      // Use the stored sorting preference
      const activeSorting = JSON.parse(storedPriceSorting);
      
      // Update search criteria with stored sorting
      const updatedCriteria = {
        ...searhCriteria,
        sort_by: activeSorting.field,
        sort_order: activeSorting.order
      };
      setSearhCriteria(updatedCriteria);
      
      // Update UI states
      setSelectedPriceSorting(activeSorting);
      setTempPriceSorting(activeSorting);
    } else {
      // Reset to default price ascending if no stored preferences
      setSelectedPriceSorting(sortingPriceOptions[0]);
      setTempPriceSorting(sortingPriceOptions[0]);
      
      // Update search criteria with default sorting
      const updatedCriteria = {
        ...searhCriteria,
        sort_by: sortingPriceOptions[0].field,
        sort_order: sortingPriceOptions[0].order
      };
      setSearhCriteria(updatedCriteria);
    }
  }, []); // Run only on mount

  /**
   * Handle the change of price sorting option
   * @param {object} event The event object
   * @param {object} newValue The new value of the price sorting option
   */
  const handlePriceSortingChange = (event, newValue) => {
    // Only update the temporary value
    setTempPriceSorting(newValue);
  };

  // Function to handle sorting when apply is clicked
  const handleApplySorting = (newPriceSorting) => {
    // Get values from session storage
    const storedPriceSorting = sessionStorage.getItem('priceSorting');
    
    // Use stored values if they exist, otherwise use passed values
    const priceSorting = storedPriceSorting ? JSON.parse(storedPriceSorting) : newPriceSorting;

    // Update the search criteria with the active sorting values
    const updatedCriteria = {
      ...searhCriteria,
      sort_by: priceSorting.field,
      sort_order: priceSorting.order
    };
    
    // Update the state and fetch new data
    setSearhCriteria(updatedCriteria);
    fetchInventory(true, updatedCriteria);
  };

  // Function to fetch the inventory
  const fetchInventory = (reset = false, updatedCriteria) => {
    setLoading(true);

    // Use updatedCriteria if provided, otherwise fallback to the current state
    const criteria = updatedCriteria ? updatedCriteria : searhCriteria;

    const apiCall =
      criteria.aiQuery && criteria.aiQuery.trim() !== ""
        ? getAISearchCarInventory
        : getcarInventory;

    try {
      dispatch(
        apiCall({
          token,
          query: criteria.aiQuery || "",
          vin: criteria.vin || "",
          bodyType: criteria.bodyType || [],
          carmodel: criteria.carmodel || "",
          carbrand: criteria.carbrand || [],
          latitude: criteria.latitude,
          longitude: criteria.longitude,
          trims: criteria.trims || [],
          exterior_colors: criteria.exterior_colors || [],
          interior_colors: criteria.interior_colors || [],
          fuel_types: criteria.fuel_types || [],
          distance: criteria.distance,
          zip: criteria.zip,
          page: criteria.page,
          page_size: criteria.page_size,
          sort_by: criteria.sort_by,
          sort_order: criteria.sort_order,
          userAvailableAmount: criteria.vin ? null : criteria.userAvailableAmount,
          userCarLookingOption: criteria.userCarLookingOption,
          addedCarinGarageVins: criteria.addedCarinGarageVins || [],
          priceRangeValue: criteria.priceRangeValue || [],
          yearRangeValue: criteria.yearRangeValue || [],
          yearValuesMatch: criteria.yearValuesMatch || false,
          priceValuesMatch: criteria.priceValuesMatch || false,
        })
      )
        .then((response) => {
          handleResponse(response, reset);
        })
        .catch((error) => {
          console.error("Error while Fetching:", error);
          setLoading(false);
        });
    } catch (error) {
      console.error("Error while Fetching:", error);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!searchParams) {
      if (token) {
        navigate("/specific-search");
      } else {
        navigate("/send-otp");
      }
    } else {
      // Only fetch inventory after sorting preferences are set
      fetchInventory(true);
    }
  }, [page, searhCriteria.sort_by, searhCriteria.sort_order]);

  // Function to handle the response
  const handleResponse = (response, reset) => {
    if (response && response.payload && response.payload.data) {
      let newData = response.payload.data; // Get the new data
      
      // Create an empty object to store dealers and their vehicle counts
      let dealerVehicleCount = {};

      // Iterate through the `newData` array
      newData.forEach((vehicle) => {
        // Get the dealer name
        let dealerName = vehicle.seller_name;

        // If the dealer is already in the object, increment the count, otherwise set it to 1
        if (dealerVehicleCount[dealerName]) {
          dealerVehicleCount[dealerName]++;
        } else {
          dealerVehicleCount[dealerName] = 1;
        }
      });

      // Convert the dealerVehicleCount object to a list of dealers and their vehicle counts
      let dealerList = Object.entries(dealerVehicleCount).map(
        ([dealer, count]) => {
          return { dealer, count };
        }
      );

      setDealerList(dealerList);
      if (newData && response.payload.total_count) {
        // Don't sort the data here as it should already be sorted from the API
        const updatedCarsInfo = reset ? newData : [...carsInfo, ...newData];
        setCarsInfo(updatedCarsInfo); // Update carsInfo state
        setTotalCarCount(response.payload.total_count); // Update totalCarCount state
        setDataFlag(true); // Update dataFlag state
        setHasMoreData(newData.length > 0); // Update hasMoreData state
      } else {
        setError(1);
        setDataFlag(true);
      }
    } else {
      setLoading(false);
      toast.error("Failed to fetch vehicle inventory. Please try again."); // Show error toast
      setDataFlag(false); // Update dataFlag state
      setHasMoreData(false); // No more data
    }
    setTotalMcVehicleCount(response.payload.totalMcVehicleCount);
    setTotalShowCount(response.payload.data.length);
    setTimeout(() => {
      setLoading(false);
    }, 500); // Delay of 500ms to ensure all data is loaded before stopping the loader
  };

  // Function to fetch more cars
  const fetchMoreCars = () => {
    if (hasMoreData) {
      // Only fetch more cars if there's more data
      console.log("Fetched cars...");
    }
  };

  // Function to handle right swipe
  const onRightSwipe = async (swipedData, token) => {
    if (!swipedData.inventory_row_id) {
      toast.error(
        "Cannot move vehicle to garage as required info is missing. Please try later."
      );
      return false;
    }
    setLoadingRightSwipe(true);
    try {
      dispatch(rightSwipe({ token, swipedData }))
        .then((response) => {
          if (response.error) {
            toast.error(
              `Cannot move the previous vehicle as ${response.payload.message}. Please try later.`
            );
            setLoadingRightSwipe(false);
            return;
          }
          // Push the VIN into addedCarinGarageVins
          if (swipedData.vin) {
            const updatedVins = Array.isArray(searchParams.addedCarinGarageVins)
              ? [...searchParams.addedCarinGarageVins, swipedData.vin] // Append the new VIN
              : [swipedData.vin];

            // Update `location.state` with the new `vins`
            navigate(location.pathname, {
              state: {
                ...location.state,
                params: {
                  ...searchParams,
                  addedCarinGarageVins: updatedVins,
                },
              },
            });
          } else {
            toast.error(
              "Cannot move vehicle to garage as required info is missing. Please try later."
            );
            return false;
          }

          toast.success(
            `${swipedData.heading ? swipedData.heading : "Vehicle"
            } Moved to My Garage`
          );
          setLoadingRightSwipe(false);
        })
        .catch((error) => {
          console.error("Error while Fetching:", error);
          setLoadingRightSwipe(false);
        });
    } catch (error) {
      console.error("Error while Fetching:", error);
      setLoadingRightSwipe(false);
    }
    const component = componentMetaData.find((comp) => comp.slug === "right-swipe");
    if (component) {
      // Run this function without waiting for it to complete
      (async () => {
        try {
          await callComponentActionApi(dispatch, component.id, swipedData.inventory_row_id);
        } catch (error) {
          console.error("Error while calling callComponentActionApi:", error);
        }
      })();
    }
  };

  const fetchInventoryVehicleDetails = async (row_id) => {
    try {
      const response = await dispatch(
        getVehicleDetailsInventory({ row_id, token })
      );
      return response.payload; // Return the response payload
    } catch (error) {
      console.error("Error while Fetching Vehicles:", error);
      throw error; // Re-throw the error to handle it in the child component
    }
  };

  useEffect(() => {
    // Get the page_id from pageMetaData based on slug
    const slug = "swipe-cards"; // Replace this with the desired slug
    const pageData = pageMetaData.find((page) => page.slug === slug);
    if (pageData) {
      setPageId(pageData.id);
    }

    // Get Page Componenet Meta Data
    const matchingComponents = pageComponentData.filter(
      (pageComponent) => pageComponent.page_id === pageId
    );

    if (matchingComponents.length > 0) {
      setComponentMetaData(matchingComponents);
    }
  }, [pageId]);

  // Return all the states
  return {
    page_size,
    carsInfo,
    totalCarCount,
    dataFlag,
    fetchMoreCars,
    onRightSwipe,
    loading,
    loadingRightSwipe,
    hasMoreData,
    error,
    totalMcVehicleCount,
    prevRoute,
    fetchInventoryVehicleDetails,
    dealerList,
    distanceOptions,
    setDistanceOptions,
    searhCriteria,
    setSearhCriteria,
    fetchInventory,
    totalShowCount,
    componentMetaData,
    handlePriceSortingChange,
    selectedPriceSorting: tempPriceSorting, // Use temporary state for display
    sortingPriceOptions,
    handleApplySorting,
  };
};
