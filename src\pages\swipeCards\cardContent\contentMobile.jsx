import { useRef, useState, useEffect } from "react";
import {
  Stack,
  Typography,
  CardContent,
  Grid,
  styled,
  Chip,
  Tooltip,
  CircularProgress,
} from "@mui/material";
import Icon from "../../../icon";
import { useMediaQuery } from "react-responsive";
import PropTypes from "prop-types";
import VehicleInventoryDetailsModal from "../modal/vehicleInventoryDetailsModal";
import HighValueFeaturessModal from "../../myGarage/modal/highvaluFeatures";
import { useDispatch } from "react-redux";
import { callComponentActionApi } from "../../../util/callComponenetActionApi";
import { pageMetaData } from "../../../../data/pageMetaData";
import { pageComponentData } from "../../../../data/componentMetaData";

const Ribbon = styled("div")(({ theme }) => ({
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.primary.contrastText,
  padding: theme.spacing(0.5, 1),
  borderRadius: theme.shape.borderRadius,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));

const ContentMobile = ({ car, fetchInventoryVehicleDetails }) => {
  const isMobile = useMediaQuery({ query: "(max-width: 767px)" }); // Check if device is mobile
  const chipContainerRef = useRef(null); // Reference to the chip container
  const headingRef = useRef(null); // Reference to the heading
  const [visibleChips, setVisibleChips] = useState([]); // State to store visible chips
  const [remainingChips, setRemainingChips] = useState([]); // State to store remaining chips
  const [loadingDetails, setLoadingDetails] = useState(null); // New state for loading details
  const [setError] = useState(null); // State to store error
  const dispatch = useDispatch(); // Redux dispatch

  const truncateText = (text, maxLength) => {
    if (text.length > maxLength) {
      return text.slice(0, maxLength) + "...";
    }
    return text;
  }; // Truncate text if necessary

  useEffect(() => {
    const adjustChipVisibility = () => {
      const maxHeight = 55; // Example height limit, adjust as necessary
      const headingHeight = headingRef.current ? headingRef.current.offsetHeight : 0; // Height of the heading
      const availableHeight = maxHeight - headingHeight; // Remaining height for chips

      const chips = car.high_value_features
        ? car.high_value_features.split("|")
        : []; // Split high value features by '|'
      let totalHeight = 0;
      let visible = [];
      let remaining = [];

      chips.forEach((chip) => {
        const chipHeight = 32; // Example chip height, adjust as necessary
        if (totalHeight + chipHeight <= availableHeight || visible.length === 0) {
          visible.push(chip);
          totalHeight += chipHeight;
        } else {
          remaining.push(chip);
        }
      }); // Add chips to visible and remaining arrays based on their height

      setVisibleChips(visible);
      setRemainingChips(remaining);
    };

    adjustChipVisibility();
  }, [car]); // Only re-run when car changes

  // Vehicle Details Modal
  const [vehicleDetails, setVehicleDetails] = useState(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [highValueModalOpen, setHighValueModalOpen] = useState(false);
  const [pageId, setPageId] = useState(null);
  const [componentMetaData, setComponentMetaData] = useState(null); // State to store the componentMetaData

  useEffect(() => {
    // Get the page_id from pageMetaData based on slug
    const slug = "swipe-cards"; // Replace this with the desired slug
    const pageData = pageMetaData.find((page) => page.slug === slug);
    if (pageData) {
      setPageId(pageData.id);
    }

    // Get Page Componenet Meta Data
    const matchingComponents = pageComponentData.filter(
      (pageComponent) => pageComponent.page_id === pageId
    );

    if (matchingComponents.length > 0) {
      setComponentMetaData(matchingComponents);
    }
  }, [pageId]);

  // Function to fetch vehicle details
  const fetchVehicleDetails = async (car) => {
    setLoadingDetails(car.row_id);
    try {
      const result = await fetchInventoryVehicleDetails(car.row_id);
      setVehicleDetails(result); // Store the result in state
      setLoadingDetails(null);
    } catch (err) {
      setError(err.message); // Handle the error in state
      setLoadingDetails(null);
    }
    setModalOpen(true);
    // Find the component by slug
    const component = componentMetaData.find((comp) => comp.slug === 'view-details');
    // Call the API in the background without blocking the main logic
    callComponentActionApi(dispatch, component.id, car.row_id).catch((error) => {
    console.error("Error while calling callComponentActionApi:", error);
  });
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    setVehicleDetails(null);
  };

  const fetchVehicleHighValueFeatures = async (car) => {
    setVehicleDetails(car);
    setHighValueModalOpen(true);
    // Find the component by slug
    const component = componentMetaData.find((comp) => comp.slug === 'high-value-features');
    // Call the API in the background without blocking the main logic
    callComponentActionApi(dispatch, component.id, car.row_id).catch((error) => {
    console.error("Error while calling callComponentActionApi:", error);
  });
  };

  const handleHighValueFeatureCloseModal = () => {
    setHighValueModalOpen(false);
    setVehicleDetails(null);
  };
  // End //

  return (
    <CardContent
      sx={{
        textAlign: "left",
        paddingTop: isMobile ? "0px" : "3px",
      }}
    >
      <Stack
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        marginBottom={1}
      >
        <Stack direction="row" alignItems="center">
          {car.inventory_type && (
            <Ribbon>
              <Typography
                variant="body2"
                sx={{
                  fontSize: isMobile ? "12px" : "16px",
                  fontWeight: "600",
                }}
              >
                {car.inventory_type.toUpperCase()}
              </Typography>
            </Ribbon>
          )}
          {car.year && (
            <Ribbon sx={{ ml: 1, backgroundColor: "#FBC60B" }}>
              <Typography
                variant="body2"
                sx={{
                  fontSize: isMobile ? "12px" : "16px",
                  fontWeight: "800",
                  color: "#000000",
                }}
              >
                {car.year}
              </Typography>
            </Ribbon>
          )}
        </Stack>
        {loadingDetails === car.row_id ? (
          <CircularProgress size={isMobile ? 28 : 36} />
        ) : (
          <Icon icon="info" size={isMobile ? 28 : 36} onClick={() => fetchVehicleDetails(car)} />
        )}
      </Stack>
      {car.heading && (
        <Typography
          variant="subtitle2"
          ref={headingRef}
          sx={{
            fontSize: isMobile ? "14px" : "24px",
            fontWeight: "bold",
          }}
        >
          {truncateText(car.heading, 50)}
        </Typography>
      )}

      {car.miles != null && car.miles != 'null' && car.miles !== 0 && car.miles !== "0" && (
        <Typography
          variant="body2"
          color="textSecondary"
          sx={{ fontSize: isMobile ? "12px" : "20px", fontWeight: "600" }}
        >
          {car.miles} Miles
        </Typography>
      )}
      {car.price && (
        <Typography
          variant="body2"
          color="#4991FF"
          sx={{ fontSize: isMobile ? "12px" : "20px", fontWeight: "600" }}
        >
          $ {car.price.toLocaleString()}
        </Typography>
      )}
      <Grid container spacing={1} marginTop={0.25} ref={chipContainerRef}>
        {visibleChips.map((feature, index) => (
          <Grid item key={index}>
            <Tooltip disableFocusListener title={feature}>
              <Chip
                label={truncateText(feature, 15)}
                size={isMobile ? "small" : "medium"}
                sx={{
                  "& .MuiChip-label": {
                    display: "block",
                    whiteSpace: "normal",
                    fontSize: isMobile ? "10px" : "18px",
                    fontWeight: 800,
                  },
                }}
              />
            </Tooltip>
          </Grid>
        ))}
        {remainingChips.length > 0 && (
          <Grid item>
            <Chip
              style={{ color: "#4991FF" }}
              onClick={() => fetchVehicleHighValueFeatures(car)}
              label={`+${remainingChips.length} more`}
              size={isMobile ? "small" : "medium"}
              sx={{
                "& .MuiChip-label": {
                  display: "block",
                  whiteSpace: "normal",
                  fontSize: isMobile ? "10px" : "18px",
                  fontWeight: 800,
                },
              }}
            />
          </Grid>
        )}
      </Grid>
      <VehicleInventoryDetailsModal
        open={modalOpen}
        onClose={handleCloseModal}
        vehicleDetails={vehicleDetails}
        selectedCar={car}
      />
      <HighValueFeaturessModal
        highValueopen={highValueModalOpen}
        onClose={handleHighValueFeatureCloseModal}
        vehicleDetails={vehicleDetails}
        selectedCar={car}
      />
    </CardContent>
  );
};

ContentMobile.propTypes = {
  car: PropTypes.shape({
    high_value_features: PropTypes.string,
    price: PropTypes.number,
    miles: PropTypes.number,
    year: PropTypes.number,
    inventory_type: PropTypes.string,
    heading: PropTypes.string,
    row_id: PropTypes.string.isRequired,
  }).isRequired,
  fetchInventoryVehicleDetails: PropTypes.func.isRequired,
};

export default ContentMobile;
