import { useRef, useState, useEffect } from "react";
import {
  Stack,
  Typography,
  CardContent,
  Grid,
  styled,
  Chip,
  Tooltip,
  CircularProgress,
} from "@mui/material";
import Icon from "../../../icon";
import { useMediaQuery } from "react-responsive";
import PropTypes from "prop-types";
import VehicleInventoryDetailsModal from "../modal/vehicleInventoryDetailsModal";
import HighValueFeaturessModal from "../../myGarage/modal/highvaluFeatures";
import { useDispatch } from "react-redux";
import { callComponentActionApi } from "../../../util/callComponenetActionApi";
import { pageMetaData } from "../../../../data/pageMetaData";
import { pageComponentData } from "../../../../data/componentMetaData";

const Ribbon = styled("div")(({ theme }) => ({
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.primary.contrastText,
  padding: theme.spacing(0.5, 1),
  borderRadius: theme.shape.borderRadius,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));

const truncateText = (text, maxLength) => {
  if (text.length > maxLength) {
    return text.slice(0, maxLength) + "...";
  }
  return text;
}; // Truncate text if necessary

const ContentWeb = ({ car, fetchInventoryVehicleDetails }) => {
  const chipContainerRef = useRef(null); // Reference to the chip container
  const headingRef = useRef(null); // Reference to the heading element
  const [visibleChips, setVisibleChips] = useState([]); // State to store visible chips
  const [remainingChips, setRemainingChips] = useState([]); // State to store remaining chips
  const isBigScreen = useMediaQuery({ query: "(min-width: 1824px)" }); // Check if device is big screen
  const [loadingDetails, setLoadingDetails] = useState(null); // New state for loading details
  const [maxHeight, setMaxHeight] = useState(isBigScreen ? 90 : 32); // State to store maxHeight
  const [setError] = useState(null); // State to store error
  const dispatch = useDispatch(); // Redux dispatch
  
  useEffect(() => {
    if (headingRef.current) {
      const headingHeight = headingRef.current.offsetHeight;
      const availableHeight = isBigScreen ? 120 - headingHeight : 60 - headingHeight; // Adjust available height based on heading height
      setMaxHeight(availableHeight);
    }
  }, [car.heading, isBigScreen]); // Recalculate on heading change and screen size change

  useEffect(() => {
    const chips = car.high_value_features
      ? car.high_value_features.split("|")
      : []; // Split high value features by '|'

    let totalHeight = 0;
    let visible = [];
    let remaining = [];

    chips.forEach((chip) => {
      const chipHeight = isBigScreen ? 22 : 18; // Example chip height, adjust as necessary
      if (totalHeight + chipHeight <= maxHeight || visible.length === 0) {
        visible.push(chip);
        totalHeight += chipHeight;
      } else {
        remaining.push(chip);
      }
    }); // Add chips to visible and remaining arrays based on their height

    setVisibleChips(visible);
    setRemainingChips(remaining);
  }, [car.high_value_features, maxHeight, isBigScreen]); // Recalculate on features, maxHeight, and screen size change

  // Vehicle Details Modal
  const [vehicleDetails, setVehicleDetails] = useState(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [highValueModalOpen, setHighValueModalOpen] = useState(false);
  const [pageId, setPageId] = useState(null);
  const [componentMetaData, setComponentMetaData] = useState(null); // State to store the componentMetaData

  useEffect(() => {
      // Get the page_id from pageMetaData based on slug
      const slug = "swipe-cards"; // Replace this with the desired slug
      const pageData = pageMetaData.find((page) => page.slug === slug);
      if (pageData) {
        setPageId(pageData.id);
      }
  
      // Get Page Componenet Meta Data
      const matchingComponents = pageComponentData.filter(
        (pageComponent) => pageComponent.page_id === pageId
      );
  
      if (matchingComponents.length > 0) {
        setComponentMetaData(matchingComponents);
      }
    }, [pageId]);

  // Function to fetch vehicle details  
  const fetchVehicleDetails = async (car) => {
    setLoadingDetails(car.row_id);
    try {
      const result = await fetchInventoryVehicleDetails(car.row_id);
      setVehicleDetails(result); // Store the result in state
      setLoadingDetails(null);
    } catch (err) {
      setError(err.message); // Handle the error in state
      setLoadingDetails(null);
    }
    setModalOpen(true);
    // Find the component by slug
    const component = componentMetaData.find((comp) => comp.slug === 'view-details');
    // Call the API in the background without blocking the main logic
    callComponentActionApi(dispatch, component.id, car.row_id).catch((error) => {
    console.error("Error while calling callComponentActionApi:", error);
  });
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    setVehicleDetails(null);
  };

  const fetchVehicleHighValueFeatures = async (car) => {
    setVehicleDetails(car);
    setHighValueModalOpen(true);
    // Find the component by slug
    const component = componentMetaData.find((comp) => comp.slug === 'high-value-features');
    // Call the API in the background without blocking the main logic
    callComponentActionApi(dispatch, component.id, car.row_id).catch((error) => {
    console.error("Error while calling callComponentActionApi:", error);
  });
  };

  const handleHighValueFeatureCloseModal = () => {
    setHighValueModalOpen(false);
    setVehicleDetails(null);
  };
  const showDealerName = import.meta.env.VITE_SHOW_DEALER_NAME === 'true';
  // End //
  return (
    <CardContent sx={{ textAlign: "left", padding: "23px", paddingTop: isBigScreen ? 1 : 0 }}>
      <Stack
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        marginBottom={1}
      >
        <Stack direction="row" alignItems="center">
          {car.inventory_type && (
            <Ribbon>
              <Typography
                variant="body2"
                sx={{
                  fontSize: isBigScreen ? "18px" : "10px",
                  fontWeight: "600",
                }}
              >
                {car.inventory_type.toUpperCase()}
              </Typography>
            </Ribbon>
          )}
          {car.year && (
            <Ribbon sx={{ marginLeft: 1, backgroundColor: "#FBC60B" }}>
              <Typography
                variant="body2"
                sx={{
                  fontSize: isBigScreen ? "18px" : "10px",
                  fontWeight: "800",
                  color: "#000000",
                }}
              >
                {car.year}
              </Typography>
            </Ribbon>
          )}
        </Stack>
      </Stack>
      {car.heading && (
        <Typography
          ref={headingRef} // Reference to the heading element
          variant="subtitle2"
          sx={{ fontSize: isBigScreen ? "18px" : "14px", fontWeight: "bold" }}
        >
          {car.heading}
        </Typography>
      )}
      {car.miles != null &&
        car.miles != "null" &&
        car.miles !== 0 &&
        car.miles !== "0" && (
          <Typography
            variant="body2"
            color="textSecondary"
            sx={{ fontSize: isBigScreen ? "18px" : "12px", fontWeight: "600" }}
          >
            {car.miles} Miles
          </Typography>
        )}
      {car.price && (
        <Typography
          variant="body2"
          color="#4991FF"
          sx={{ fontSize: isBigScreen ? "18px" : "12px", fontWeight: "600" }}
        >
          $ {car.price.toLocaleString()}
        </Typography>
      )}
      {showDealerName && ( <Typography>Dealer Name : <b>{car.seller_name}</b></Typography> )}
      <Grid container spacing={1} marginTop={isBigScreen ? 1 : 0.5} ref={chipContainerRef}>
        {visibleChips.map((feature, index) => (
          <Grid item key={index}>
            <Tooltip disableFocusListener title={feature}>
              <Chip
                style={{ cursor: "pointer" }} // Ensure the icon is clickable
                label={truncateText(feature, 15)}
                size="small"
                sx={{
                  "& .MuiChip-label": {
                    display: "block",
                    whiteSpace: "normal",
                    fontSize: isBigScreen ? "14px" : "10px",
                    fontWeight: 800,
                  },
                }}
              />
            </Tooltip>
          </Grid>
        ))}
        {remainingChips.length > 0 && (
          <Grid item>
            <Tooltip disableFocusListener title="Click to View Vehicle High Value Features">
              <Chip
                onClick={() => fetchVehicleHighValueFeatures(car)}
                style={{ cursor: "pointer", color: "#4991FF" }} // Ensure the icon is clickable
                label={`+${remainingChips.length} more`}
                size="small"
                sx={{
                  "& .MuiChip-label": {
                    display: "block",
                    whiteSpace: "normal",
                    fontSize: isBigScreen ? "14px" : "10px",
                    fontWeight: 800,
                  },
                }}
              />
            </Tooltip>
          </Grid>
        )}
        <Grid item sx={{ marginLeft: 'auto' }}>
          {loadingDetails === car.row_id ? (
            <CircularProgress size={isBigScreen ? 32 : 24} />
          ) : (
            <Tooltip disableFocusListener title="Click to View Vehicle Details">
              <div>
                <Icon
                  icon="info" // Ensure this is the correct prop if using a different library
                  size={isBigScreen ? 32 : 24}
                  onClick={() => fetchVehicleDetails(car)}
                  style={{ cursor: 'pointer' }} // Ensure the icon is clickable
                />
              </div>
            </Tooltip>
          )}
        </Grid>
      </Grid >
      <VehicleInventoryDetailsModal
        open={modalOpen}
        onClose={handleCloseModal}
        vehicleDetails={vehicleDetails}
        selectedCar={car}
      />
      <HighValueFeaturessModal
        highValueopen={highValueModalOpen}
        onClose={handleHighValueFeatureCloseModal}
        vehicleDetails={vehicleDetails}
        selectedCar={car}
      />
    </CardContent >
  );
};

ContentWeb.propTypes = {
  car: PropTypes.shape({
    high_value_features: PropTypes.string,
    price: PropTypes.number,
    miles: PropTypes.number,
    year: PropTypes.number,
    inventory_type: PropTypes.string,
    heading: PropTypes.string,
    row_id: PropTypes.string.isRequired,
    seller_name: PropTypes.string
  }).isRequired,
  fetchInventoryVehicleDetails: PropTypes.func.isRequired,
};

export default ContentWeb;
