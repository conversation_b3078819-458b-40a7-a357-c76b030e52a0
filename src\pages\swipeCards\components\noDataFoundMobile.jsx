import { useNavigate } from "react-router-dom";
import { useMediaQuery } from "react-responsive";
import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import SearchIcon from "@mui/icons-material/Search";
import { ImageBaseURL } from "../../../config";
import PropTypes from "prop-types";
import { useState } from "react";
import { Slider } from "@mui/material";
import Icon from "../../../icon";

function NoInventoryFoundMobile({
  totalMcVehicleCount,
  prevRoute,
  distanceOptions,
  searhCriteria,
  onUpdateDistance,
}) {
  const navigate = useNavigate();
  const Item = styled(Paper)(({ theme }) => ({
    ...theme.typography.body2,
    padding: theme.spacing(1),
    textAlign: "center",
    borderRadius: "none",
    boxShadow: "none",
  }));

  const StyledButton = styled(({ ...other }) => <Button {...other} />)(
    ({ isMobile }) => ({
      borderRadius: "10px",
      width: isMobile ? "100%" : "70%",
      fontSize: isMobile ? "14px" : "18px",
      fontWeight: "600",
      padding: "15px",
      backgroundColor: "#4891FF",
      "&:hover": {
        backgroundColor: "primary",
      },
    })
  );

  const token = localStorage.getItem("token");
  //function to reload page
  const handleReload = () => {
    if (token) {
      if (prevRoute === "generic-search") {
        navigate("/generic-search");
      } else if (prevRoute === "specific-search") {
        navigate("/specific-search");
      } else {
        navigate("/");
      }
    } else {
      navigate("/");
    }
  };
  const isMobile = useMediaQuery({ maxWidth: 767 });

  // Slider default values
  const [value, setValue] = useState(searhCriteria.distance); // Default value of 50 miles

  const handleSliderChange = (event, newValue) => {
    setValue(newValue);
  };

  const handleRetrySearch = () => {
    const updatedSearchCriteria = {
      ...searhCriteria,
      distance: value, // Update distance based on slider
    };

    // Call the parent's function to update the search criteria and trigger search
    onUpdateDistance(updatedSearchCriteria);
  };

  // Convert DistanceOptions to marks for the Slider
  const marks = distanceOptions.map((option) => ({
    value: option.value,
    label: String(option.value),
  }));
  return (
    <Box
      sx={{
        flexGrow: 1,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        marginX: "2vh",
      }}
    >
      <Grid container spacing={2} sx={{ padding: "15px 0px" }}>
        <Grid item xs={12}>
          <Item className="page_bg">
            <img
              src={ImageBaseURL + "zero_data_count.png"}
              alt="Not Found"
              style={
                isMobile
                  ? { maxWidth: "60%", maxHeight: "60%" }
                  : { maxWidth: "50%", maxHeight: "50%" }
              }
            />
          </Item>
        </Grid>
        <Grid item xs={12}>
          <Item className="page_bg">
            <Typography
              variant="subtitle2"
              gutterBottom
              sx={{ fontWeight: "800" }}
              style={isMobile ? { fontSize: "12px" } : { fontSize: "18px" }}
            >
              No Data Found.
            </Typography>
            {!searhCriteria.vin && (
              <Typography
                variant="subtitle2"
                gutterBottom
                sx={{ fontWeight: "800" }}
                style={isMobile ? { fontSize: "12px" } : { fontSize: "18px" }}
              >
                Please Try with Other Combinations.
              </Typography>
            )}
          </Item>
        </Grid>
        {searhCriteria.distance < 250 && !searhCriteria.vin && (
          <Grid item xs={12} style={{ paddingTop: "0px" }}>
            <Item className="page_bg">
              <Typography
                variant="caption"
                gutterBottom
                sx={{ fontWeight: "800" }}
                style={isMobile ? { fontSize: "12px" } : { fontSize: "18px" }}
              >
                Trouble finding your favorite vehicle? Increase the Search
                Radius to Search Again.
              </Typography>
            </Item>
          </Grid>
        )}
        {searhCriteria.distance < 250 && !searhCriteria.vin && (
          <Grid
            item
            xs={12}
            style={{ paddingTop: "0px", marginBottom: "10px" }}
          >
            <Item className="page_bg">
              <Slider
                value={value}
                onChange={handleSliderChange}
                marks={marks}
                min={10}
                max={250}
                step={null}
                valueLabelDisplay="auto"
                aria-labelledby="distance-slider"
                sx={{
                  "& .MuiSlider-markLabel": {
                    whiteSpace: "nowrap",
                    fontSize: "0.7rem", // Reduce font size to avoid overlap
                    transform: (mark) =>
                      mark === 10 || mark === 20 ? "translateX(-20%)" : "none", // Adjust position for overlapping values
                  },
                }}
              />
            </Item>
          </Grid>
        )}
        {searhCriteria.distance < 250 && !searhCriteria.vin && (
          <Grid item xs={12} style={{ paddingTop: "0px" }}>
            <Item className="page_bg">
              <StyledButton
                isMobile={isMobile}
                variant="contained"
                onClick={handleRetrySearch}
              >
                <SearchIcon sx={{ mr: 1 }} fontSize="large" /> search again
              </StyledButton>
            </Item>
          </Grid>
        )}
        <Grid item xs={12} style={{ paddingTop: "0px" }}>
          <Item className="page_bg">
            <StyledButton
              isMobile={isMobile}
              variant="contained"
              onClick={handleReload}
            >
              <Icon
                icon="phone-in-hand"
                size={35}
                style={{ marginRight: "20px" }}
              />{" "}
              try other search
            </StyledButton>
          </Item>
        </Grid>
        {!(searhCriteria.vin || searhCriteria.aiQuery) && (
          <Grid item xs={12} style={{ paddingTop: "0px" }}>
            <Item className="page_bg">
              <Typography
                variant="caption"
                gutterBottom
                sx={{ fontWeight: "800" }}
                style={isMobile ? { fontSize: "12px" } : { fontSize: "18px" }}
              >
                Search Over {totalMcVehicleCount.toLocaleString()} Vehicles.
              </Typography>
            </Item>
          </Grid>
        )}
      </Grid>
    </Box>
  );
}

NoInventoryFoundMobile.propTypes = {
  totalMcVehicleCount: PropTypes.number.isRequired,
  prevRoute: PropTypes.string.isRequired,
  distanceOptions: PropTypes.array.isRequired,
  searhCriteria: PropTypes.object.isRequired,
  onUpdateDistance: PropTypes.func.isRequired,
};

export default NoInventoryFoundMobile;
