import { useNavigate } from "react-router-dom";
import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import { ImageBaseURL } from "../../../config";
import PropTypes from "prop-types";
import Icon from "../../../icon";
import { Slider } from "@mui/material";
import { useState } from "react";
import SearchIcon from "@mui/icons-material/Search";
import { useMediaQuery } from 'react-responsive';

function NoInventoryFound({
  totalMcVehicleCount,
  prevRoute,
  distanceOptions,
  searhCriteria,
  onUpdateDistance
}) {
  const navigate = useNavigate();
  const Item = styled(Paper)(({ theme }) => ({
    ...theme.typography.body2,
    padding: theme.spacing(1),
    textAlign: "center",
    borderRadius: "none",
    boxShadow: "none",
  }));

  const StyledButtonSearch = styled(Button)({
    borderRadius: "10px",
    width: "80%",
    fontWeight: "600",
    padding: "8px",
    margin: "3px 0",
    backgroundColor: "#4891FF",
    "&:hover": {
      backgroundColor: "primary",
    },
  });

  const token = localStorage.getItem("token");
  const isBigScreen = useMediaQuery({ query: "(min-width: 1824px)" }); // Adjust the breakpoint as needed

  const buttonWidth = isBigScreen ? "30%" : "80%";
  // Function to reload page
  const handleReload = () => {
    if (token) {
      if (prevRoute === "generic-search") {
        navigate("/generic-search");
      } else if (prevRoute === "specific-search") {
        navigate("/specific-search");
      } else {
        navigate("/");
      }
    } else {
      navigate("/");
    }
  };

  // Slider default values
  const [value, setValue] = useState(searhCriteria.distance); // Default value of 50 miles

  const handleSliderChange = (event, newValue) => {
    setValue(newValue);
  };

  const handleRetrySearch = () => {
    const updatedSearchCriteria = {
      ...searhCriteria,
      distance: value, // Update distance based on slider
    };
    // Call the parent's function to update the search criteria and trigger search
    onUpdateDistance(updatedSearchCriteria);
  };
  
  // Convert DistanceOptions to marks for the Slider
  const marks = distanceOptions.map(option => ({
    value: option.value,
    label: String(option.value),
  }));

  return (
    <Box sx={{ flexGrow: 1, marginX: "2vh" }} mt={1}>
      <Grid container spacing={2}>
        <Grid item md={12}>
          <Item className="page_bg">
            <img
              src={ImageBaseURL + "zero_data_count.png"}
              alt="no data"
              style={{
                width: isBigScreen ? "20%" : "70%",
                height: isBigScreen ? "20%" : "70%",
              }}
            />
          </Item>
          <Item className="page_bg">
            <Typography
              variant="subtitle2"
              gutterBottom
              sx={{ fontWeight: "800" }}
            >
              No Data Found.
            </Typography>
            {!searhCriteria.vin && (
              <Typography
                variant="caption"
                gutterBottom
                sx={{ fontWeight: "800" }}
              >
                Please Try with Other Combinations.
              </Typography>
            )}
          </Item>
        </Grid>
        {searhCriteria.distance < 250 && !searhCriteria.vin && (
          <Grid item md={12} style={{ paddingTop: "0px" }}>
            <Item className="page_bg">
              <Typography
                variant="caption"
                gutterBottom
                sx={{ fontWeight: "800" }}
              >
                Trouble finding your favorite vehicle? Increase the Search
                Radius to search again.
              </Typography>
            </Item>
          </Grid>
        )}
        {/* Conditionally render the slider if distance is not 250 */}
        {searhCriteria.distance < 250 && !searhCriteria.vin && (
          <Grid
            item
            md={12}
            style={{ paddingTop: "0px", marginBottom: "10px", width: buttonWidth }}
          >
            <Item className="page_bg">
              <Slider
                value={value}
                onChange={handleSliderChange}
                marks={marks}
                min={10}
                max={250}
                step={null}
                valueLabelDisplay="auto"
                aria-labelledby="distance-slider"
                sx={{
                  "& .MuiSlider-markLabel": {
                    whiteSpace: "nowrap",
                    fontSize: "0.65rem", // Reduce font size to avoid overlap
                    transform: (mark) =>
                      mark === 10 || mark === 20 ? "translateX(-20%)" : "none",
                  },
                  width: buttonWidth,
                }}
              />
            </Item>
          </Grid>
        )}
        {searhCriteria.distance < 250 && !searhCriteria.vin && (
          <Grid item md={12} style={{ paddingTop: "0px" }}>
            <Item className="page_bg">
              <StyledButtonSearch
                variant="contained"
                onClick={handleRetrySearch}
                sx={{ width: buttonWidth }}
              >
                <SearchIcon sx={{ mr: 1 }} fontSize="large" />
                Search again
              </StyledButtonSearch>
            </Item>
          </Grid>
        )}
        <Grid item md={12} style={{ paddingTop: "0px" }}>
          <Item className="page_bg">
            <StyledButtonSearch variant="contained" onClick={handleReload} sx={{ width: buttonWidth }}>
              <Icon
                icon="phone-in-hand"
                size={35}
                style={{ marginRight: "20px" }}
              />
              try other search
            </StyledButtonSearch>
          </Item>
        </Grid>
        {!(searhCriteria.vin || searhCriteria.aiQuery) && (
          <Grid item md={12} style={{ paddingTop: "0px" }}>
            <Item className="page_bg">
              <Typography
                variant="caption"
                gutterBottom
                sx={{ fontWeight: "800" }}
              >
                Search Over {totalMcVehicleCount.toLocaleString()} Vehicles.
              </Typography>
            </Item>
          </Grid>
        )}
      </Grid>
    </Box>
  );
}

NoInventoryFound.propTypes = {
  totalMcVehicleCount: PropTypes.number.isRequired,
  prevRoute: PropTypes.string.isRequired,
  distanceOptions: PropTypes.array.isRequired,
  searhCriteria: PropTypes.object.isRequired,
  onUpdateDistance: PropTypes.func.isRequired,
};

export default NoInventoryFound;
