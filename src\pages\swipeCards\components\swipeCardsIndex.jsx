import { useMediaQuery } from 'react-responsive'
import SwipeCardsMobile from './swipeCardsMobile';
import SwipeCardsWeb from './swipeCardsWeb';
import MasterLayout from '../../../components/layouts/masterLayout';

function SwipeCards() {
    const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' });
    return (
        <MasterLayout>
            {isTabletOrMobile ? <SwipeCardsMobile /> : <SwipeCardsWeb />}
        </MasterLayout>
    );
}

export default SwipeCards;