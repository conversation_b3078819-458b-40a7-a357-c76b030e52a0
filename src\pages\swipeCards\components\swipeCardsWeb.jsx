import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Stack,
  Typography,
  Box,
  Grid,
  styled,
  Paper,
  Button,
  CircularProgress,
} from "@mui/material";
import { CardSwiper } from "../../../assets/plugins/cardSwiper/react-card-swiper-main/src/lib";
import Icon from "../../../icon";
import "../css/swipeSelectionPage.css";
import { SwipeCardsApi } from "../api/swipeCardsApi";
import { ImageBaseURL } from "../../../config";
import { useMediaQuery } from "react-responsive";
import ContentWeb from "../cardContent/contentWeb";
import NoInventoryFound from "./noDataFoundWeb";
import loaderGif from "../../../assets/gifs/loader.gif";
import validUrl from "valid-url";
import {
  Container,
  Card,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import FloatingButton from "./undoSwipeButton";
import { useDispatch } from "react-redux";
import { callComponentActionApi } from "../../../util/callComponenetActionApi";
import PurchasePowerConfirmationModal from "../modal/PurchasePowerConfirmationModal";
import VehicleFilterButton from "./vehicleFilterButton";
import { useGlobalTutorialModal } from "../../../context/TutorialModalContext";
import { hasSeenTutorial, shouldShowTutorial } from "../../../context/TutorialStorage";

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  borderRadius: "none",
  boxShadow: "none",
}));

const StyledButtonAccept = styled(Button)({
  borderRadius: "50%",
  width: "75px",
  height: "75px",
  fontWeight: "600",
  padding: "15px",
  backgroundColor: "#ffffff",
  boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.2)",
  "&:hover": {
    backgroundColor: "aliceblue",
  },
});

const StyledButton = styled(Button)({
  borderRadius: "10px",
  width: "90%",
  fontWeight: "600",
  padding: "14px",
  margin: "3px 0",
  backgroundColor: "#4891FF",
  "&:hover": {
    backgroundColor: "primary",
  },
});

const SwipeCardsWeb = () => {
  const {
    // page_size,
    carsInfo,
    totalCarCount,
    dataFlag,
    fetchMoreCars,
    loading,
    loadingRightSwipe,
    onRightSwipe,
    totalMcVehicleCount,
    prevRoute,
    fetchInventoryVehicleDetails,
    dealerList,
    distanceOptions,
    searhCriteria,
    setSearhCriteria,
    fetchInventory,
    totalShowCount,
    componentMetaData,
    handlePriceSortingChange,
    selectedPriceSorting,
    sortingPriceOptions,
    handleApplySorting,
    handleDistanceSortingChange,
    selectedDistanceSorting,
    sortingDistanceOptions,
  } = SwipeCardsApi();
  const navigate = useNavigate();
  const isBigScreen = useMediaQuery({ query: "(min-width: 1824px)" }); // Adjust the breakpoint as needed
  const [showSwipeCard, setShowSwipeCard] = useState(true); // State to control the visibility of the swipe card
  const [vehicleCount, setVehicleCount] = useState(1); // State to track the displayed vehicle count
  const token = localStorage.getItem("token"); // Retrieve the token from local storage
  const fallbackImg = `${ImageBaseURL}fallback_image.png`; // Fallback image URL
  const dispatch = useDispatch(); // Redux dispatch
  const [openPriceModal, setOpenPriceModal] = useState(false); // State to store the openPriceModal
  const [pendingSwipeData, setPendingSwipeData] = useState(null); // Store swiped data
  const [event, setEvent] = useState(null); // Store swiped event

  // Handler function for updating the distance in search criteria
  const handleUpdateDistance = (updatedCriteria) => {
    // Update the state
    setSearhCriteria(updatedCriteria);

    // Immediately call the API with the updated criteria
    fetchInventory(false, updatedCriteria); // Use updatedCriteria directly here
  };

  function isNumeric(value) {
    return !isNaN(value) && isFinite(value);
  }
  // Function to check if the URL is valid
  const isValidUrl = (url) => {
    const result = validUrl.isUri(url) !== undefined;
    return result;
  };

  const [undoDisable, setUndoDisable] = useState(false);
  const [data, setData] = useState([]);
  //will be used later for pagination purpose.
  // const cardTotalCount = JSON.parse(JSON.stringify(data.length)) > 20 ? page_size : JSON.parse(JSON.stringify(data.length));
  const [swipedCards, setSwipedCards] = useState([]);
  const [dataMapped, setDataMapped] = useState(false);

  useEffect(() => {
    if (carsInfo.length > 0) {
      let sortedCars = [...carsInfo]; // Create a copy of the data

      // Reverse the array to match the expected order in CardSwiper
      const reversedCars = [...sortedCars].reverse();

      const mappedData = reversedCars.map((car) => {
        const imageUrl = isValidUrl(car.photo_url)
          ? car.photo_url
          : fallbackImg;
        return {
          id: car.row_id,
          meta: {
            apk: {
              vin: car.vin,
              row_id: car.row_id,
              heading: car.heading,
              stock_number: car.stock_no,
              price: car.price,
            },
          },
          src: imageUrl,
          content: (
            <ContentWeb
              car={car}
              fetchInventoryVehicleDetails={fetchInventoryVehicleDetails}
            />
          ),
        };
      });

      setData(mappedData);
      setDataMapped(true); // Prevents re-triggering on subsequent renders
    }
  }, [carsInfo, selectedPriceSorting.order]);

  // Code for storing page click count

  useEffect(() => {
    let isMounted = true; // Add a flag to check if the component is still mounted

    if (
      !loading &&
      componentMetaData &&
      componentMetaData.length > 0 &&
      isMounted
    ) {
      try {
        const component = componentMetaData.find(
          (comp) => comp.slug === "swipe-cards" && comp.type === "url"
        );
        if (!component) {
          console.error(`Component with slug "swipe-card" not found.`);
          return;
        }
        // Call the API with the component ID
        callComponentActionApi(dispatch, component.id);
      } catch (error) {
        console.error("Error handling button click:", error);
      }
    }

    // Cleanup function to reset the flag when the component is unmounted
    return () => {
      isMounted = false;
    };
  }, [loading, componentMetaData]);

  // END //

  // for tutorial
  const { openTutorialModal } = useGlobalTutorialModal();
  useEffect(() => {
    const checkAndShowTutorial = async () => {
      if (loading) return; // Don't proceed if loading

      const localFlag = hasSeenTutorial("swipeCards_vehicles");
      if (localFlag) return; // Already seen locally, skip

      const shouldShow = await shouldShowTutorial({
        tutorialKey: "swipeCards_vehicles",
        dispatch,
      });

      if (shouldShow && data.length > 0) {
        openTutorialModal({
          bodyImageSrc: ImageBaseURL + "tutorials/images/ai-negotiation-tutorial-feature-image.png",
          additionalTopText: <span>Get an</span>,
          mainText: <span>Out the Door Price</span>,
          subText: <span>Select top 5 & click on Start AI Negotiator</span>,
          audioSrc: ImageBaseURL + "tutorials/audios/my-garage-negotiation-audio.mp3",
        });
      }
    };

    checkAndShowTutorial();
  }, [loading]);

  const handleFinish = (status) => {
    if (status === "finished") {
      setShowSwipeCard(false); //PAGINATION PURPOSE
      fetchMoreCars();
      setUndoDisable(true);
    }
  }; // Handle the finish event

  // Function for VIN Search //
  /**
   * Handles the VIN card dismiss event.
   * If the card is swiped right and the price is higher than the user's available amount,
   * the modal is opened and the card is added back to the list.
   * If the card is swiped left, the card is removed from the list.
   * If the card is swiped right and the price is lower than or equal to the user's available amount,
   * the card is removed from the list.
   * @param {Event} event The event object.
   * @param {Object} cardData The card data object.
   */
  const handleVINCardDismiss = (event, cardData) => {
    if (
      searhCriteria.vin &&
      cardData.apk.price &&
      searhCriteria.userAvailableAmount &&
      cardData.apk.price > searhCriteria.userAvailableAmount
    ) {
      const styles = event.style.cssText;
      const match = styles.match(/rotate\(([^)]+)\)/);
      if (match) {
        const rotateValue = match[1];
        const parsedValue = parseFloat(rotateValue); // Parse the rotate value to a number
        if (!isNaN(parsedValue)) {
          if (parsedValue > 0) {
            setOpenPriceModal(true);
            setPendingSwipeData(cardData);
            setEvent(event);
            const lastSwipedCard = cardData.apk; // Access the only swiped card
            const restoredCard = {
              id: lastSwipedCard.row_id,
              meta: {
                apk: lastSwipedCard,
              },
              src: data[0].src,
              content: data[0].content,
            };
            let ultimateData = data;
            ultimateData.push(restoredCard);
            setData(ultimateData);
            setSwipedCards([]); // Clear the swiped cards
            setVehicleCount((prevCount) => prevCount - 0); // Decrement the displayed vehicle count
          } else {
            return false;
          }
        }
      }
    } else {
      handleDismiss(event, cardData);
    }
  };

  /**
   * Handles the close event of the purchase power modal.
   * Restores the last swiped card back in the data and closes the modal.
   * @param {object} event - The close event of the modal.
   * @param {object} cardData - The last swiped card data.
   */
  const handleModalClose = (event, cardData) => {
    const lastSwipedCard = cardData.apk; // Access the only swiped card
    const restoredCard = {
      id: lastSwipedCard.row_id,
      meta: {
        apk: lastSwipedCard,
      },
      src: data[0].src,
      content: data[0].content,
    };
    let ultimateData = data;
    ultimateData.pop(restoredCard);
    setData(ultimateData);
    setOpenPriceModal(false);
  }

  // End //

  const handleDismiss = (event, cardData) => {
    const baseUrl = `${window.location.protocol}//${window.location.host}`;
    const swipedData = {
      inventory_row_id: cardData.apk.row_id,
      heading: cardData.apk.heading,
      vin: cardData.apk?.vin || null,
      stock_number: cardData.apk.stock_number,
      price: cardData.apk.price,
      conversation_details_url: `${baseUrl}/conversation`,
    }; // Prepare the data to be sent to the API

    const styles = event.style.cssText;
    const match = styles.match(/rotate\(([^)]+)\)/);

    if (match) {
      const rotateValue = match[1];
      const parsedValue = parseFloat(rotateValue); // Parse the rotate value to a number
      if (!isNaN(parsedValue)) {
        if (parsedValue > 0) {
          onRightSwipe(swipedData, token); // Call the onRightSwipe function
          const completeCardData = data.find(
            (card) => card.id === cardData.id || card.id === cardData.apk.row_id
          );
          if (openPriceModal) {
            setOpenPriceModal(false);
          }
          // Store only the latest swiped card, replacing the previous one
          setSwipedCards([]); //this logic is disabling the undo button for right swipe.

          let finalData = [];
          // timeout added for animation
          setTimeout(() => {
            if (data.length > 1) {
              data.map((item) => {
                if (completeCardData.id !== item.id) {
                  finalData.push(item);
                }
              });
              // Remove the card from data
              setData(finalData);
            }
          }, 100);
        } else if (parsedValue < 0) {
          // Add the dismissed card to the swipedCards state
          const completeCardData = data.find(
            (card) => card.id === cardData.id || card.id === cardData.apk.row_id
          );

          // Store only the latest swiped card, replacing the previous one
          setSwipedCards([completeCardData]);

          let finalData = [];
          // timeout added for animation
          setTimeout(() => {
            if (data.length > 1) {
              data.map((item) => {
                if (completeCardData.id !== item.id) {
                  finalData.push(item);
                }
              });
              // Remove the card from data
              setData(finalData);
            }
          }, 100);
          try {
            // Find the component by slug
            const component = componentMetaData.find(
              (comp) => comp.slug === "left-swipe"
            );
            // Call the API in the background without blocking the main logic
            callComponentActionApi(
              dispatch,
              component.id,
              cardData.apk.row_id,
              cardData.apk?.vin || null
            ).catch((error) => {
              console.error(
                "Error while calling callComponentActionApi:",
                error
              );
            });
          } catch (error) {
            console.error("Error handling button click:", error);
          }
        } else {
          console.log("Transition format 0");
        }
      } else {
        console.log("Transition format");
      }
    } else {
      console.log(".");
    }
    if (vehicleCount < totalCarCount) {
      setVehicleCount((prevCount) => prevCount + 1);
    } // Increment the displayed vehicle count
  }; // Handle the dismiss event

  // Function to undo the last swiped card
  const handleUndo = () => {
    if (swipedCards.length === 0) return; // No card to undo

    const lastSwipedCard = swipedCards[0]; // Access the only swiped card

    // Create the card in the same structure as it was originally
    const restoredCard = {
      id: lastSwipedCard.id,
      meta: {
        apk: lastSwipedCard.meta.apk,
      },
      src: lastSwipedCard.src,
      content: lastSwipedCard.content,
    };
    let ultimateData = data;
    ultimateData.push(restoredCard);
    setData(ultimateData);
    setSwipedCards([]); // Clear the swiped cards
    setVehicleCount((prevCount) => prevCount - 1); // Decrement the displayed vehicle count
  };

  const loadingTexts = [
    "Fetching Best Vehicles for You...",
    "Just a Moment, Almost There...",
    "Hang Tight! We're Getting Your Vehicles...",
    "Loading, Please Wait...",
    "We're Finding the Perfect Match for You...",
  ];

  const [currentTextIndex, setCurrentTextIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTextIndex((prevIndex) => (prevIndex + 1) % loadingTexts.length);
    }, 3000);

    return () => clearInterval(interval);
  }, []);
  if (!dataFlag || loading) {
    return (
      <Stack
        alignItems="center"
        justifyContent="center"
        sx={{ height: "100vh" }}
      >
        <img
          className="page_bg"
          src={loaderGif}
          alt="Loading..."
          style={{ width: "200px", height: "200px" }}
        />
        <span style={{ fontWeight: "600" }}>
          {loadingTexts[currentTextIndex]}
        </span>
      </Stack>
    );
  } else {
    if (!isNumeric(totalCarCount) && carsInfo.length <= 0) {
      return (
        <Stack
          alignItems="center"
          justifyContent="center"
          sx={{ height: "100vh" }}
        >
          <NoInventoryFound
            totalMcVehicleCount={totalMcVehicleCount}
            prevRoute={prevRoute}
            distanceOptions={distanceOptions}
            searhCriteria={searhCriteria}
            onUpdateDistance={handleUpdateDistance}
          />
        </Stack>
      );
    }
  }

  // Function to handle the redirect
  const handleRedirect = () => {
    if (prevRoute === "generic-search") {
      navigate("/generic-search");
    } else if (prevRoute === "specific-search") {
      navigate("/specific-search");
    } else {
      navigate("/");
    }
  };
  const showDealerName = import.meta.env.VITE_SHOW_DEALER_NAME === "true";
  return (
    <Box sx={{ flexGrow: 1, paddingY: "6%" }}>
      {loadingRightSwipe && ( // Render loader if either loadingRightSwipe is true
        <div className="loader-overlay">
          <div className="loader_container">
            <CircularProgress />
          </div>
        </div>
      )}
      {isBigScreen ? (
        <Grid container spacing={2}>
          <VehicleFilterButton
            handlePriceSortingChange={handlePriceSortingChange}
            selectedPriceSorting={selectedPriceSorting}
            sortingPriceOptions={sortingPriceOptions}
            handleDistanceSortingChange={handleDistanceSortingChange}
            selectedDistanceSorting={selectedDistanceSorting}
            sortingDistanceOptions={sortingDistanceOptions}
            onApply={handleApplySorting}
            showSortButton={!searhCriteria.vin}
          />
          <FloatingButton
            data={data}
            swipedCards={swipedCards}
            handleUndo={handleUndo}
            undoDisable={undoDisable}
          />
          {showSwipeCard && data.length > 0 && (
            <Grid item lg={12} id="swipe-card">
              <Item className="page_bg">
                <img
                  src={ImageBaseURL + "swipe_direction_tutorial.png"}
                  style={{
                    maxWidth: "20%",
                    maxHeight: "20%",
                    backgroundColor: "#ffffff",
                  }}
                  alt="swipe tutorial"
                />
              </Item>
              <Item className="page_bg">
                <Typography
                  variant={isBigScreen ? "h4" : "h6"}
                  sx={{ backgroundColor: "#ffffff" }}
                >
                  Showing <b>{vehicleCount}</b> of <b>{totalShowCount}</b>{" "}
                  Vehicles
                </Typography>
              </Item>
            </Grid>
          )}
          <Grid item lg={12} mt={4}>
            <Item className="page_bg">
              {data.length > 0 ? (
                <Stack
                  className="card_container"
                  sx={{
                    width: "100%",
                    height: totalCarCount <= 0 ? "100%" : "700px",
                    margin: isBigScreen ? "-2% auto" : "-8% auto",
                    overflow: "hidden",
                  }}
                >
                  <CardSwiper
                    data={data}
                    key={data.map((card) => card.id).join(",")}
                    onDismiss={(event, cardData) =>
                      handleVINCardDismiss(event, cardData)
                    }
                    withActionButtons
                    dislikeButton={
                      <Box
                        display="flex"
                        flexDirection="column"
                        alignItems="center"
                      >
                        <StyledButtonAccept size="large">
                          <Icon icon="swipe-left" size={35} />
                        </StyledButtonAccept>
                        <Typography
                          variant="body2"
                          sx={{ fontWeight: "bold" }}
                          mt={1}
                        >
                          {"Not a Match".toUpperCase()}
                        </Typography>
                      </Box>
                    }
                    likeButton={
                      <Box
                        display="flex"
                        flexDirection="column"
                        alignItems="center"
                      >
                        <StyledButtonAccept size="large">
                          <Icon icon="swipe-right" size={35} />
                        </StyledButtonAccept>
                        <Typography
                          variant="body2"
                          sx={{ fontWeight: "bold" }}
                          mt={1}
                        >
                          {"Good Match".toUpperCase()}
                        </Typography>
                      </Box>
                    }
                    withRibbons
                    likeRibbonText="GARAGE"
                    dislikeRibbonText="SKIP"
                    ribbonColors={{
                      bgLike: "green",
                      bgDislike: "red",
                      textColor: "white",
                    }}
                    preserveOrder={true}
                    emptyState={
                      <Box
                        sx={{
                          flexGrow: 1,
                          height: "100vh",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <Grid container spacing={2}>
                          <Grid item xs={12} mt={12}>
                            <Item className="page_bg">
                              <img
                                src={
                                  ImageBaseURL + "inventory_no_data_found.png"
                                }
                                alt="no data"
                                style={{ width: "100%", height: "100%" }}
                              />
                            </Item>
                          </Grid>
                          <Grid item xs={12}>
                            <Item className="page_bg">
                              <Typography
                                variant="subtitle2"
                                gutterBottom
                                sx={{ fontWeight: "800" }}
                              >
                                No More Vehicles to Show...
                              </Typography>
                              {!searhCriteria.vin && (
                                <Typography
                                  variant="caption"
                                  gutterBottom
                                  sx={{ fontWeight: "800" }}
                                >
                                  Please Try with Other Combinations.
                                </Typography>
                              )}
                            </Item>
                          </Grid>
                          <Grid item xs={12}>
                            <Item className="page_bg">
                              <StyledButton
                                variant="contained"
                                onClick={handleRedirect}
                              >
                                <Icon
                                  icon="phone-in-hand"
                                  size={40}
                                  style={{ marginRight: "20px" }}
                                />
                                try other search
                              </StyledButton>
                            </Item>
                          </Grid>
                          <Grid item xs={12}>
                            <Item className="page_bg">
                              <Typography
                                variant="caption"
                                gutterBottom
                                sx={{ fontWeight: "800" }}
                              >
                                {totalMcVehicleCount
                                  ? `Search Over ${totalMcVehicleCount.toLocaleString()} Vehicles.`
                                  : ""}
                              </Typography>
                            </Item>
                          </Grid>
                        </Grid>
                      </Box>
                    }
                    onFinish={handleFinish}
                    className="cardSwiperContainer"
                  />
                </Stack>
              ) : (
                <NoInventoryFound
                  totalMcVehicleCount={totalMcVehicleCount}
                  prevRoute={prevRoute}
                  distanceOptions={distanceOptions}
                  searhCriteria={searhCriteria}
                  onUpdateDistance={handleUpdateDistance}
                />
              )}
            </Item>
          </Grid>
        </Grid>
      ) : (
        <Grid container spacing={2} justifyContent="center" alignItems="center">
          <VehicleFilterButton
            handlePriceSortingChange={handlePriceSortingChange}
            selectedPriceSorting={selectedPriceSorting}
            sortingPriceOptions={sortingPriceOptions}
            handleDistanceSortingChange={handleDistanceSortingChange}
            selectedDistanceSorting={selectedDistanceSorting}
            sortingDistanceOptions={sortingDistanceOptions}
            onApply={handleApplySorting}
            showSortButton={!searhCriteria.vin}
          />
          <FloatingButton
            data={data}
            swipedCards={swipedCards}
            handleUndo={handleUndo}
            undoDisable={undoDisable}
          />
          {showSwipeCard && data.length > 0 && (
            <>
              <Grid
                item
                md={12}
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
                id="swipe-card"
              >
                <img
                  src={ImageBaseURL + "swipe_direction_tutorial.png"}
                  style={{
                    maxWidth: "20%",
                    maxHeight: "20%",
                    backgroundColor: "#ffffff",
                  }}
                  alt="swipe tutorial"
                />
              </Grid>
              <Grid
                item
                md={12}
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
                id="swipe-card"
                mt={isBigScreen ? 2 : 0}
                mb={1}
              >
                <Typography
                  variant={isBigScreen ? "h4" : "h6"}
                  sx={{ backgroundColor: "#ffffff" }}
                >
                  Showing <b>{vehicleCount}</b> of <b>{totalShowCount}</b>{" "}
                  Vehicles
                </Typography>
              </Grid>
              {showDealerName && (
                <Container>
                  <Typography variant="h4" component="h1" gutterBottom>
                    Dealer Vehicle Counts
                  </Typography>
                  {dealerList.length > 0 ? (
                    <Card sx={{ padding: 2 }}>
                      <TableContainer>
                        <Table sx={{ minWidth: 650 }}>
                          <TableHead>
                            <TableRow>
                              <TableCell>Dealer Name</TableCell>
                              <TableCell>Vehicle Count</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {dealerList.map((dealerItem, index) => (
                              <TableRow key={index}>
                                <TableCell>{dealerItem.dealer}</TableCell>
                                <TableCell>
                                  {dealerItem.count} vehicles
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Card>
                  ) : (
                    <Typography variant="body1">No data available</Typography>
                  )}
                </Container>
              )}
            </>
          )}
          <Grid
            item
            md={4}
            sx={{
              display: "flex",
              justifyContent: "end",
              alignItems: "center",
            }}
          />
          <Grid item md={4} mt={isBigScreen ? 2 : 0}>
            <Item
              className="page_bg"
              sx={{
                width: "100%",
                maxWidth: "700px",
                display: "block",
                textAlign: "center",
              }}
            >
              {data.length > 0 ? (
                <Stack
                  className="card_container"
                  sx={{
                    width: "100%",
                    height: totalCarCount <= 0 ? "100%" : "500px",
                    margin: isBigScreen ? "-2% auto" : "-8% auto",
                    overflow: "hidden",
                  }}
                >
                  <CardSwiper
                    data={data}
                    key={data.map((card) => card.id).join(",")}
                    onDismiss={(event, cardData) =>
                      handleVINCardDismiss(event, cardData)
                    }
                    withActionButtons
                    dislikeButton={
                      <Box
                        display="flex"
                        flexDirection="column"
                        alignItems="center"
                      >
                        <StyledButtonAccept size="large">
                          <Icon icon="swipe-left" size={35} />
                        </StyledButtonAccept>
                        <Typography
                          variant="body2"
                          sx={{ fontWeight: "bold" }}
                          mt={1}
                        >
                          {"Not a Match".toUpperCase()}
                        </Typography>
                      </Box>
                    }
                    likeButton={
                      <Box
                        display="flex"
                        flexDirection="column"
                        alignItems="center"
                      >
                        <StyledButtonAccept size="large">
                          <Icon icon="swipe-right" size={35} />
                        </StyledButtonAccept>
                        <Typography
                          variant="body2"
                          sx={{ fontWeight: "bold" }}
                          mt={1}
                        >
                          {"Good Match".toUpperCase()}
                        </Typography>
                      </Box>
                    }
                    withRibbons
                    likeRibbonText="GARAGE"
                    dislikeRibbonText="SKIP"
                    ribbonColors={{
                      bgLike: "green",
                      bgDislike: "red",
                      textColor: "white",
                    }}
                    preserveOrder={true}
                    emptyState={
                      <Box
                        sx={{
                          flexGrow: 1,
                          height: "100vh",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <Grid container spacing={2}>
                          <Grid item xs={12} mt={12}>
                            <Item className="page_bg">
                              <img
                                src={
                                  ImageBaseURL + "inventory_no_data_found.png"
                                }
                                alt="no data"
                                style={{ width: "100%", height: "100%" }}
                              />
                            </Item>
                          </Grid>
                          <Grid item xs={12}>
                            <Item className="page_bg">
                              <Typography
                                variant="subtitle2"
                                gutterBottom
                                sx={{ fontWeight: "800" }}
                              >
                                No More Vehicles to Show...
                              </Typography>
                              {!searhCriteria.vin && (
                                <Typography
                                  variant="caption"
                                  gutterBottom
                                  sx={{ fontWeight: "800" }}
                                >
                                  Please Try with Other Combinations.
                                </Typography>
                              )}
                            </Item>
                          </Grid>
                          <Grid item xs={12}>
                            <Item className="page_bg">
                              <StyledButton
                                variant="contained"
                                onClick={handleRedirect}
                              >
                                <Icon
                                  icon="phone-in-hand"
                                  size={40}
                                  style={{ marginRight: "20px" }}
                                />
                                try other search
                              </StyledButton>
                            </Item>
                          </Grid>
                          <Grid item xs={12}>
                            <Item className="page_bg">
                              <Typography
                                variant="caption"
                                gutterBottom
                                sx={{ fontWeight: "800" }}
                              >
                                {totalMcVehicleCount
                                  ? `Search Over ${totalMcVehicleCount.toLocaleString()} Vehicles.`
                                  : ""}
                              </Typography>
                            </Item>
                          </Grid>
                        </Grid>
                      </Box>
                    }
                    onFinish={handleFinish}
                    className="cardSwiperContainer"
                  />
                </Stack>
              ) : (
                <NoInventoryFound
                  totalMcVehicleCount={totalMcVehicleCount}
                  prevRoute={prevRoute}
                  distanceOptions={distanceOptions}
                  searhCriteria={searhCriteria}
                  onUpdateDistance={handleUpdateDistance}
                />
              )}
            </Item>
          </Grid>
          <Grid
            item
            md={4}
            sx={{
              display: "flex",
              justifyContent: "start",
              alignItems: "center",
            }}
          />
        </Grid>
      )}
      {openPriceModal && (
        <PurchasePowerConfirmationModal
          open={openPriceModal}
          onClose={() => handleModalClose(event, pendingSwipeData)}
          onConfirm={() => handleDismiss(event, pendingSwipeData)}
        />
      )}
    </Box>
  );
};

export default SwipeCardsWeb;
