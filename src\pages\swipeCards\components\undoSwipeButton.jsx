import { Box, Fab } from '@mui/material';
import ReplayIcon from '@mui/icons-material/Replay';
import PropTypes from "prop-types";
import { useMediaQuery } from 'react-responsive';

const FloatingButton = ({ data, swipedCards, handleUndo, undoDisable }) => {
    const isDisabled = swipedCards.length === 0 || undoDisable;
    const isBigScreen = useMediaQuery({ query: "(min-width: 1824px)" }); // Adjust the breakpoint as needed
    const isMobile = useMediaQuery({ maxWidth: 767 });

    // Set the left value of sort button based on screen size
    let leftValue;
    if (isBigScreen) {
      leftValue = '42%';
    } else if (isMobile) {
      leftValue = '10px';
    } else {
      leftValue = 'auto';
    }

    // Set the bottom value of sort button based on screen size
    let bottomValue;
    if (isBigScreen) {
      bottomValue = '120px';
    } else if (isMobile) {
      bottomValue = '13vh';
    } else {
      bottomValue = '15vh';
    }
    
    return data.length > 0 ? (
        <Box
            sx={{
                position: 'fixed',
                bottom: bottomValue,  // Add extra space to avoid footer overlap on big screen
                left: leftValue,
                transform: isBigScreen ? 'translateX(-50%)' : 'none', // Center horizontally
                right: isBigScreen || isMobile ? 'auto' : '20px',    // Keep right position when not on big screen
                zIndex: 1000,
            }}
        >
            <span>
                <Fab
                    color="primary"
                    aria-label="add"
                    onClick={handleUndo}
                    disabled={isDisabled}
                    title={isDisabled ? '' : 'Undo last swipe'}
                    sx={{
                        width: isBigScreen ? 80 : 56,  // Larger width if big screen
                        height: isBigScreen ? 80 : 56,  // Larger height if big screen
                    }}
                >
                    <ReplayIcon sx={{ fontSize: isBigScreen ? 40 : 24 }} /> {/* Larger icon size if big screen */}
                </Fab>
            </span>
        </Box>
    ) : null;
};

FloatingButton.propTypes = {
    data: PropTypes.array.isRequired,
    swipedCards: PropTypes.array.isRequired,
    handleUndo: PropTypes.func.isRequired,
    undoDisable: PropTypes.bool.isRequired
};

export default FloatingButton;
