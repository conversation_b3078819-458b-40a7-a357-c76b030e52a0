import { Box, Fab } from '@mui/material';
import PropTypes from "prop-types";
import { useMediaQuery } from 'react-responsive';
import TuneIcon from '@mui/icons-material/Tune';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import Tooltip from '@mui/material/Tooltip';
import VehicleSortingModal from '../modal/vehicleSortingModal';
import { useState, useEffect } from 'react';

const VehicleFilterButton = ({
    handlePriceSortingChange,
    selectedPriceSorting,
    sortingPriceOptions,
    onApply,
    showSortButton = true,
}) => {
    const isBigScreen = useMediaQuery({ query: "(min-width: 1824px)" }); // Adjust the breakpoint as needed
    const isMobile = useMediaQuery({ maxWidth: 767 });
    const [modalOpen, setModalOpen] = useState(false);
    const [tooltipOpen, setTooltipOpen] = useState(false);
    const [currentSorting, setCurrentSorting] = useState({
        price: selectedPriceSorting
    });

    // Update current sorting when session storage changes
    useEffect(() => {
        const storedPriceSorting = sessionStorage.getItem('priceSorting');
        
        setCurrentSorting({
            price: storedPriceSorting ? JSON.parse(storedPriceSorting) : selectedPriceSorting
        });
    }, [selectedPriceSorting]);

    const handleOpenModal = () => {
        setModalOpen(true);
    };
    const handleCloseModal = () => {
        setModalOpen(false);
    };

    const handleTooltipToggle = () => {
        setTooltipOpen(!tooltipOpen);
    };

    // Set the bottom value of sort button based on screen size
    let bottomValue;
    if (isBigScreen) {
        bottomValue = '120px';
    } else if (isMobile) {
        bottomValue = '13vh';
    } else {
        bottomValue = '26vh';
    }

    // Get the active sorting option for tooltip
    const getActiveSortingText = () => {
        // Get values from session storage
        const storedPriceSorting = sessionStorage.getItem('priceSorting');
        
        // If price sorting is stored, show that
        if (storedPriceSorting) {
            const priceSorting = JSON.parse(storedPriceSorting);
            return `Sorted by Price: ${priceSorting.label}`;
        }
        
        // Default to price ascending if nothing is stored
        return 'Sorted by Price: Low to High';
    };

    // If showSortButton is false, don't render anything
    if (!showSortButton) {
        return null;
    }

    return (
        <Box
            sx={{
                position: 'fixed',
                bottom: bottomValue,
                left: isBigScreen ? '71%' : 'auto',
                transform: isBigScreen ? 'translateX(-50%)' : 'none',
                right: isBigScreen || isMobile ? '10px' : '20px',
                zIndex: 1000,
            }}
        >
            <Box sx={{ position: 'relative', display: 'inline-block' }}>
                <Tooltip placement='left' title="Click to sort">
                    {/* Floating Sorting Button */}
                    <Fab
                        aria-label="Sort"
                        className='sort-button'
                        sx={{
                            width: isBigScreen ? 80 : 56,
                            height: isBigScreen ? 80 : 56,
                        }}
                        onClick={handleOpenModal}
                    >
                        <TuneIcon sx={{ fontSize: isBigScreen ? 40 : 24, color: "#4891FF" }} />
                    </Fab>
                </Tooltip>

                {/* Info Icon at top-right */}
                <Tooltip 
                    placement='top' 
                    title={getActiveSortingText()}
                    open={tooltipOpen}
                    onClose={() => setTooltipOpen(false)}
                    enterDelay={0}
                    leaveDelay={0}
                >
                    <InfoOutlinedIcon
                        sx={{
                            size: isBigScreen ? 20 : 10,
                            cursor: 'pointer',
                        }}
                        className='info-icon'
                        onClick={handleTooltipToggle}
                        onMouseEnter={() => setTooltipOpen(true)}
                        onMouseLeave={() => setTooltipOpen(false)}
                    />
                </Tooltip>
            </Box>
            <VehicleSortingModal
                open={modalOpen}
                onClose={handleCloseModal}
                handlePriceSortingChange={handlePriceSortingChange}
                selectedPriceSorting={selectedPriceSorting}
                sortingPriceOptions={sortingPriceOptions}
                onApply={onApply}
            />
        </Box>
    );
};

VehicleFilterButton.propTypes = {
    handlePriceSortingChange: PropTypes.func,
    selectedPriceSorting: PropTypes.object,
    sortingPriceOptions: PropTypes.array,
    onApply: PropTypes.func,
    showSortButton: PropTypes.bool,
};

export default VehicleFilterButton;
