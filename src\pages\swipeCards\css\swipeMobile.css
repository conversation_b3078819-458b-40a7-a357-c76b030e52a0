.card_container_mb {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    max-width: 400px;
    height: 500px;
}

@media only screen and (min-width: 768px) and (max-width: 1224px) {
    .card_container_mb {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        max-width: 600px !important;
        height: 700px !important;
        padding-bottom: 25px;
    }
}

.cardSwiperContainer_mb {
    width: 100%;
    max-width: 400px;
    height: auto;
}

.card {
    width: 100%;
    max-width: 300px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin: 16px;
}

.cardImage {
    height: 200px;
}

.emptyStateImage {
    width: 250px;
}

.emptyStateText {
    text-align: center;
    gap: 16px;
}

.swipe-card__image-container {
    position: relative !important;
    height: 45% !important;
}

.swipe-card__cards {
    position: relative !important;
    max-width: 100% !important;
    height: 100% !important;
}

.swipe-card__image {
    width: 100% !important;
    height: 100% !important;
    display: block !important;
    object-fit: cover !important;
}

.swipe-card__children {
    bottom: 0 !important;
    display: flex !important;
    justify-content: space-around !important;
    width: 100% !important;
    transition: all .7s ease !important;
    position: absolute !important;
}