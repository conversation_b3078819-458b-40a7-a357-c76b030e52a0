.card_container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  max-width: 400px;
}

.cardSwiperContainer {
  width: 100%;
  max-width: 400px;
  height: auto;
}

.card {
  width: 100%;
  max-width: 300px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 16px;
  padding-top: 10vh;
  padding-bottom: 10vh;
}

.cardImage {
  height: 200px;
}

.emptyStateImage {
  width: 250px;
}

.emptyStateText {
  text-align: center;
  gap: 16px;
}

.swipe-card {
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  gap: 30px !important;
  width: 80% !important;
  height: 80% !important;
  margin-top: 10px;
  margin-right: 5px;
}

/* Media query for mobile devices */
@media only screen and (max-width: 767px) {
  .swipe-card {
    width: 75% !important;
    height: 75% !important;
  }
}

@media only screen and (min-width: 768px) and (max-width: 1224px) {
  .swipe-card {
    width: 80% !important;
    height: 75% !important;
  }
}

.swipe-card__image-container {
  position: relative !important;
  height: 50% !important;
}

.swipe-card__cards {
  position: relative !important;
  max-width: 100% !important;
  height: 100% !important;
}

.swipe-card__image {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
  object-fit: contain !important;
}

.swipe-card__children {
  bottom: 0 !important;
  display: flex !important;
  justify-content: space-evenly !important;
  width: 100% !important;
  transition: all .7s ease !important;
  position: absolute !important;
  margin: 25% !important;
}

.swipe-card__ribbon-like,
.swipe-card__ribbon-dislike {
  width: 100px !important;
  height: 50px !important;
  font-size: 16px !important;
}

.swipe-card__container {
  box-shadow: 0 0 20px #0000001b !important;
}

@media only screen and (min-width: 1824px) {
  .swipe-card {
    width: 80% !important;
    height: 80% !important;
  }

  .card_container {
    max-width: 25%;
  }

  .swipe-card__children {
    margin: 20% !important;
  }
}