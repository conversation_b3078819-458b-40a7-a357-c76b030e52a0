import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography } from "@mui/material";

const PurchasePowerConfirmationModal = ({ open, onClose, onConfirm }) => {
  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>{"Vehicle Price is High"}</DialogTitle>
      <DialogContent>
        <Typography>{"The vehicle price is more than your purchase power amount. Do you want to continue?"}</Typography>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="secondary">
          Cancel
        </Button>
        <Button onClick={onConfirm} color="primary">
          Continue
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PurchasePowerConfirmationModal;
