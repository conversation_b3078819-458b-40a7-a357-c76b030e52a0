import { useState, useEffect } from "react";
import { useMediaQuery } from "react-responsive";
import PropTypes from "prop-types";
import {
    Box,
    Button,
    TextField,
    Autocomplete,
    Typography,
} from "@mui/material";
import { Modal } from "react-bootstrap";
import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";

const Item = styled(Paper)(({ theme }) => ({
    ...theme.typography.body2,
    padding: theme.spacing(1),
    textAlign: "center",
    borderRadius: "none",
    boxShadow: "none",
}));

const CustomAutocomplete = styled(Autocomplete)(({ disabled }) => ({
    "& .MuiOutlinedInput-root": {
        "& fieldset": {
            borderColor: "#4891FF",
            borderWidth: "2px",
        },
        "&:hover fieldset": {
            borderColor: "#4891FF",
        },
        "&.Mui-focused fieldset": {
            borderColor: "#4891FF",
        },
        "& input": {
            fontWeight: "semibold",
        },
    },
    "& .MuiAutocomplete-option": {
        fontWeight: "semibold",
    },
    "& .MuiSvgIcon-root": {
        color: disabled ? "#C0C0C0" : "#4891FF",
    },
}));

const StyledButton = styled(({ ...other }) => <Button {...other} />)(
    ({ btnWidth }) => ({
        borderRadius: "10px",
        width: btnWidth,
        fontWeight: "600",
        fontSize: "12px",
        padding: "12px",
        backgroundColor: "#4891FF",
        color: "#fff",

        "&:hover": {
            backgroundColor: "#357AE8",
        },

        "&.Mui-disabled": {
            backgroundColor: "#4891FF",
            color: "#fff",
            opacity: 0.6,
            cursor: "not-allowed",
        },
    })
);

const VehicleSortingModal = ({
    open,
    onClose,
    handlePriceSortingChange,
    selectedPriceSorting,
    sortingPriceOptions,
    onApply,
}) => {
    const isMobile = useMediaQuery({ maxWidth: 767 });
    const isBigScreen = useMediaQuery({ query: "(min-width: 2560px)" });
    // Set the width of sort button based on screen size
    let btnWidth;
    if (isBigScreen) {
        btnWidth = '20%';
    } else if (isMobile) {
        btnWidth = '25%';
    } else {
        btnWidth = '18%';
    }

    // Local state to track changes before applying
    const [localPriceSorting, setLocalPriceSorting] = useState(selectedPriceSorting);
    // Add state for warning message
    const [warningMessage, setWarningMessage] = useState('');

    // Update local state when modal opens
    useEffect(() => {
        if (open) {
            // Get values from session storage or use props
            const storedPriceSorting = sessionStorage.getItem('priceSorting');
            
            setLocalPriceSorting(storedPriceSorting ? JSON.parse(storedPriceSorting) : selectedPriceSorting);
            setWarningMessage(''); // Clear warning message when modal opens
        }
    }, [open, selectedPriceSorting]);

    // Handle price sorting change locally
    const handleLocalPriceChange = (event, newValue) => {
        setLocalPriceSorting(newValue);
        setWarningMessage(''); // Clear warning message when selection changes
    };

    // Handle apply button click
    const handleApply = () => {
        // Get the previously stored values from session storage
        const storedPriceSorting = sessionStorage.getItem('priceSorting');
        
        // Parse the stored values if they exist
        const previousPriceSorting = storedPriceSorting ? JSON.parse(storedPriceSorting) : selectedPriceSorting;

        // Check if there are any changes compared to the stored values
        const hasPriceChanged = localPriceSorting.label !== previousPriceSorting.label;
        
        if (hasPriceChanged) {
            // First clear storage key
            sessionStorage.removeItem('priceSorting');

            // Store the new value
            sessionStorage.setItem('priceSorting', JSON.stringify(localPriceSorting));
            
            // Call onApply with the final value
            onApply(localPriceSorting);
            onClose();
        } else {
            // Show warning message if no changes were made
            setWarningMessage("You are trying to apply the same sorting parameters. Please select a different sorting option to see changes.");
        }
    };

    return (
        <Modal show={open} onHide={onClose} centered size="md">
            <Modal.Header className="modal-header-fixed" closeButton>
                <Modal.Title>Sort By</Modal.Title>
            </Modal.Header>
            <Modal.Body className="modal-body-scrollable">
                <Box sx={{ flexGrow: 1 }}>
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <Item className="page_bg">
                                <CustomAutocomplete
                                    id="price-sorting-select"
                                    options={sortingPriceOptions}
                                    value={localPriceSorting}
                                    onChange={handleLocalPriceChange}
                                    disableClearable
                                    autoHighlight
                                    getOptionLabel={(option) => option.label}
                                    fullWidth
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            label="Price"
                                            InputLabelProps={{
                                                classes: {
                                                    root: "input_fonts",
                                                },
                                            }}
                                        />
                                    )}
                                />
                                {warningMessage && (
                                    <Typography 
                                        variant="caption" 
                                        color="error" 
                                        sx={{ 
                                            display: 'block', 
                                            mt: 1,
                                            textAlign: 'center'
                                        }}
                                    >
                                        {warningMessage}
                                    </Typography>
                                )}
                            </Item>
                        </Grid>
                    </Grid>
                </Box>
            </Modal.Body>
            <Modal.Footer className="modal-footer-fixed">
                <StyledButton
                    variant="contained"
                    btnWidth={btnWidth}
                    onClick={handleApply}
                >
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                        <svg fill="#ffffff" width="18px" height="18px" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                            <g id="SVGRepo_iconCarrier">
                                <path d="M12,25l6.67,6.67a1,1,0,0,0,.7.29.91.91,0,0,0,.39-.08,1,1,0,0,0,.61-.92V13.08L31.71,1.71A1,1,0,0,0,31.92.62,1,1,0,0,0,31,0H1A1,1,0,0,0,.08.62,1,1,0,0,0,.29,1.71L11.67,13.08V24.33A1,1,0,0,0,12,25ZM3.41,2H28.59l-10,10a1,1,0,0,0-.3.71V28.59l-4.66-4.67V12.67a1,1,0,0,0-.3-.71Z" />
                            </g>
                        </svg>
                        Apply
                    </Box>
                </StyledButton>
            </Modal.Footer>
        </Modal>
    );
};

// Update propTypes for VehicleSortingModal
VehicleSortingModal.propTypes = {
    open: PropTypes.bool,
    onClose: PropTypes.func,
    handlePriceSortingChange: PropTypes.func,
    selectedPriceSorting: PropTypes.object,
    sortingPriceOptions: PropTypes.array,
    onApply: PropTypes.func,
};

export default VehicleSortingModal;
