import PropTypes from "prop-types";
import { Routes, Route, Navigate } from "react-router-dom";
import Home from "../pages/home/<USER>/homeIndex";
import NoInternet from "../pages/connectionError/noInternet/noInternetIndex";
import NotFound from "../pages/connectionError/notFound/notFoundIndex";
import AccessDenied from "../pages/connectionError/accessDenied/accessDeniedIndex";
import AuthRoutes from "./auth";
import LandingRoutes from "./landing";
import SpecificSearchRoutes from "./search";
import LoanRoutes from "./loan";
import SwipeCardsRoutes from "./inventory"; // Assuming SwipeCardsRoutes is imported correctly
import GarageRoutes from "./garage";
import GenericSearchRoutes from "./genericSearch";
import ContactUsRoutes from "./contactUs";
import ChatRoutes from "./chat";
import CuChatRoutes from "./msrChat";
import FpAdvisoryChatRoutes from "./fpAdvisoryChat";

const AppRoutes = ({ isOnline, token }) => (
  <Routes>
    {isOnline ? (
      <>
        <Route path="/" element={<Home />} />
        {AuthRoutes.map((route, index) => (
          <Route key={index} path={route.path} element={route.element} />
        ))}

        {token ? (
          <>
            {LandingRoutes.map((route, index) => (
              <Route key={index} path={route.path} element={route.element} />
            ))}
            {SpecificSearchRoutes.map((route, index) => (
              <Route key={index} path={route.path} element={route.element} />
            ))}
            {LoanRoutes.map((route, index) => (
              <Route key={index} path={route.path} element={route.element} />
            ))}
            {SwipeCardsRoutes.map((route, index) => (
              <Route key={index} path={route.path} element={route.element} />
            ))}
            {GarageRoutes.map((route, index) => (
              <Route key={index} path={route.path} element={route.element} />
            ))}
            {GenericSearchRoutes.map((route, index) => (
              <Route key={index} path={route.path} element={route.element} />
            ))}
            {ContactUsRoutes.map((route, index) => (
              <Route key={index} path={route.path} element={route.element} />
            ))}
            {ChatRoutes.map((route, index) => (
              <Route key={index} path={route.path} element={route.element} />
            ))}
            {CuChatRoutes.map((route, index) => (
              <Route key={index} path={route.path} element={route.element} />
            ))}
            {FpAdvisoryChatRoutes.map((route, index) => (
              <Route key={index} path={route.path} element={route.element} />
            ))}
          </>
        ) : (
            <Route path="*" element={<Navigate to="/send-otp" replace />} />
        )}
        <Route path="/access-denied" element={<AccessDenied />} />
        <Route path="*" element={<NotFound />} />
      </>
    ) : (
      <Route path="/no-internet" element={<NoInternet />} />
    )}
  </Routes>
);

AppRoutes.propTypes = {
  isOnline: PropTypes.bool.isRequired,
  token: PropTypes.string,
};

export default AppRoutes;
