// eslint-disable-next-line no-unused-vars
import React from "react";
import { Navigate } from "react-router-dom";
import PropTypes from "prop-types";
const MobileRoute = ({ children }) => {
  // Define a maximum width for mobile and tablet devices
  const isMobileOrTablet = window.innerWidth <= 1024;

  return isMobileOrTablet ? children : <Navigate to="/conversations" />;
};
MobileRoute.propTypes = {
  children: PropTypes.node.isRequired,
};
export default MobileRoute;
