const fs = require('fs');
const path = require('path');
const axios = require('axios');

const baseURL = process.env.VITE_BACKEND_URL;

if (!baseURL) {
  console.error('BASE_URL environment variable is missing!');
  process.exit(1); // Exit if env variable is missing
}

const constructURL = (endpoint) => `${baseURL}/${endpoint}`;

async function fetchMakeData() {
  try {
    const response = await axios.post(constructURL('api/activity/fetch-page-meta'));
    return response.data;
  } catch (error) {
    console.error('Error fetching make data:', error.message);
    throw error;
  }
}

async function generateMakeData() {
  try {
    const filePath = path.resolve(process.cwd(), 'data', 'pageMetaData.js');
    const dirPath = path.dirname(filePath);

    // Fetch data
    const data = await fetchMakeData();

    // Ensure directory exists
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }

    // Write data to file
    const fileContent = `export const pageMetaData = ${JSON.stringify(data.data, null, 2)};`;
    fs.writeFileSync(filePath, fileContent, 'utf8');

    console.log('Data file created successfully at:', filePath);
  } catch (error) {
    console.error('Error generating page meta data:', error.message);
    process.exit(1); // Exit with error
  }
}

// Run the function
generateMakeData();
