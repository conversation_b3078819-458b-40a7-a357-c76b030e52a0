import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
let baseAIURL = '';
if (import.meta.env.VITE_LOCAL_BACKEND_AI_URL) {
  baseAIURL = import.meta.env.VITE_LOCAL_BACKEND_AI_URL;
} else {
  baseAIURL = import.meta.env.VITE_FASTPASS_AI_BACKEND_URL;
}

// Construct full API URL
const constructURL = (endpoint) => `${baseAIURL}/${endpoint}`;

// Async thunk to fetch chat history list
export const getChatHistoryList = createAsyncThunk(
  "appAIChat/getChatHistoryList",
  async ({ page, page_size, search, token }, { rejectWithValue }) => {
    try {
      const payloadData = {
        page,
        page_size,
        search
      };

      const config = {
        headers: {
          "Authorization": 'Bearer ' + token,
          'Content-Type': 'application/json'
        }
      };

      const res = await axios.post(
        constructURL("vehicle/fetch-chat-history-list"),
        payloadData,
        config
      );

      return res.data;
    } catch (err) {
      if (err.response) {
        return rejectWithValue(err.response.data);
      } else if (err.request) {
        return rejectWithValue("No response received from the server");
      } else {
        return rejectWithValue("An error occurred while processing your request");
      }
    }
  }
);

export const sendUserQuery = createAsyncThunk(
  "appAIChat/sendUserQuery",
  async (params, { rejectWithValue }) => {
    try {
      const { token, ...payloadData } = params;
      const config = {
        headers: {
          "Authorization": 'Bearer ' + token,
          'Content-Type': 'application/json'
        }
      };

      const res = await axios.post(
        constructURL("vehicle/query"),
        payloadData,
        config
      );

      return res.data; // Return the response data
    } catch (err) {
      if (err.response) {
        return rejectWithValue(err.response.data);
      } else if (err.request) {
        return rejectWithValue("No response received from the server");
      } else {
        return rejectWithValue("An error occurred while processing your request");
      }
    }
  }
);

export const fetchSpecificConversations = createAsyncThunk(
  "appAIChat/fetchSpecificConversations",
  async (params, { rejectWithValue }) => {
    try {
      const { token, ...payloadData } = params;
      const config = {
        headers: {
          "Authorization": 'Bearer ' + token,
          'Content-Type': 'application/json'
        }
      };

      const res = await axios.post(
        constructURL("vehicle/fetch-chat-history"),
        payloadData,
        config
      );

      return res.data; // Return the response data
    } catch (err) {
      if (err.response) {
        return rejectWithValue(err.response.data);
      } else if (err.request) {
        return rejectWithValue("No response received from the server");
      } else {
        return rejectWithValue("An error occurred while processing your request");
      }
    }
  }
);

export const messsageFeedback = createAsyncThunk(
  "appAIChat/messsageFeedback",
  async (params, { rejectWithValue }) => {
    try {
      const { token, ...payloadData } = params;
      const config = {
        headers: {
          "Authorization": 'Bearer ' + token,
          'Content-Type': 'application/json'
        }
      };

      const res = await axios.post(
        constructURL("vehicle/feedback"),
        payloadData,
        config
      );

      return res.data; // Return the response data
    } catch (err) {
      if (err.response) {
        return rejectWithValue(err.response.data);
      } else if (err.request) {
        return rejectWithValue("No response received from the server");
      } else {
        return rejectWithValue("An error occurred while processing your request");
      }
    }
  }
);

export const checkAIHealth = createAsyncThunk(
  "appAIChat/checkAIHealth",
  async (token, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          "Authorization": 'Bearer ' + token,
          'Content-Type': 'application/json'
        }
      };
      const res = await axios.get(
        constructURL("health"),
        config
      );
      return res.data;
    } catch (err) {
      if (err.response) {
        return rejectWithValue(err.response.data);
      } else if (err.request) {
        return rejectWithValue("No response received from the server");
      } else {
        return rejectWithValue("An error occurred while processing your request");
      }
    }
  }
);

// Slice definition
const appAIChatSlice = createSlice({
  name: "appAIChat",
  initialState: {
    chatHistory: [],
    currentConversation: null,
    individualChatHistory: [],
    feedbackResponse: [],
    aiHealth: null,
    aiHealthLoading: false,
    aiHealthError: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(getChatHistoryList.fulfilled, (state, action) => {
      state.chatHistory = action.payload;
    });
    builder.addCase(sendUserQuery.fulfilled, (state, action) => {
      state.currentConversation = action.payload;
    });
    builder.addCase(fetchSpecificConversations.fulfilled, (state, action) => {
      state.individualChatHistory = action.payload;
    });
    builder.addCase(messsageFeedback.fulfilled, (state, action) => {
      state.feedbackResponse = action.payload;
    });
    builder
      .addCase(checkAIHealth.pending, (state) => {
        state.aiHealthLoading = true;
        state.aiHealthError = null;
      })
      .addCase(checkAIHealth.fulfilled, (state, action) => {
        state.aiHealth = action.payload;
        state.aiHealthLoading = false;
        state.aiHealthError = null;
      })
      .addCase(checkAIHealth.rejected, (state, action) => {
        state.aiHealth = null;
        state.aiHealthLoading = false;
        state.aiHealthError = action.payload || "Unknown error";
      });
  },
});

export default appAIChatSlice.reducer;
