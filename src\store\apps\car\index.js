import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { formatTimeStamp } from '../../../util/formatTimeStamp';

import axios from "axios";

const baseURL = import.meta.env.VITE_BACKEND_URL;
// Create a function to construct the full URL using the baseURL
const constructURL = (endpoint) => {
  return `${baseURL}/${endpoint}`;
};

export const getcarInventory = createAsyncThunk(
  "appCar/getcarInventory",
  async (
    {
      token,
      bodyType,
      vin,
      carmodel,
      caryear,
      carbrand,
      latitude,
      longitude,
      trims,
      exterior_colors,
      interior_colors,
      fuel_types,
      distance,
      zip,
      page,
      page_size,
      sort_by,
      sort_order,
      userAvailableAmount,
      userCarLookingOption,
      priceRangeValue,
      yearRangeValue,
      yearValuesMatch,
      priceValuesMatch,
      addedCarinGarageVins
    },
    { rejectWithValue }
  ) => {
    try {
      const requestData = {
        bodyType: bodyType,
        vin: vin,
        model: carmodel,
        year: caryear,
        make: carbrand,
        latitude: latitude,
        longitude: longitude,
        trims: trims,
        exterior_colors: exterior_colors,
        interior_colors: interior_colors,
        fuel_types: fuel_types,
        distance: distance,
        zip: zip,
        page: page,
        page_size: page_size,
        sort_by: sort_by,
        sort_order: sort_order,
        user_available_amount: userAvailableAmount,
        userCarLookingOption: userCarLookingOption,
        priceRangeValue: priceRangeValue,
        yearRangeValue: yearRangeValue,
        yearValuesMatch: yearValuesMatch,
        priceValuesMatch: priceValuesMatch,
        addedCarinGarageVins: addedCarinGarageVins
      };
      // Add token to the request headers
      const config = {
        headers: {
          Authorization: token,
        },
      };
      const res = await axios.post(
        constructURL(`api/fetch-vehicle-inventory`),
        requestData,
        config
      );

      return res.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

//function for sending email when no auto expert found
export const sendEmail = createAsyncThunk(
  "appCar/sendEmail",
  async (
    { name, dealer_name, from_credit, address, loan_id, mail_body, authToken },
    { rejectWithValue }
  ) => {
    try {
      const config = {
        headers: {
          Authorization: authToken,
        },
      };
      const requestData = {
        name: name,
        dealer_name: dealer_name,
        from_credit: from_credit,
        address: address,
        loan_id: loan_id,
        mail_body: mail_body,
      };
      const response = await axios.post(
        constructURL(`api/dealer-query-email-send`),
        requestData,
        config
      );
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const contactUsEmail = createAsyncThunk(
  "appCar/contactUsEmail",
  async (
    { name, email, from_credit, loan_id, mail_body, token },
    { rejectWithValue }
  ) => {
    try {
      const config = {
        headers: {
          Authorization: token,
        },
      };
      const requestData = {
        name: name,
        email: email,
        from_credit: from_credit,
        loan_id: loan_id,
        mail_body: mail_body,
      };
      const response = await axios.post(
        constructURL(`api/contact-us-email`),
        requestData,
        config
      );
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

//function for right swiping
export const rightSwipe = createAsyncThunk(
  "appCar/rightSwipe",
  async ({ swipedData, token }, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          Authorization: token,
        },
      };
      const requestData = {
        row_id: swipedData.inventory_row_id,
        stock_number: swipedData.stock_number,
        conversation_details_url: swipedData.conversation_details_url,
        vin: swipedData.vin
      };
      const response = await axios.post(
        constructURL("api/add-vehicle-to-garage"),
        requestData,
        config
      );
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

// New function for add-vehicle-to-garage-redirection
export const addVehicleToGarageRedirection = createAsyncThunk(
  "appCar/addVehicleToGarageRedirection",
  async ({ row_id, member_id, token }, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          Authorization: token,
        },
      };
      const requestData = {
        row_id: row_id,
        member_id: member_id,
      };
      const response = await axios.post(
        constructURL("api/add-vehicle-to-garage-redirect"),
        requestData,
        config
      );
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        return rejectWithValue("No response received from the server");
      } else {
        return rejectWithValue("An error occurred while processing your request");
      }
    }
  }
);

//fetching right swiped cars at my garage
export const getCars = createAsyncThunk(
  "appCar/getCars",
  async ({ memberId, page, page_size, token, offset }, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          Authorization: token,
        },
      };
      const requestData = {
        user_id: memberId,
        page: page,
        page_size: page_size,
        offset: offset,
      };
      const response = await axios.post(
        constructURL("api/fetch-vehicle-from-garage-user-wise"),
        requestData,
        config
      );
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

//function for sending email when no auto expert found
export const sendHelpQueryEmail = createAsyncThunk(
  "appCar/sendEmail",
  async (
    { name, from_credit, loan_id, mail_body, authToken },
    { rejectWithValue }
  ) => {
    try {
      const config = {
        headers: {
          Authorization: authToken,
        },
      };

      const requestData = {
        name: name,
        from_credit: from_credit,
        loan_id: loan_id,
        mail_body: mail_body,
      };
      const response = await axios.post(
        constructURL(`api/send-user-help-request-email`),
        requestData,
        config
      );
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

// Define an asynchronous thunk action to fetch vehicle make data
export const getMakeData = createAsyncThunk(
  "appCar/getMakeData",
  async (token, { rejectWithValue }) => {
    try {
      // Add token to the request headers
      const config = {
        headers: {
          Authorization: token,
        },
      };
      // Make a GET request to fetch the vehicle make list
      const response = await axios.get(
        constructURL("api/fetch-vehicle-make-list"),
        config
      );
      // Return the response data if the request is successful
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

// Define an asynchronous thunk action to fetch vehicle make data according the selected bodytype
export const getMakeFromBodytype = createAsyncThunk(
  "appCar/getMakeFromBodyType",
  async (token, { rejectWithValue }) => {
    try {
      // Add token to the request headers
      const config = {
        headers: {
          Authorization: token,
        },
      };
      // Make a GET request to fetch the vehicle make list
      const response = await axios.get(
        constructURL("api/fetch-make-from-bodytype"),
        config
      );
      // Return the response data if the request is successful
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

// Define an asynchronous thunk action to fetch vehicle model data
export const getModelData = createAsyncThunk(
  "appCar/getModelData",
  async (token, { rejectWithValue }) => {
    try {
      // Add token to the request headers
      const config = {
        headers: {
          Authorization: token,
        },
      };
      // Make a GET request to fetch the vehicle model list
      const response = await axios.get(
        constructURL("api/fetch-vehicle-model-list"),
        config
      );
      // Return the response data if the request is successful
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

//fetching right swiped cars at my garage
export const deleteCarFromMyGarage = createAsyncThunk(
  "appCar/deleteCarFromMyGarage",
  async ({ vin, token, mc_row_id }, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          Authorization: token,
        },
      };
      const requestData = {
        vin: vin,
        mc_row_id: mc_row_id,
      };
      const response = await axios.post(
        constructURL("api/delete-vehicle-from-garage-user-wise"),
        requestData,
        config
      );
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

const constructGetRequestURL = (path, params) => {
  const url = new URL(path, baseURL); // Replace with your base URL
  Object.keys(params).forEach((key) =>
    url.searchParams.append(key, params[key])
  );
  return url.toString();
};

export const getVehicleDetails = createAsyncThunk(
  "appCar/getVehicleDetails",
  async ({ vin, token }, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          Authorization: token,
        },
      };

      const url = constructGetRequestURL("api/fetch-vehicle-details", { vin });

      const response = await axios.get(url, config);
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

// Define an asynchronous thunk action to fetch vehicle body type data
export const getBodyTypes = createAsyncThunk(
  "appCar/getBodyTypes",
  async (token, { rejectWithValue }) => {
    try {
      // Add token to the request headers
      const config = {
        headers: {
          Authorization: token,
        },
      };
      // Make a GET request to fetch the vehicle make list
      const response = await axios.get(
        constructURL("api/fetch-vehicle-body-type-list"),
        config
      );
      // Return the response data if the request is successful
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

// Define an function to fetch vehicle data
export const getVehicleDetailsInventory = createAsyncThunk(
  "appCar/getVehicleDetailsInventory",
  async ({ row_id, token }, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          Authorization: token,
        },
      };

      const url = constructGetRequestURL(
        "api/fetch-inventory-vehicle-details",
        { row_id }
      );

      const response = await axios.get(url, config);
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

// Define an asynchronous thunk action to fetch vehicle trim data
export const getTrimData = createAsyncThunk(
  "appCar/getTrimData",
  async (token, { rejectWithValue }) => {
    try {
      // Add token to the request headers
      const config = {
        headers: {
          Authorization: token,
        },
      };
      // Make a GET request to fetch the vehicle trim list
      const response = await axios.get(
        constructURL("api/fetch-vehicle-trim-list"),
        config
      );
      // Return the response data if the request is successful
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const fetchDealerResponse = createAsyncThunk(
  "appCar/fetchDealerResponse",
  async ({ car, token }, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          Authorization: token,
        },
      };
      const requestData = {
        vin: car.vin,
        fp_dealer_id: car.fp_dealer_id,
        vehicle_row_id: car.mc_row_id,
      };
      const response = await axios.post(
        constructURL(`api/fetch-ask-dealer-response`),
        requestData,
        config
      );
      return response;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const sendDealerTextRequest = createAsyncThunk(
  "appCar/sendDealerTextRequest",
  async ({ carDetails, userMsg, value, testDriveMessage, token }, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          Authorization: token,
        },
      };
      const formattedValue = value ? formatTimeStamp(value) : '';
      const baseUrl = `${window.location.protocol}//${window.location.host}`;
      const conversation_details_url = `${baseUrl}/conversation`
      // Get the local timezone
      const localTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      // Determine the message to send
      const message = formattedValue ? `${testDriveMessage} ${formattedValue}?` : userMsg;
      const requestData = {
        vin: carDetails.vin,
        fp_dealer_id: carDetails.fp_dealer_id,
        vehicle_name: carDetails.heading,
        year: carDetails.year,
        vehicle_row_id: carDetails.mc_row_id,
        test_drive_date_time: value,
        request_type: "askDealer",
        message: message,
        conversation_details_url: conversation_details_url,
        localTimezone: localTimezone
      };
      const response = await axios.post(
        constructURL(`api/send-ask-dealer-request`),
        requestData,
        config
      );
      return response;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

// Define an function to fetch vehicle vins which are stored in garage
export const getGarageVehicleVins = createAsyncThunk(
  "appCar/getGarageVehicleVins",
  async (token, { rejectWithValue }) => {
    try {
      // Add token to the request headers
      const config = {
        headers: {
          Authorization: token,
        },
      };
      // Make a GET request to fetch the vehicle make list
      const response = await axios.get(
        constructURL("api/fetch-garage-vehicle-memberid"),
        config
      );
      return response.data.data ? response.data.data.split(",").map(item => item.trim().replace(/^'|'$/g, "")) : [];
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

// Define an function to fetch vehicle vins which are stored in garage
export const getVehicleMinPrice = createAsyncThunk(
  "appCar/getVehicleMinPrice",
  async (token, { rejectWithValue }) => {
    try {
      // Add token to the request headers
      const config = {
        headers: {
          Authorization: token,
        },
      };
      // Make a GET request to fetch the vehicle make list
      const response = await axios.get(
        constructURL("api/fetch-inventory-vehicle-min-price"),
        config
      );
      // Return the response data if the request is successful
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const undoDeletedCarFromMyGarage = createAsyncThunk(
  "appCar/undoDeletedCarFromMyGarage",
  async ({ vin, token, mc_row_id, otd_price, is_negotiation }, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          Authorization: token,
        },
      };
      const requestData = {
        mc_row_id: mc_row_id,
        vin: vin,
        otd_price: otd_price,
        is_negotiation: is_negotiation,
      };
      const response = await axios.post(
        constructURL("api/undo-deleted-vehicle-from-garage-user-wise"),
        requestData,
        config
      );
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const getAISearchCarInventory = createAsyncThunk(
  "appCar/getAISearchCarInventory",
  async (
    {
      token,
      latitude,
      longitude,
      query,
      existingCarInfo,
      distance,
      zip,
      page,
      page_size,
      sort_by,
      sort_order,
    },
    { rejectWithValue }
  ) => {
    try {
      const requestData = {
        query: query || '',
        distance: distance,
        latitude: latitude,
        longitude: longitude,
        zip: zip,
        page: page,
        page_size: page_size,
        sort_by: sort_by,
        sort_order: sort_order,
        existingCarInfo: existingCarInfo,
      };
      // Add token to the request headers
      const config = {
        headers: {
          Authorization: token,
        },
      };
      const res = await axios.post(
        constructURL(`api/ai-search`),
        requestData,
        config
      );

      return res.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const generateAiNegotiation = createAsyncThunk(
  "appCar/generateAiNegotiation",
  async ({ vehicles_data, token }, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          Authorization: token,
        },
      };
      const requestData = {
        vehicles_data: vehicles_data
      };
      const response = await axios.post(
        constructURL("api/generate-ai-negotiation"),
        requestData,
        config
      );
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

// integrating api for share fastpass timestamp & flag
export const sendShareFastpassDetails = createAsyncThunk(
  "appCar/sendShareFastpassDetails",
  async ({ carDetails, token }, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          Authorization: token,
        },
      };
      // Get the local timezone
      const localTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const requestData = {
        vin: carDetails.vin,
        fp_dealer_id: carDetails.fp_dealer_id,
        vehicle_name: carDetails.heading,
        vehicle_row_id: carDetails.mc_row_id,
        localTimezone: localTimezone
      };
      const response = await axios.post(
        constructURL(`api/insert-share-fastpass-details`),
        requestData,
        config
      );
      return response;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const appCarSlice = createSlice({
  name: "appCar",
  initialState: {
    loading: true,
    mail: [],
    swipe: [],
    searchData: [],
    myGarageData: [],
    makeData: [],
    modelData: [],
    bodyTypes: [],
    trimData: [],
    vehicleDetails: [],
    vehicleDetailsInventory: [],
    routeCheck: null, //checking previous route
    dealerResponseDetails: [],
    dealerRequest: [],
    carAddedInGarageVins: [],
    inventoryVehicleMinPrice: [],
    restoredVehicle: [],
    aiNegotiationVehicles: [],
  },
  reducers: {
    setRouteCheck: (state, action) => {
      state.routeCheck = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(getCars.fulfilled, (state, action) => {
      state.myGarageData = action.payload;
    });
    builder.addCase(getMakeData.fulfilled, (state, action) => {
      state.makeData = action.payload;
    });
    builder.addCase(getModelData.fulfilled, (state, action) => {
      state.modelData = action.payload;
    });
    builder.addCase(getBodyTypes.fulfilled, (state, action) => {
      state.bodyTypes = action.payload;
    });
    builder.addCase(getVehicleDetails.fulfilled, (state, action) => {
      state.vehicleDetails = action.payload;
    });
    builder.addCase(getVehicleDetailsInventory.fulfilled, (state, action) => {
      state.vehicleDetailsInventory = action.payload;
    });
    builder.addCase(getTrimData.fulfilled, (state, action) => {
      state.trimData = action.payload;
    });
    builder.addCase(fetchDealerResponse.fulfilled, (state, action) => {
      state.dealerResponseDetails = action.payload;
    });
    builder.addCase(sendDealerTextRequest.fulfilled, (state, action) => {
      state.dealerRequest = action.payload;
    });
    builder.addCase(getGarageVehicleVins.fulfilled, (state, action) => {
      state.carAddedInGarageVins = action.payload;
    });
    builder.addCase(getVehicleMinPrice.fulfilled, (state, action) => {
      state.inventoryVehicleMinPrice = action.payload;
    });
    builder.addCase(undoDeletedCarFromMyGarage.fulfilled, (state, action) => {
      state.restoredVehicle = action.payload;
    });
    builder.addCase(generateAiNegotiation.fulfilled, (state, action) => {
      state.aiNegotiationVehicles = action.payload;
    });
  },
});

export const { setRouteCheck } = appCarSlice.actions;

export default appCarSlice.reducer;
