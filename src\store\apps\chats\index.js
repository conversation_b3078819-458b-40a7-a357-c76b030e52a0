import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";

import axios from "axios";

const baseURL = import.meta.env.VITE_BACKEND_URL;
// Create a function to construct the full URL using the baseURL
const constructURL = (endpoint) => {
	return `${baseURL}/${endpoint}`;
};

export const getUserChatList = createAsyncThunk(
	"appChat/getUserChatList",
	async (
		{
			token,
			page,
			page_size,
			sort_type,
			order_type,
			search_text,
		},
		{ rejectWithValue }
	) => {
		try {
			// Add token to the request headers and page information to the request body
			const config = {
				headers: {
					Authorization: token,
				}
			};

			const body = {
				page: page,
				page_size: page_size,
				sort_type: sort_type,
				order_type: order_type,
				search_text: search_text,
			};

			const res = await axios.post(
				constructURL(`api/fetch-conversations-list`),
				body,
				config
			);
			return res.data.userConversationList.response;
		} catch (error) {
			// Handle the error, for example:
			if (error.response) {
				return rejectWithValue(error.response.data);
			} else if (error.request) {
				return rejectWithValue("No response received from the server");
			} else {
				return rejectWithValue("An error occurred while processing your request");
			}
		}
	}
);

export const getVehicleDetailsByChatId = createAsyncThunk(
	"appChat/getUserChatList",
	async (
		{
			token,
			conversation_id
		},
		{ rejectWithValue }
	) => {
		try {
			// Add token to the request headers and conversation_id to the request body
			const config = {
				headers: {
					Authorization: token,
				}
			};

			const body = {
				conversation_id: conversation_id
			};

			const res = await axios.post(
				constructURL(`api/fetch-vehicle-details-by-chatId`),
				body,
				config
			);
			return res.data.fetchVehicleDetailsByChatId.response;
		} catch (error) {
			// Handle the error, for example:
			if (error.response) {
				return rejectWithValue(error.response.data);
			} else if (error.request) {
				return rejectWithValue("No response received from the server");
			} else {
				return rejectWithValue("An error occurred while processing your request");
			}
		}
	}
);



export const appChatSlice = createSlice({
	name: "appChat",
	initialState: {
		loading: true,
		userChatList: [],
		vehicleDetailsByChatId: []
	},
	reducers: {
		setRouteCheck: (state, action) => {
			state.routeCheck = action.payload;
		},
	},
	extraReducers: (builder) => {
		builder.addCase(getUserChatList.fulfilled, (state, action) => {
			state.userChatList = action.payload;
		});
		builder.addCase(getVehicleDetailsByChatId.fulfilled, (state, action) => {
			state.vehicleDetailsByChatId = action.payload;
		});
	},
});

export const { setRouteCheck } = appChatSlice.actions;

export default appChatSlice.reducer;
