import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
const baseURL = import.meta.env.VITE_REACT_APP_BACKEND_URL;
// Create a function to construct the full URL using the baseURL
const constructURL = (endpoint) => {
  return `${baseURL}/${endpoint}`;
};

export const getDealersByLatLong = createAsyncThunk(
  "appLoan/getDealersByLatLong",
  async ({ number, lat, long, authToken }, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          Authorization: authToken,
        },
      };
      const res = await axios.get(
        constructURL(
          `api/dealer/get-dealers-by-lat-long?phone_number=${number}&lat=${lat}&long=${long}`
        ),
        config
      );
      return res.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const appLoanSlice = createSlice({
  name: "appLoan",
  initialState: {
    user: [],
    dealers: [],
    loading: true,
    otpSuccess: [],
    otpError: [],
    verifyotpSuccess: [],
    verifyotpError: [],
    token: null,
    mail: [],
    swipe: [],
    searchData: [],
    myGarageData: [],
  },
  reducers: {}, // You can define additional reducers here if needed
  extraReducers: (builder) => {
    builder.addCase(getDealersByLatLong.fulfilled, (state, action) => {
      state.dealers = action.payload;
    });
  },
});

export default appLoanSlice.reducer;
