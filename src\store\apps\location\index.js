import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

const baseURL = import.meta.env.VITE_BACKEND_URL;
// Create a function to construct the full URL using the baseURL
const constructURL = (endpoint) => {
  return `${baseURL}/${endpoint}`;
};

// Define asynchronous thunk action to fetch location data from Azure Maps Service
export const fetchLocationData = createAsyncThunk(
  "appLocation/fetchLocationData",
  async ({ zipcode, token }, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          Authorization: token,
        },
      };
      const requestData = {
        zipcode: zipcode,
      };
      const response = await axios.post(
        constructURL("api/fetch-zipcode-lat-long-azure-map"),
        requestData,
        config
      );
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const appLocationSlice = createSlice({
  name: "appLocation",
  initialState: {
    loading: false,
    locationData: null,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchLocationData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchLocationData.fulfilled, (state, action) => {
        state.locationData = action.payload;
        state.loading = false;
      })
      .addCase(fetchLocationData.rejected, (state, action) => {
        state.error = action.payload;
        state.loading = false;
      });
  },
});

export default appLocationSlice.reducer;
