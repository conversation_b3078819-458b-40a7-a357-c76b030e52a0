import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

const baseURL = import.meta.env.VITE_BACKEND_URL;
// Create a function to construct the full URL using the baseURL
const constructURL = (endpoint) => {
  return `${baseURL}/${endpoint}`;
};

export const getUser = createAsyncThunk(
  "appUsers/getUser",
  async ({ phone_number, authToken }, thunkAPI) => {
    try {
      const config = {
        headers: {
          Authorization: authToken,
        },
      };  
      const res = await axios.get(constructURL(`api/dealer/get-user-details?phone_number=${phone_number}`), config);
      return res.data;
      
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return thunkAPI.rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return thunkAPI.rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const sendOTP = createAsyncThunk(
  "appUsers/sendOTP",
  async ({ countryCode, phoneNumber }, thunkAPI) => {
    try {
      const requestData = {
        country_code: countryCode,
        phone_number: phoneNumber,
      };
      const response = await axios.post(constructURL('api/auth/send-otp/'), requestData);
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return thunkAPI.rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return thunkAPI.rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const verifyOTP = createAsyncThunk(
  "appUsers/verifyOTP",
  async ({ countryCode, phoneNumber, otp }, thunkAPI) => {
    try {
      const requestData = {
        country_code: countryCode,
        phone_number: phoneNumber,
        otp: otp,
      };

      const response = await axios.post(constructURL('api/auth/verify-otp/'), requestData);
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return thunkAPI.rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return thunkAPI.rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const setToken = createAsyncThunk(
  "appUsers/setToken",
  async (token, thunkAPI) => {
    try {
      // Perform any async operation here (e.g., making API calls)
      // For simplicity, just return the token as payload
      return token;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return thunkAPI.rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return thunkAPI.rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const getInterest = createAsyncThunk(
  "appUsers/getInterest",
  async ({ lenderId, creditScore, authToken }, thunkAPI) => {
    try {
      const config = {
        headers: {
          Authorization: authToken,
        },
      };

      const res = await axios.get(constructURL(`api/dealer/get-interest-rates?lender_id=${lenderId}&credit_score=${creditScore}`), config);

      return res.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return thunkAPI.rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return thunkAPI.rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const getloggedInUserDetails = createAsyncThunk(
  "appUsers/getloggedInUserDetails",
  async (token, thunkAPI) => {
    try {
      const config = {
        headers: {
          Authorization: token,
        },
      };  
      const res = await axios.get(constructURL("api/fetch-logged-in-user-details"), config);
      return res.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return thunkAPI.rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return thunkAPI.rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const updateUserLoanDetails = createAsyncThunk(
  "appUsers/updateUserLoanDetails",
  async ({ token, userLoanDetails }, thunkAPI) => {
    try {
      const config = {
        headers: {
          Authorization: token,
        },
      };  
      const res = await axios.post(
        constructURL(`api/update-user-loan-details`),
        userLoanDetails,
        config
      );
      return res.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return thunkAPI.rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return thunkAPI.rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const submitUserActivity = createAsyncThunk(
  "appUsers/submitUserActivity",
  async ({ componentId, vehicle_row_id, vin, token }, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          Authorization: token,
        },
      };
      const requestData = {
        component_id: componentId,
        vehicle_row_id: vehicle_row_id,
        vin: vin,
      };
      const response = await axios.post(
        constructURL("api/activity/store-user-activity-data"),
        requestData,
        config
      );
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const sendMsgToMsrOrAdvisor = createAsyncThunk(
  "appUsers/sendMsgToMsrOrAdvisor",
  async ({ userMsg, loan_id, req_type, token }, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          Authorization: token,
        },
      };
      const requestData = {
        req_type: req_type,
        user_msg: userMsg,
        loan_id: loan_id
      };
      const response = await axios.post(
        constructURL("api/send-message-to-msr"),
        requestData,
        config
      );
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const fetchMsrOrAdvisorMsgResponse = createAsyncThunk(
  "appUsers/fetchMsrOrAdvisorMsgResponse",
  async ({ token, loan_id, req_type }, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          Authorization: token,
        },
      };
      const requestData = {
        loan_id,
        req_type: req_type,
      };
      const response = await axios.post(
        constructURL("api/fetch-message-from-msr"),
        requestData,
        config
      );
      return response.data;
    } catch (error) {
      // Handle the error, for example:
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        return rejectWithValue("No response received from the server");
      } else {
        // Something happened in setting up the request that triggered an Error
        return rejectWithValue(
          "An error occurred while processing your request"
        );
      }
    }
  }
);

export const appUsersSlice = createSlice({
  name: "appUsers",
  initialState: {
    user: [],
    loading: true,
    otpSuccess: [],
    otpError: [],
    verifyotpSuccess: [],
    verifyotpError: [],
    token: null,
    mail: [],
    interest: [],
    loggedInUserData: [],
    userMsrOrAdvisorMsgSend: [],
    msrOrAdvisorMsgFetchData: [],
  },
  reducers: {}, // You can define additional reducers here if needed
  extraReducers: (builder) => {
    builder.addCase(setToken.fulfilled, (state, action) => {
      state.token = action.payload;
    });
    builder.addCase(getUser.fulfilled, (state, action) => {
      state.user = action.payload;
      state.loading = false;
    });
    builder.addCase(sendOTP.fulfilled, (state, action) => {
      state.otpSuccess = action.payload;
      state.otpError = action.error;
    });
    builder.addCase(verifyOTP.fulfilled, (state, action) => {
      state.verifyotpSuccess = action.payload;
      state.verifyotpError = null;
    });
    builder.addCase(getInterest.fulfilled, (state, action) => {
      state.interest = action.payload;
      state.loading = false;
    });
    builder.addCase(getloggedInUserDetails.fulfilled, (state, action) => {
      state.loggedInUserData = action.payload;
      state.loading = false;
    });
    builder.addCase(sendMsgToMsrOrAdvisor.fulfilled, (state, action) => {
      state.userMsrOrAdvisorMsgSend = action.payload;
      state.loading = false;
    });
    builder.addCase(fetchMsrOrAdvisorMsgResponse.fulfilled, (state, action) => {
      state.msrOrAdvisorMsgFetchData = action.payload;
      state.loading = false;
    });
  },
});

export const selectToken = (state) => state.appUsers.token; // Selector to get token from state
export const selectLoggedInUserData = (state) => state.user.loggedInUserData; // Selector to get loggedInUserData from state

export default appUsersSlice.reducer;
