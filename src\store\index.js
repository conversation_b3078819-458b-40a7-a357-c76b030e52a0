// src/store/index.js
import { configureStore } from "@reduxjs/toolkit";
import user from "./apps/user/index";
import car from "./apps/car/index";
import loan from "./apps/loan/index";
import location from "./apps/location/index"; 
import aiChat from "./apps/AIChat/index"

const store = configureStore({
  reducer: {
    user,
    car,
    loan,
    location,
    aiChat
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export default store; // Ensure the store is exported as default
