import { submitUserActivity } from "../store/apps/user";

/**
 * Utility function to call the component action API
 * @param {Object} dispatch - Redux dispatch function
 * @param {string} componentId - ID of the component
 * @returns {Promise<Object|null>} - API response payload or null if an error occurs
 */
export const callComponentActionApi = async (dispatch, componentId, vehicle_row_id = null, vin = null) => {
  const token = localStorage.getItem("token");
  try {
    // Dispatch the async action with the required parameters
    const response = await dispatch(submitUserActivity({ componentId, vehicle_row_id, vin, token }));
    // Return the payload of the response
    return response.payload;
  } catch (error) {
    // Log the error and return null
    console.error("Error while fetching component action API:", error);
    return null;
  }
};
