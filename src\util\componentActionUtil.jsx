import { callComponentAction<PERSON><PERSON> } from "./callComponenetAction<PERSON>pi";
/**
 * Calls the component action API without blocking the main logic.
 *
 * This function takes care of calling the component action API when a component
 * action is triggered. It catches any errors that occur during the API call and
 * logs them to the console.
 *
 * @param {Function} dispatch - Redux dispatch function
 * @param {String} component_id - ID of the component
 * @param {String} vehicle_row_id - Row ID of the vehicle (if applicable)
 * @returns {undefined}
 */
export const executeComponentAction = async (dispatch, component_id, vehicle_row_id) => {
    if (component_id) {
      // Run this function without waiting for it to complete
      (async () => {
        try {
          await callComponentActionApi(dispatch, component_id, vehicle_row_id);
        } catch (error) {
          console.error("Error while calling callComponentActionApi:", error);
        }
      })();
    }
  };
  