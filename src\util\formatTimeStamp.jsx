export   const formatTimeStamp = (timestamp, timeZone = '') => {
    if (!timestamp) return '';

    return new Date(timestamp).toLocaleString('en-US', {
      timeZone: timeZone || Intl.DateTimeFormat().resolvedOptions().timeZone,
      year: 'numeric',
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
      timeZoneName: "short", // Display timezone abbreviation (e.g., CST)
    });
  };