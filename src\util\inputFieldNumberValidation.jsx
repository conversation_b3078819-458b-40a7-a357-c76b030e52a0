export const validateInput = (value, maxDigits = 15, maxDecimalPoints = 2) => {
  const sanitizedValue = value.replace(/,/g, "").trim();
  const regex = new RegExp(`^\\d{0,${maxDigits}}(\\.\\d{0,${maxDecimalPoints}})?$`);

  if (regex.test(sanitizedValue)) {
    return {
      isValid: true,
      parsedValue: parseFloat(sanitizedValue),
    };
  } else {
    return {
      isValid: false,
      parsedValue: null,
    };
  }
};