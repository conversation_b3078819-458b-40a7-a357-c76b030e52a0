import { createRoot } from 'react-dom/client';
import React, { useState } from 'react';
import MemberBasedLoanOfferModal from '../components/modals/memberBasedLoanOfferModal.jsx';
import { updateUserLoanDetails } from '../store/apps/user';

const MemberBasedLoanOfferModalPortal = ({ dispatch, token, onClose, forceUnderProcess = false }) => {
  const [loading, setLoading] = useState(false);
  const [underProcess, setUnderProcess] = useState(forceUnderProcess);

  const handleAccept = async () => {
    const lafcuOfferURL = import.meta.env.VITE_LAFCU_ACCEPT_LOAN_MY_OFFERS; 
    // Open the LAFCU offer URL in a new tab
    window.open(lafcuOfferURL, '_blank', 'noopener,noreferrer');
    setLoading(true);
    try {
      const userLoanDetails = { is_loan_offer_accepted: 1 };
      const response = await dispatch(updateUserLoanDetails({ token, userLoanDetails }));
      // Check for is_loan_offer_accepted in response
      const isAccepted =
        response?.payload?.UserLoanDetails?.data?.is_loan_offer_accepted === true;
      if (isAccepted) {
        setUnderProcess(true);
        localStorage.setItem("is_loan_offer_accepted", isAccepted);
      }
    } catch (error) {
      console.error('Error storing accept loan offer flag at database:', error);
      return false;
    }
    setLoading(false);
  };

  return (
    <MemberBasedLoanOfferModal
      show={true}
      onHide={onClose}
      onAccept={handleAccept}
      loading={loading}
      underProcess={underProcess}
    />
  );
};

export const memberBasedLoanOfferActionUtil = (dispatch, token, forceUnderProcess = false) => {
  const modalContainer = document.createElement('div');
  modalContainer.id = 'member-based-loan-offer-modal-container';
  document.body.appendChild(modalContainer);

  const root = createRoot(modalContainer);

  const handleClose = () => {
    root.unmount();
    if (modalContainer && modalContainer.parentNode) {
      modalContainer.parentNode.removeChild(modalContainer);
    }
  };

  // Render the modal
  root.render(
    <MemberBasedLoanOfferModalPortal
      dispatch={dispatch}
      token={token}
      onClose={handleClose}
      forceUnderProcess={forceUnderProcess}
    />
  );
};
