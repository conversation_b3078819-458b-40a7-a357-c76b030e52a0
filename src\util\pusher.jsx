// src/utils/pusher.js
import Pusher from "pusher-js";

let pusherInstance = null;

export const getPusherInstance = () => {
  if (!pusherInstance) {
    pusherInstance = new Pusher(import.meta.env.VITE_PUSHER_KEY, {
      cluster: import.meta.env.VITE_PUSHER_CLUSTER,
    });
  }
  return pusherInstance;
};

export const subscribeToChannel = (user_id) => {
  const pusher = getPusherInstance();
  const channelName = `chat-channel-${user_id}`;
  let channel = pusher.channel(channelName);

  if (!channel) {
    channel = pusher.subscribe(channelName);
  } else {
  }

  return channel;
};

export const unsubscribeFromChannel = (user_id) => {
  const pusher = getPusherInstance();
  const channelName = `chat-channel-${user_id}`;
  pusher.unsubscribe(channelName);
};
