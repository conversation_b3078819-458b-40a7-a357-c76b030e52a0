/* eslint-disable no-undef */
const { execSync } = require('child_process');
const path = require('path');

// Import your existing function to generate the makeData file (if you prefer)
const fetchPageMetaDataPath = path.resolve(__dirname, 'src', 'scripts', 'fetchPageMetaData.cjs');
// Import your existing function to generate the makeData file (if you prefer)
const fetchComponentMetaDataPath = path.resolve(__dirname, 'src', 'scripts', 'fetchComponentMetaData.cjs');

async function loadEnvAndGenerateMakeData() {
  try {
    // Dynamically import the ES module for loading secrets
    const { loadSecretsIntoEnv } = await import('./src/fetchKeyVaulrSecrets.mjs');

    // Fetch secrets and load them into process.env
    await loadSecretsIntoEnv();

    // Now that the env variables are loaded, generate the pageMetaData.js file
    execSync(`node ${fetchPageMetaDataPath}`, { stdio: 'inherit' });
    console.log('page meta data imported successfully!');
     // Now that the env variables are loaded, generate the componentMetaData.js file
     execSync(`node ${fetchComponentMetaDataPath}`, { stdio: 'inherit' });
     console.log('component meta data imported successfully!');
  } catch (error) {
    console.error('Error importing data:', error);
    process.exit(1);  // Exit the process if an error occurs
  }
}

async function main() {
  try {
    // Load environment variables and generate make data
    await loadEnvAndGenerateMakeData();

    // Start the vite application
    execSync('vite', { stdio: 'inherit' });
  } catch (error) {
    console.error('Error starting application with secrets:', error);
    process.exit(1);  // Exit the process if an error occurs
  }
}

main();
